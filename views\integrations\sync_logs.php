<?php defined('BASEPATH') or exit('No direct script access allowed'); ?>
<?php init_head(); ?>
<div id="wrapper">
    <div class="content">
        <div class="row">
            <div class="col-md-12">
                <div class="panel_s">
                    <div class="panel-body">
                        <div class="row">
                            <div class="col-md-8">
                                <h4 class="no-margin">
                                    <i class="fa fa-list"></i> <?php echo $title; ?>
                                </h4>
                            </div>
                            <div class="col-md-4 text-right">
                                <div class="btn-group">
                                    <button type="button" class="btn btn-warning" onclick="clearOldLogs()">
                                        <i class="fa fa-trash"></i> <?php echo _l('clear_old_logs'); ?>
                                    </button>
                                    <button type="button" class="btn btn-info" onclick="exportLogs()">
                                        <i class="fa fa-download"></i> <?php echo _l('export_logs'); ?>
                                    </button>
                                    <a href="<?php echo admin_url('pos_inventory/integrations'); ?>" class="btn btn-default">
                                        <i class="fa fa-arrow-left"></i> <?php echo _l('back_to_integrations'); ?>
                                    </a>
                                </div>
                            </div>
                        </div>
                        <hr class="hr-panel-heading" />

                        <!-- Sync Statistics -->
                        <div class="row">
                            <div class="col-md-2">
                                <div class="panel panel-primary">
                                    <div class="panel-body text-center">
                                        <h3 class="no-margin text-primary" id="total-logs"><?php echo $statistics['total_logs']; ?></h3>
                                        <p class="text-muted"><?php echo _l('total_logs'); ?></p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-2">
                                <div class="panel panel-success">
                                    <div class="panel-body text-center">
                                        <h3 class="no-margin text-success" id="successful-logs"><?php echo $statistics['successful_logs']; ?></h3>
                                        <p class="text-muted"><?php echo _l('successful'); ?></p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-2">
                                <div class="panel panel-danger">
                                    <div class="panel-body text-center">
                                        <h3 class="no-margin text-danger" id="failed-logs"><?php echo $statistics['failed_logs']; ?></h3>
                                        <p class="text-muted"><?php echo _l('failed'); ?></p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-2">
                                <div class="panel panel-info">
                                    <div class="panel-body text-center">
                                        <h3 class="no-margin text-info" id="running-logs"><?php echo $statistics['running_logs']; ?></h3>
                                        <p class="text-muted"><?php echo _l('running'); ?></p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-2">
                                <div class="panel panel-warning">
                                    <div class="panel-body text-center">
                                        <h3 class="no-margin text-warning" id="today-logs"><?php echo $statistics['today_logs']; ?></h3>
                                        <p class="text-muted"><?php echo _l('today'); ?></p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-2">
                                <div class="panel panel-default">
                                    <div class="panel-body text-center">
                                        <h3 class="no-margin" id="avg-duration"><?php echo $statistics['avg_duration']; ?>s</h3>
                                        <p class="text-muted"><?php echo _l('avg_duration'); ?></p>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Filters -->
                        <div class="row">
                            <div class="col-md-12">
                                <div class="panel panel-default">
                                    <div class="panel-body">
                                        <div class="row">
                                            <div class="col-md-2">
                                                <div class="form-group">
                                                    <label for="filter-platform"><?php echo _l('platform'); ?></label>
                                                    <select class="form-control" id="filter-platform">
                                                        <option value=""><?php echo _l('all_platforms'); ?></option>
                                                        <option value="woocommerce">WooCommerce</option>
                                                        <option value="shopify">Shopify</option>
                                                    </select>
                                                </div>
                                            </div>
                                            <div class="col-md-2">
                                                <div class="form-group">
                                                    <label for="filter-sync-type"><?php echo _l('sync_type'); ?></label>
                                                    <select class="form-control" id="filter-sync-type">
                                                        <option value=""><?php echo _l('all_types'); ?></option>
                                                        <option value="products"><?php echo _l('products'); ?></option>
                                                        <option value="inventory"><?php echo _l('inventory'); ?></option>
                                                        <option value="orders"><?php echo _l('orders'); ?></option>
                                                        <option value="customers"><?php echo _l('customers'); ?></option>
                                                        <option value="categories"><?php echo _l('categories'); ?></option>
                                                    </select>
                                                </div>
                                            </div>
                                            <div class="col-md-2">
                                                <div class="form-group">
                                                    <label for="filter-status"><?php echo _l('status'); ?></label>
                                                    <select class="form-control" id="filter-status">
                                                        <option value=""><?php echo _l('all_statuses'); ?></option>
                                                        <option value="success"><?php echo _l('success'); ?></option>
                                                        <option value="failed"><?php echo _l('failed'); ?></option>
                                                        <option value="running"><?php echo _l('running'); ?></option>
                                                        <option value="cancelled"><?php echo _l('cancelled'); ?></option>
                                                    </select>
                                                </div>
                                            </div>
                                            <div class="col-md-2">
                                                <div class="form-group">
                                                    <label for="filter-date-from"><?php echo _l('date_from'); ?></label>
                                                    <input type="date" class="form-control" id="filter-date-from">
                                                </div>
                                            </div>
                                            <div class="col-md-2">
                                                <div class="form-group">
                                                    <label for="filter-date-to"><?php echo _l('date_to'); ?></label>
                                                    <input type="date" class="form-control" id="filter-date-to">
                                                </div>
                                            </div>
                                            <div class="col-md-2">
                                                <div class="form-group">
                                                    <label>&nbsp;</label>
                                                    <div>
                                                        <button type="button" class="btn btn-info btn-block" onclick="applyFilters()">
                                                            <i class="fa fa-filter"></i> <?php echo _l('filter'); ?>
                                                        </button>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="col-md-12">
                                                <button type="button" class="btn btn-default btn-sm" onclick="clearFilters()">
                                                    <i class="fa fa-times"></i> <?php echo _l('clear_filters'); ?>
                                                </button>
                                                <button type="button" class="btn btn-info btn-sm" onclick="refreshLogs()">
                                                    <i class="fa fa-refresh"></i> <?php echo _l('refresh'); ?>
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Sync Logs Table -->
                        <div class="row">
                            <div class="col-md-12">
                                <div class="panel panel-default">
                                    <div class="panel-body">
                                        <div class="table-responsive">
                                            <table class="table dt-table" id="sync-logs-table">
                                                <thead>
                                                    <tr>
                                                        <th><?php echo _l('id'); ?></th>
                                                        <th><?php echo _l('platform'); ?></th>
                                                        <th><?php echo _l('sync_type'); ?></th>
                                                        <th><?php echo _l('status'); ?></th>
                                                        <th><?php echo _l('records'); ?></th>
                                                        <th><?php echo _l('success_rate'); ?></th>
                                                        <th><?php echo _l('started_at'); ?></th>
                                                        <th><?php echo _l('duration'); ?></th>
                                                        <th><?php echo _l('actions'); ?></th>
                                                    </tr>
                                                </thead>
                                                <tbody></tbody>
                                            </table>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Sync Log Details Modal -->
<div class="modal fade" id="sync-log-details-modal" tabindex="-1" role="dialog">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
                <h4 class="modal-title"><?php echo _l('sync_log_details'); ?></h4>
            </div>
            <div class="modal-body">
                <div id="sync-log-details-content">
                    <!-- Content will be loaded here -->
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-default" data-dismiss="modal"><?php echo _l('close'); ?></button>
                <button type="button" class="btn btn-warning" id="retry-sync-btn" style="display: none;">
                    <i class="fa fa-refresh"></i> <?php echo _l('retry_sync'); ?>
                </button>
            </div>
        </div>
    </div>
</div>

<script>
$(document).ready(function() {
    // Initialize DataTable
    var syncLogsTable = $('#sync-logs-table').DataTable({
        "processing": true,
        "serverSide": true,
        "ajax": {
            "url": "<?php echo admin_url('pos_inventory/sync_logs_table'); ?>",
            "type": "POST",
            "data": function(d) {
                d.platform_filter = $('#filter-platform').val();
                d.sync_type_filter = $('#filter-sync-type').val();
                d.status_filter = $('#filter-status').val();
                d.date_from = $('#filter-date-from').val();
                d.date_to = $('#filter-date-to').val();
                <?php if (isset($integration_id)): ?>
                d.integration_id = <?php echo $integration_id; ?>;
                <?php endif; ?>
            }
        },
        "columns": [
            {"data": "id"},
            {"data": "platform"},
            {"data": "sync_type"},
            {"data": "status"},
            {"data": "records"},
            {"data": "success_rate"},
            {"data": "started_at"},
            {"data": "duration"},
            {"data": "actions", "orderable": false, "searchable": false}
        ],
        "order": [[0, "desc"]],
        "pageLength": 25
    });

    // Auto-refresh every 30 seconds for running syncs
    setInterval(function() {
        if ($('#running-logs').text() > 0) {
            syncLogsTable.ajax.reload(null, false);
        }
    }, 30000);
});

function applyFilters() {
    $('#sync-logs-table').DataTable().ajax.reload();
}

function clearFilters() {
    $('#filter-platform').val('');
    $('#filter-sync-type').val('');
    $('#filter-status').val('');
    $('#filter-date-from').val('');
    $('#filter-date-to').val('');
    applyFilters();
}

function refreshLogs() {
    $('#sync-logs-table').DataTable().ajax.reload();
}

function viewSyncDetails(logId) {
    $.post(admin_url + 'pos_inventory/get_sync_log_details', {
        log_id: logId
    }, function(response) {
        if (response.success) {
            $('#sync-log-details-content').html(response.html);
            
            // Show retry button if sync failed
            if (response.log.status === 'failed') {
                $('#retry-sync-btn').show().data('log-id', logId);
            } else {
                $('#retry-sync-btn').hide();
            }
            
            $('#sync-log-details-modal').modal('show');
        } else {
            alert_float('danger', response.message);
        }
    }, 'json');
}

function retrySyncLog(logId) {
    if (confirm('<?php echo _l('confirm_retry_sync'); ?>')) {
        $.post(admin_url + 'pos_inventory/retry_sync_log', {
            log_id: logId
        }, function(response) {
            if (response.success) {
                $('#sync-log-details-modal').modal('hide');
                $('#sync-logs-table').DataTable().ajax.reload();
                alert_float('success', response.message);
            } else {
                alert_float('danger', response.message);
            }
        }, 'json');
    }
}

function cancelSync(logId) {
    if (confirm('<?php echo _l('confirm_cancel_sync'); ?>')) {
        $.post(admin_url + 'pos_inventory/cancel_sync', {
            log_id: logId
        }, function(response) {
            if (response.success) {
                $('#sync-logs-table').DataTable().ajax.reload();
                alert_float('success', response.message);
            } else {
                alert_float('danger', response.message);
            }
        }, 'json');
    }
}

function deleteSyncLog(logId) {
    if (confirm('<?php echo _l('confirm_delete_sync_log'); ?>')) {
        $.post(admin_url + 'pos_inventory/delete_sync_log', {
            log_id: logId
        }, function(response) {
            if (response.success) {
                $('#sync-logs-table').DataTable().ajax.reload();
                alert_float('success', response.message);
            } else {
                alert_float('danger', response.message);
            }
        }, 'json');
    }
}

function clearOldLogs() {
    if (confirm('<?php echo _l('confirm_clear_old_logs'); ?>')) {
        $.post(admin_url + 'pos_inventory/clear_old_sync_logs', {}, function(response) {
            if (response.success) {
                $('#sync-logs-table').DataTable().ajax.reload();
                alert_float('success', response.message);
            } else {
                alert_float('danger', response.message);
            }
        }, 'json');
    }
}

function exportLogs() {
    var filters = {
        platform: $('#filter-platform').val(),
        sync_type: $('#filter-sync-type').val(),
        status: $('#filter-status').val(),
        date_from: $('#filter-date-from').val(),
        date_to: $('#filter-date-to').val()
    };
    
    var queryString = $.param(filters);
    window.open(admin_url + 'pos_inventory/export_sync_logs?' + queryString, '_blank');
}

// Retry sync button click handler
$('#retry-sync-btn').click(function() {
    var logId = $(this).data('log-id');
    retrySyncLog(logId);
});
</script>

<?php init_tail(); ?>
