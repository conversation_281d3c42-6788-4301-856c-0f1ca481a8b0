<?php defined('BASEPATH') or exit('No direct script access allowed'); ?>
<?php init_head(); ?>
<div id="wrapper">
    <div class="content">
        <div class="row">
            <div class="col-md-12">
                <div class="panel_s">
                    <div class="panel-body">
                        <div class="row">
                            <div class="col-md-8">
                                <h4 class="no-margin"><?php echo _l('purchase_orders'); ?></h4>
                            </div>
                            <div class="col-md-4 text-right">
                                <?php if (has_permission('pos_purchase_orders', '', 'create')): ?>
                                    <a href="<?php echo admin_url('pos_inventory/purchase_order'); ?>" class="btn btn-primary">
                                        <i class="fa fa-plus"></i> <?php echo _l('new_purchase_order'); ?>
                                    </a>
                                <?php endif; ?>
                            </div>
                        </div>
                        <hr class="hr-panel-heading" />

                        <!-- Statistics Cards -->
                        <div class="row">
                            <div class="col-md-3">
                                <div class="panel panel-default">
                                    <div class="panel-body text-center">
                                        <h3 class="no-margin text-primary" id="total-pos">0</h3>
                                        <p class="text-muted"><?php echo _l('total_purchase_orders'); ?></p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="panel panel-default">
                                    <div class="panel-body text-center">
                                        <h3 class="no-margin text-warning" id="pending-pos">0</h3>
                                        <p class="text-muted"><?php echo _l('pending_orders'); ?></p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="panel panel-default">
                                    <div class="panel-body text-center">
                                        <h3 class="no-margin text-success" id="received-pos">0</h3>
                                        <p class="text-muted"><?php echo _l('received_orders'); ?></p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="panel panel-default">
                                    <div class="panel-body text-center">
                                        <h3 class="no-margin text-danger" id="overdue-pos">0</h3>
                                        <p class="text-muted"><?php echo _l('overdue_orders'); ?></p>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Filters -->
                        <div class="row">
                            <div class="col-md-12">
                                <div class="panel panel-default">
                                    <div class="panel-heading">
                                        <h4 class="panel-title">
                                            <a data-toggle="collapse" href="#filters-panel">
                                                <i class="fa fa-filter"></i> <?php echo _l('filters'); ?>
                                            </a>
                                        </h4>
                                    </div>
                                    <div id="filters-panel" class="panel-collapse collapse">
                                        <div class="panel-body">
                                            <div class="row">
                                                <div class="col-md-3">
                                                    <div class="form-group">
                                                        <label for="filter_status"><?php echo _l('status'); ?></label>
                                                        <select id="filter_status" class="form-control selectpicker" multiple>
                                                            <option value="pending"><?php echo _l('pending'); ?></option>
                                                            <option value="ordered"><?php echo _l('ordered'); ?></option>
                                                            <option value="partially_received"><?php echo _l('partially_received'); ?></option>
                                                            <option value="received"><?php echo _l('received'); ?></option>
                                                            <option value="cancelled"><?php echo _l('cancelled'); ?></option>
                                                            <option value="backorder"><?php echo _l('backorder'); ?></option>
                                                        </select>
                                                    </div>
                                                </div>
                                                <div class="col-md-3">
                                                    <div class="form-group">
                                                        <label for="filter_supplier"><?php echo _l('supplier'); ?></label>
                                                        <select id="filter_supplier" class="form-control selectpicker" data-live-search="true">
                                                            <option value=""><?php echo _l('all_suppliers'); ?></option>
                                                            <?php foreach ($suppliers as $supplier): ?>
                                                                <option value="<?php echo $supplier['id']; ?>"><?php echo $supplier['name']; ?></option>
                                                            <?php endforeach; ?>
                                                        </select>
                                                    </div>
                                                </div>
                                                <div class="col-md-3">
                                                    <div class="form-group">
                                                        <label for="filter_date_from"><?php echo _l('date_from'); ?></label>
                                                        <input type="date" id="filter_date_from" class="form-control">
                                                    </div>
                                                </div>
                                                <div class="col-md-3">
                                                    <div class="form-group">
                                                        <label for="filter_date_to"><?php echo _l('date_to'); ?></label>
                                                        <input type="date" id="filter_date_to" class="form-control">
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="row">
                                                <div class="col-md-12">
                                                    <button type="button" class="btn btn-primary" onclick="applyFilters()">
                                                        <i class="fa fa-filter"></i> <?php echo _l('apply_filters'); ?>
                                                    </button>
                                                    <button type="button" class="btn btn-default" onclick="clearFilters()">
                                                        <i class="fa fa-refresh"></i> <?php echo _l('clear_filters'); ?>
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Purchase Orders Table -->
                        <div class="row">
                            <div class="col-md-12">
                                <div class="table-responsive">
                                    <table class="table dt-table table-purchase-orders" id="purchase-orders-table">
                                        <thead>
                                            <tr>
                                                <th><?php echo _l('po_number'); ?></th>
                                                <th><?php echo _l('supplier'); ?></th>
                                                <th><?php echo _l('order_date'); ?></th>
                                                <th><?php echo _l('expected_delivery'); ?></th>
                                                <th><?php echo _l('status'); ?></th>
                                                <th><?php echo _l('total'); ?></th>
                                                <th><?php echo _l('actions'); ?></th>
                                            </tr>
                                        </thead>
                                        <tbody></tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
$(document).ready(function() {
    initDataTable('.table-purchase-orders', admin_url + 'pos_inventory/purchase_orders_table', [6], [6]);
    loadStatistics();
    
    // Auto-refresh statistics every 30 seconds
    setInterval(loadStatistics, 30000);
});

function loadStatistics() {
    $.post(admin_url + 'pos_inventory/get_po_statistics', {
        date_from: $('#filter_date_from').val(),
        date_to: $('#filter_date_to').val()
    }, function(response) {
        if (response.success) {
            var stats = response.statistics;
            $('#total-pos').text(stats.total_orders || 0);
            $('#pending-pos').text(stats.pending_orders || 0);
            $('#received-pos').text(stats.received_orders || 0);
            $('#overdue-pos').text(stats.backorder_orders || 0);
        }
    }, 'json');
}

function applyFilters() {
    var table = $('#purchase-orders-table').DataTable();
    
    // Get filter values
    var status = $('#filter_status').val();
    var supplier = $('#filter_supplier').val();
    var dateFrom = $('#filter_date_from').val();
    var dateTo = $('#filter_date_to').val();
    
    // Apply filters to DataTable
    table.ajax.url(admin_url + 'pos_inventory/purchase_orders_table?' + 
        'status=' + (status ? status.join(',') : '') +
        '&supplier=' + (supplier || '') +
        '&date_from=' + (dateFrom || '') +
        '&date_to=' + (dateTo || '')
    ).load();
    
    // Update statistics with filters
    loadStatistics();
}

function clearFilters() {
    $('#filter_status').selectpicker('deselectAll');
    $('#filter_supplier').selectpicker('val', '');
    $('#filter_date_from').val('');
    $('#filter_date_to').val('');
    
    // Reset table
    var table = $('#purchase-orders-table').DataTable();
    table.ajax.url(admin_url + 'pos_inventory/purchase_orders_table').load();
    
    // Reset statistics
    loadStatistics();
}

function deletePO(id) {
    if (confirm('<?php echo _l('confirm_delete_purchase_order'); ?>')) {
        $.post(admin_url + 'pos_inventory/delete_purchase_order', {
            id: id
        }, function(response) {
            if (response.success) {
                alert_float('success', response.message);
                $('#purchase-orders-table').DataTable().ajax.reload();
                loadStatistics();
            } else {
                alert_float('danger', response.message);
            }
        }, 'json');
    }
}

function duplicatePO(id) {
    if (confirm('<?php echo _l('confirm_duplicate_purchase_order'); ?>')) {
        $.post(admin_url + 'pos_inventory/duplicate_purchase_order', {
            id: id
        }, function(response) {
            if (response.success) {
                alert_float('success', response.message);
                window.location.href = admin_url + 'pos_inventory/purchase_order/' + response.new_id;
            } else {
                alert_float('danger', response.message);
            }
        }, 'json');
    }
}

function quickReceive(id) {
    if (confirm('<?php echo _l('confirm_quick_receive_all'); ?>')) {
        $.post(admin_url + 'pos_inventory/quick_receive_all', {
            po_id: id
        }, function(response) {
            if (response.success) {
                alert_float('success', response.message);
                $('#purchase-orders-table').DataTable().ajax.reload();
                loadStatistics();
            } else {
                alert_float('danger', response.message);
            }
        }, 'json');
    }
}

function exportPOs() {
    var status = $('#filter_status').val();
    var supplier = $('#filter_supplier').val();
    var dateFrom = $('#filter_date_from').val();
    var dateTo = $('#filter_date_to').val();
    
    var url = admin_url + 'pos_inventory/export_purchase_orders?' +
        'status=' + (status ? status.join(',') : '') +
        '&supplier=' + (supplier || '') +
        '&date_from=' + (dateFrom || '') +
        '&date_to=' + (dateTo || '');
    
    window.open(url, '_blank');
}

// Bulk actions
function bulkAction(action) {
    var selectedIds = [];
    $('.individual-checkbox:checked').each(function() {
        selectedIds.push($(this).val());
    });
    
    if (selectedIds.length === 0) {
        alert_float('warning', '<?php echo _l('no_items_selected'); ?>');
        return;
    }
    
    var confirmMessage = '';
    switch (action) {
        case 'delete':
            confirmMessage = '<?php echo _l('confirm_bulk_delete_pos'); ?>';
            break;
        case 'cancel':
            confirmMessage = '<?php echo _l('confirm_bulk_cancel_pos'); ?>';
            break;
        case 'mark_ordered':
            confirmMessage = '<?php echo _l('confirm_bulk_mark_ordered'); ?>';
            break;
    }
    
    if (confirm(confirmMessage)) {
        $.post(admin_url + 'pos_inventory/bulk_po_action', {
            action: action,
            ids: selectedIds
        }, function(response) {
            if (response.success) {
                alert_float('success', response.message);
                $('#purchase-orders-table').DataTable().ajax.reload();
                loadStatistics();
            } else {
                alert_float('danger', response.message);
            }
        }, 'json');
    }
}
</script>

<?php init_tail(); ?>
