<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title><?php echo $title; ?></title>
    <style>
        body {
            font-family: 'Courier New', monospace;
            font-size: 12px;
            line-height: 1.4;
            margin: 0;
            padding: 20px;
            width: 300px;
            background: white;
        }
        
        .receipt-header {
            text-align: center;
            margin-bottom: 20px;
            border-bottom: 1px dashed #000;
            padding-bottom: 15px;
        }
        
        .company-name {
            font-size: 16px;
            font-weight: bold;
            margin-bottom: 5px;
        }
        
        .company-info {
            font-size: 10px;
            margin-bottom: 3px;
        }
        
        .receipt-title {
            font-size: 14px;
            font-weight: bold;
            margin: 10px 0;
        }
        
        .transaction-info {
            margin-bottom: 15px;
            font-size: 11px;
        }
        
        .transaction-info div {
            margin-bottom: 2px;
        }
        
        .items-table {
            width: 100%;
            margin-bottom: 15px;
            border-collapse: collapse;
        }
        
        .items-table th,
        .items-table td {
            padding: 3px 2px;
            text-align: left;
            font-size: 11px;
        }
        
        .items-table th {
            border-bottom: 1px solid #000;
            font-weight: bold;
        }
        
        .item-name {
            font-weight: bold;
        }
        
        .item-sku {
            font-size: 9px;
            color: #666;
        }
        
        .qty {
            text-align: center;
            width: 30px;
        }
        
        .price,
        .total {
            text-align: right;
            width: 60px;
        }
        
        .totals-section {
            border-top: 1px dashed #000;
            padding-top: 10px;
            margin-bottom: 15px;
        }
        
        .total-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 3px;
            font-size: 11px;
        }
        
        .total-row.grand-total {
            font-weight: bold;
            font-size: 13px;
            border-top: 1px solid #000;
            padding-top: 5px;
            margin-top: 5px;
        }
        
        .payment-info {
            margin-bottom: 15px;
            font-size: 11px;
        }
        
        .receipt-footer {
            text-align: center;
            border-top: 1px dashed #000;
            padding-top: 10px;
            font-size: 10px;
        }
        
        .thank-you {
            font-weight: bold;
            margin-bottom: 5px;
        }
        
        .notes {
            margin-top: 10px;
            padding: 5px;
            border: 1px solid #000;
            font-size: 10px;
        }
        
        @media print {
            body {
                margin: 0;
                padding: 10px;
            }
        }
    </style>
</head>
<body>
    <div class="receipt-container">
        <!-- Header -->
        <div class="receipt-header">
            <div class="company-name"><?php echo strtoupper($receipt_data['company']['name']); ?></div>
            <?php if (!empty($receipt_data['company']['address'])) { ?>
                <div class="company-info"><?php echo str_replace("\n", "<br>", $receipt_data['company']['address']); ?></div>
            <?php } ?>
            <?php if (!empty($receipt_data['company']['phone'])) { ?>
                <div class="company-info">Tel: <?php echo $receipt_data['company']['phone']; ?></div>
            <?php } ?>
            <?php if (!empty($receipt_data['company']['email'])) { ?>
                <div class="company-info">Email: <?php echo $receipt_data['company']['email']; ?></div>
            <?php } ?>
            <div class="receipt-title">RECEIPT</div>
        </div>

        <!-- Transaction Info -->
        <div class="transaction-info">
            <div><strong>Transaction #:</strong> <?php echo $receipt_data['transaction']['transaction_number']; ?></div>
            <div><strong>Date:</strong> <?php echo date('M d, Y H:i', strtotime($receipt_data['transaction']['transaction_date'])); ?></div>
            <div><strong>Cashier:</strong> <?php echo $receipt_data['transaction']['firstname'] . ' ' . $receipt_data['transaction']['lastname']; ?></div>
            <?php if (!empty($receipt_data['transaction']['customer_name'])) { ?>
                <div><strong>Customer:</strong> <?php echo $receipt_data['transaction']['customer_name']; ?></div>
            <?php } ?>
            <?php if (!empty($receipt_data['transaction']['location_name'])) { ?>
                <div><strong>Location:</strong> <?php echo $receipt_data['transaction']['location_name']; ?></div>
            <?php } ?>
        </div>

        <!-- Items -->
        <table class="items-table">
            <thead>
                <tr>
                    <th>Item</th>
                    <th class="qty">Qty</th>
                    <th class="price">Price</th>
                    <th class="total">Total</th>
                </tr>
            </thead>
            <tbody>
                <?php foreach ($receipt_data['items'] as $item) { ?>
                <tr>
                    <td>
                        <div class="item-name"><?php echo $item['product_name']; ?></div>
                        <?php if (!empty($item['sku'])) { ?>
                            <div class="item-sku">SKU: <?php echo $item['sku']; ?></div>
                        <?php } ?>
                    </td>
                    <td class="qty"><?php echo $item['quantity']; ?></td>
                    <td class="price"><?php echo number_format($item['unit_price'], 2); ?></td>
                    <td class="total"><?php echo number_format($item['total_price'], 2); ?></td>
                </tr>
                <?php } ?>
            </tbody>
        </table>

        <!-- Totals -->
        <div class="totals-section">
            <div class="total-row">
                <span>Subtotal:</span>
                <span><?php echo number_format($receipt_data['transaction']['subtotal'], 2); ?></span>
            </div>
            
            <?php if ($receipt_data['transaction']['discount_amount'] > 0) { ?>
            <div class="total-row">
                <span>Discount (<?php echo $receipt_data['transaction']['discount_value']; ?><?php echo $receipt_data['transaction']['discount_type'] == 'percentage' ? '%' : ''; ?>):</span>
                <span>-<?php echo number_format($receipt_data['transaction']['discount_amount'], 2); ?></span>
            </div>
            <?php } ?>
            
            <?php if ($receipt_data['transaction']['tax_amount'] > 0) { ?>
            <div class="total-row">
                <span>Tax (<?php echo $receipt_data['transaction']['tax_rate']; ?>%):</span>
                <span><?php echo number_format($receipt_data['transaction']['tax_amount'], 2); ?></span>
            </div>
            <?php } ?>
            
            <div class="total-row grand-total">
                <span>TOTAL:</span>
                <span><?php echo number_format($receipt_data['transaction']['total_amount'], 2); ?></span>
            </div>
        </div>

        <!-- Payment Info -->
        <?php if ($receipt_data['transaction']['payment_method'] == 'cash') { ?>
        <div class="payment-info">
            <div class="total-row">
                <span>Payment Method:</span>
                <span><?php echo ucfirst($receipt_data['transaction']['payment_method']); ?></span>
            </div>
            <div class="total-row">
                <span>Amount Received:</span>
                <span><?php echo number_format($receipt_data['transaction']['amount_received'], 2); ?></span>
            </div>
            <div class="total-row">
                <span>Change:</span>
                <span><?php echo number_format($receipt_data['transaction']['change_amount'], 2); ?></span>
            </div>
        </div>
        <?php } else { ?>
        <div class="payment-info">
            <div class="total-row">
                <span>Payment Method:</span>
                <span><?php echo ucfirst($receipt_data['transaction']['payment_method']); ?></span>
            </div>
        </div>
        <?php } ?>

        <!-- Notes -->
        <?php if (!empty($receipt_data['transaction']['notes'])) { ?>
        <div class="notes">
            <strong>Notes:</strong><br>
            <?php echo nl2br(htmlspecialchars($receipt_data['transaction']['notes'])); ?>
        </div>
        <?php } ?>

        <!-- Footer -->
        <div class="receipt-footer">
            <div class="thank-you">THANK YOU FOR YOUR BUSINESS!</div>
            <?php if (!empty($receipt_data['company']['website'])) { ?>
                <div>Visit us at: <?php echo $receipt_data['company']['website']; ?></div>
            <?php } ?>
            <div style="margin-top: 10px;">
                Generated: <?php echo date('M d, Y H:i'); ?>
            </div>
        </div>
    </div>

    <script>
        // Auto-print when page loads
        window.onload = function() {
            window.print();
        };
        
        // Close window after printing
        window.onafterprint = function() {
            window.close();
        };
    </script>
</body>
</html>
