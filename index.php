<?php

defined('BASEPATH') or exit('No direct script access allowed');

/**
 * POS Inventory Module Index
 * 
 * This file handles direct access to the module directory
 * and provides proper routing for the POS Inventory module.
 */

// Prevent direct access to module directory
if (!defined('BASEPATH')) {
    // If accessed directly, redirect to the main application
    $protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http';
    $host = $_SERVER['HTTP_HOST'];
    $base_url = $protocol . '://' . $host;
    
    // Try to determine the correct path to the main application
    $current_path = $_SERVER['REQUEST_URI'];
    $module_path = '/modules/pos_inventory';
    
    if (strpos($current_path, $module_path) !== false) {
        $main_path = str_replace($module_path, '', $current_path);
        $redirect_url = $base_url . $main_path;
    } else {
        // Fallback to root
        $redirect_url = $base_url . '/';
    }
    
    header('Location: ' . $redirect_url);
    exit;
}

// If we reach here, BASEPATH is defined, so we're in a CodeIgniter context
$CI = &get_instance();

// Check if user is logged in and has access
if (!is_staff_logged_in()) {
    // Redirect to login
    redirect(admin_url('authentication'));
}

// Check if user has permission to access POS Inventory
if (!has_permission('pos_inventory', '', 'view')) {
    // Show access denied
    access_denied('pos_inventory');
}

// Load the POS Inventory controller and redirect to dashboard
redirect(admin_url('pos_inventory'));
