<?php

// Simple activation script that bypasses complex checks
define('BASEPATH', true);

// Include Perfex CRM bootstrap
require_once('../../index.php');

echo "<h2>POS Inventory Module - Simple Activation</h2>\n";

$CI = &get_instance();

echo "<h3>Step 1: Load Installation Functions</h3>\n";
try {
    require_once('install/install.php');
    echo "✅ Installation functions loaded<br>\n";
} catch (Exception $e) {
    echo "❌ Failed to load installation functions: " . $e->getMessage() . "<br>\n";
    exit;
}

echo "<h3>Step 2: Create Database Tables</h3>\n";
try {
    pos_inventory_create_tables();
    echo "✅ Database tables created<br>\n";
} catch (Exception $e) {
    echo "❌ Failed to create tables: " . $e->getMessage() . "<br>\n";
    // Continue anyway as tables might already exist
}

echo "<h3>Step 3: Insert Default Data</h3>\n";
try {
    pos_inventory_insert_default_data();
    echo "✅ Default data inserted<br>\n";
} catch (Exception $e) {
    echo "❌ Failed to insert default data: " . $e->getMessage() . "<br>\n";
    // Continue anyway
}

echo "<h3>Step 4: Create Module Options</h3>\n";
try {
    pos_inventory_create_module_options();
    echo "✅ Module options created<br>\n";
} catch (Exception $e) {
    echo "❌ Failed to create module options: " . $e->getMessage() . "<br>\n";
    // Continue anyway
}

echo "<h3>Step 5: Register Module in Database</h3>\n";
try {
    // Check if module already exists
    $existing_module = $CI->db->get_where(db_prefix() . 'modules', ['module_name' => 'pos_inventory'])->row();
    if (!$existing_module) {
        $existing_module = $CI->db->get_where(db_prefix() . 'modules', ['system_name' => 'pos_inventory'])->row();
    }
    
    if ($existing_module) {
        // Update existing module
        $where_field = isset($existing_module->module_name) ? 'module_name' : 'system_name';
        $CI->db->where($where_field, 'pos_inventory');
        $CI->db->update(db_prefix() . 'modules', [
            'active' => 1,
            'installed_version' => '1.0.0'
        ]);
        echo "✅ Updated existing module record<br>\n";
    } else {
        // Insert new module record
        $CI->db->insert(db_prefix() . 'modules', [
            'module_name' => 'pos_inventory',
            'system_name' => 'pos_inventory',
            'active' => 1,
            'installed_version' => '1.0.0'
        ]);
        echo "✅ Created new module record<br>\n";
    }
} catch (Exception $e) {
    echo "❌ Failed to register module: " . $e->getMessage() . "<br>\n";
}

echo "<h3>Step 6: Create Migration Record</h3>\n";
try {
    pos_inventory_create_migration_record();
    echo "✅ Migration record created<br>\n";
} catch (Exception $e) {
    echo "❌ Failed to create migration record: " . $e->getMessage() . "<br>\n";
    // Continue anyway
}

echo "<h3>Step 7: Verify Installation</h3>\n";

// Check tables
$required_tables = [
    'pos_categories',
    'pos_products',
    'pos_locations',
    'pos_inventory'
];

$all_tables_exist = true;
foreach ($required_tables as $table) {
    $exists = $CI->db->table_exists(db_prefix() . $table);
    $status = $exists ? '✅' : '❌';
    echo "$status Table: " . db_prefix() . $table . "<br>\n";
    if (!$exists) $all_tables_exist = false;
}

// Check module status
$module = $CI->db->get_where(db_prefix() . 'modules', ['module_name' => 'pos_inventory'])->row();
if (!$module) {
    $module = $CI->db->get_where(db_prefix() . 'modules', ['system_name' => 'pos_inventory'])->row();
}

if ($module && $module->active) {
    echo "✅ Module is active in database<br>\n";
} else {
    echo "❌ Module is not active in database<br>\n";
}

echo "<h3>Activation Summary</h3>\n";
if ($all_tables_exist && $module && $module->active) {
    echo "🎉 <strong>Module activation completed successfully!</strong><br>\n";
    echo "<a href='../../admin/modules'>← Back to Modules</a><br>\n";
} else {
    echo "⚠️ <strong>Activation completed with some issues. Check the steps above.</strong><br>\n";
}

?>
