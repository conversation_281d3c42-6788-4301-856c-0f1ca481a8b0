<?php defined('BASEPATH') or exit('No direct script access allowed'); ?>
<?php init_head(); ?>
<div id="wrapper">
    <div class="content">
        <div class="row">
            <div class="col-md-12">
                <?php echo form_open($this->uri->uri_string(), ['id' => 'stock-adjustment-form']); ?>
                <div class="panel_s">
                    <div class="panel-body">
                        <div class="row">
                            <div class="col-md-8">
                                <h4 class="no-margin">
                                    <?php echo $title; ?>
                                </h4>
                            </div>
                            <div class="col-md-4 text-right">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fa fa-save"></i> <?php echo _l('save_adjustment'); ?>
                                </button>
                                <a href="<?php echo admin_url('pos_inventory/inventory'); ?>" class="btn btn-default">
                                    <?php echo _l('cancel'); ?>
                                </a>
                            </div>
                        </div>
                        <hr class="hr-panel-heading" />
                        
                        <div class="row">
                            <!-- Product Selection -->
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="product_id" class="control-label">
                                        <?php echo _l('product'); ?> <span class="text-danger">*</span>
                                    </label>
                                    <select class="form-control selectpicker" id="product_id" name="product_id" 
                                            data-live-search="true" data-size="10" required>
                                        <option value=""><?php echo _l('select_product'); ?></option>
                                        <?php foreach ($products as $product) { ?>
                                            <option value="<?php echo $product['id']; ?>" 
                                                    data-sku="<?php echo $product['sku']; ?>"
                                                    data-name="<?php echo htmlspecialchars($product['name']); ?>"
                                                    <?php echo (isset($_GET['product']) && $_GET['product'] == $product['id']) ? 'selected' : ''; ?>>
                                                <?php echo $product['name'] . ' (' . $product['sku'] . ')'; ?>
                                            </option>
                                        <?php } ?>
                                    </select>
                                </div>
                            </div>
                            
                            <!-- Location Selection -->
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="location_id" class="control-label">
                                        <?php echo _l('location'); ?> <span class="text-danger">*</span>
                                    </label>
                                    <select class="form-control selectpicker" id="location_id" name="location_id" required>
                                        <?php foreach ($locations as $location) { ?>
                                            <option value="<?php echo $location['id']; ?>"
                                                    <?php echo (isset($_GET['location']) && $_GET['location'] == $location['id']) ? 'selected' : ''; ?>
                                                    <?php echo $location['is_default'] ? 'selected' : ''; ?>>
                                                <?php echo $location['name']; ?>
                                            </option>
                                        <?php } ?>
                                    </select>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Current Stock Display -->
                        <div class="row" id="current-stock-info" style="display: none;">
                            <div class="col-md-12">
                                <div class="alert alert-info">
                                    <div class="row">
                                        <div class="col-md-3">
                                            <strong><?php echo _l('current_stock'); ?>:</strong>
                                            <span id="current-quantity">0</span>
                                        </div>
                                        <div class="col-md-3">
                                            <strong><?php echo _l('reserved'); ?>:</strong>
                                            <span id="reserved-quantity">0</span>
                                        </div>
                                        <div class="col-md-3">
                                            <strong><?php echo _l('available'); ?>:</strong>
                                            <span id="available-quantity">0</span>
                                        </div>
                                        <div class="col-md-3">
                                            <strong><?php echo _l('value'); ?>:</strong>
                                            <span id="stock-value">$0.00</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <!-- Adjustment Type -->
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label for="adjustment_type" class="control-label">
                                        <?php echo _l('adjustment_type'); ?> <span class="text-danger">*</span>
                                    </label>
                                    <select class="form-control" id="adjustment_type" name="adjustment_type" required>
                                        <?php foreach ($adjustment_types as $type => $label) { ?>
                                            <option value="<?php echo $type; ?>"><?php echo $label; ?></option>
                                        <?php } ?>
                                    </select>
                                </div>
                            </div>
                            
                            <!-- Quantity -->
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label for="quantity" class="control-label">
                                        <?php echo _l('quantity'); ?> <span class="text-danger">*</span>
                                    </label>
                                    <input type="number" class="form-control" id="quantity" name="quantity" 
                                           required min="0" step="1">
                                    <small class="text-muted" id="quantity-help"></small>
                                </div>
                            </div>
                            
                            <!-- New Stock Level Preview -->
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label class="control-label"><?php echo _l('new_stock_level'); ?></label>
                                    <div class="form-control-static">
                                        <span id="new-stock-level" class="label label-info">0</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <!-- Reason -->
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="reason" class="control-label">
                                        <?php echo _l('adjustment_reason'); ?> <span class="text-danger">*</span>
                                    </label>
                                    <select class="form-control" id="reason" name="reason" required>
                                        <option value=""><?php echo _l('select_reason'); ?></option>
                                        <option value="damaged"><?php echo _l('damaged_goods'); ?></option>
                                        <option value="expired"><?php echo _l('expired_goods'); ?></option>
                                        <option value="lost"><?php echo _l('lost_goods'); ?></option>
                                        <option value="found"><?php echo _l('found_goods'); ?></option>
                                        <option value="recount"><?php echo _l('stock_recount'); ?></option>
                                        <option value="correction"><?php echo _l('correction'); ?></option>
                                        <option value="other"><?php echo _l('other'); ?></option>
                                    </select>
                                </div>
                            </div>
                            
                            <!-- Batch Number (Optional) -->
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="batch_number" class="control-label"><?php echo _l('batch_number'); ?></label>
                                    <input type="text" class="form-control" id="batch_number" name="batch_number">
                                    <small class="text-muted"><?php echo _l('batch_number_help'); ?></small>
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <!-- Notes -->
                            <div class="col-md-12">
                                <div class="form-group">
                                    <label for="notes" class="control-label"><?php echo _l('adjustment_notes'); ?></label>
                                    <textarea class="form-control" id="notes" name="notes" rows="3" 
                                              placeholder="<?php echo _l('additional_notes_optional'); ?>"></textarea>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Expiry Date (for batch tracking) -->
                        <div class="row" id="expiry-section" style="display: none;">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="expiry_date" class="control-label"><?php echo _l('expiry_date'); ?></label>
                                    <input type="date" class="form-control" id="expiry_date" name="expiry_date">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="manufacturing_date" class="control-label"><?php echo _l('manufacturing_date'); ?></label>
                                    <input type="date" class="form-control" id="manufacturing_date" name="manufacturing_date">
                                </div>
                            </div>
                        </div>
                        
                        <!-- Confirmation -->
                        <div class="row">
                            <div class="col-md-12">
                                <div class="alert alert-warning">
                                    <div class="checkbox">
                                        <label>
                                            <input type="checkbox" id="confirm_adjustment" name="confirm_adjustment" required>
                                            <?php echo _l('confirm_stock_adjustment'); ?>
                                        </label>
                                    </div>
                                    <small><?php echo _l('stock_adjustment_warning'); ?></small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <?php echo form_close(); ?>
            </div>
        </div>
        
        <!-- Recent Stock Movements -->
        <div class="row">
            <div class="col-md-12">
                <div class="panel_s">
                    <div class="panel-body">
                        <h5><?php echo _l('recent_stock_movements'); ?></h5>
                        <div class="table-responsive">
                            <table class="table table-striped" id="recent-movements-table">
                                <thead>
                                    <tr>
                                        <th><?php echo _l('date'); ?></th>
                                        <th><?php echo _l('product'); ?></th>
                                        <th><?php echo _l('location'); ?></th>
                                        <th><?php echo _l('type'); ?></th>
                                        <th><?php echo _l('quantity'); ?></th>
                                        <th><?php echo _l('notes'); ?></th>
                                        <th><?php echo _l('user'); ?></th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <!-- Recent movements will be loaded via AJAX -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
$(document).ready(function() {
    // Load current stock when product/location changes
    $('#product_id, #location_id').change(function() {
        loadCurrentStock();
    });
    
    // Update new stock level when adjustment type or quantity changes
    $('#adjustment_type, #quantity').on('input change', function() {
        calculateNewStockLevel();
        updateQuantityHelp();
    });
    
    // Show/hide expiry section based on batch number
    $('#batch_number').on('input', function() {
        if ($(this).val().trim() !== '') {
            $('#expiry-section').show();
        } else {
            $('#expiry-section').hide();
        }
    });
    
    // Load recent movements
    loadRecentMovements();
    
    // Initial load if product is pre-selected
    if ($('#product_id').val()) {
        loadCurrentStock();
    }
});

function loadCurrentStock() {
    var productId = $('#product_id').val();
    var locationId = $('#location_id').val();
    
    if (productId && locationId) {
        $.post(admin_url + 'pos_inventory/get_product_stock', {
            product_id: productId,
            location_id: locationId
        }, function(data) {
            if (data.success) {
                $('#current-quantity').text(data.stock.quantity);
                $('#reserved-quantity').text(data.stock.reserved);
                $('#available-quantity').text(data.stock.available);
                $('#stock-value').text(data.stock.value);
                $('#current-stock-info').show();
                calculateNewStockLevel();
            }
        }, 'json');
    } else {
        $('#current-stock-info').hide();
    }
}

function calculateNewStockLevel() {
    var currentStock = parseInt($('#current-quantity').text()) || 0;
    var adjustmentType = $('#adjustment_type').val();
    var quantity = parseInt($('#quantity').val()) || 0;
    var newLevel = currentStock;
    
    switch (adjustmentType) {
        case 'increase':
            newLevel = currentStock + quantity;
            break;
        case 'decrease':
            newLevel = Math.max(0, currentStock - quantity);
            break;
        case 'set':
            newLevel = quantity;
            break;
    }
    
    $('#new-stock-level').text(newLevel);
    
    // Update label color based on stock level
    var $label = $('#new-stock-level');
    $label.removeClass('label-success label-warning label-danger label-info');
    
    if (newLevel === 0) {
        $label.addClass('label-danger');
    } else if (newLevel <= 5) { // Assuming low stock threshold
        $label.addClass('label-warning');
    } else {
        $label.addClass('label-success');
    }
}

function updateQuantityHelp() {
    var adjustmentType = $('#adjustment_type').val();
    var helpText = '';
    
    switch (adjustmentType) {
        case 'increase':
            helpText = '<?php echo _l('quantity_to_add'); ?>';
            break;
        case 'decrease':
            helpText = '<?php echo _l('quantity_to_remove'); ?>';
            break;
        case 'set':
            helpText = '<?php echo _l('new_total_quantity'); ?>';
            break;
    }
    
    $('#quantity-help').text(helpText);
}

function loadRecentMovements() {
    $('#recent-movements-table').DataTable({
        processing: true,
        serverSide: true,
        ajax: {
            url: admin_url + 'pos_inventory/recent_movements_table',
            type: 'POST'
        },
        columns: [
            { data: 'created_at' },
            { data: 'product_name' },
            { data: 'location_name' },
            { data: 'movement_type' },
            { data: 'quantity' },
            { data: 'notes' },
            { data: 'user_name' }
        ],
        order: [[0, 'desc']],
        pageLength: 10,
        responsive: true
    });
}
</script>

<?php init_tail(); ?>
