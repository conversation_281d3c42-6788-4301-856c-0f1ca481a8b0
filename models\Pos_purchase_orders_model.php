<?php

defined('BASEPATH') or exit('No direct script access allowed');

/**
 * POS Purchase Orders Model
 */
class Pos_purchase_orders_model extends App_Model
{
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Get all purchase orders
     */
    public function get_all($where = [])
    {
        $this->db->select('po.*, s.name as supplier_name, l.name as location_name');
        $this->db->from(db_prefix() . 'pos_purchase_orders po');
        $this->db->join(db_prefix() . 'pos_suppliers s', 's.id = po.supplier_id');
        $this->db->join(db_prefix() . 'pos_locations l', 'l.id = po.location_id');
        
        if (!empty($where)) {
            $this->db->where($where);
        }
        
        $this->db->order_by('po.created_at', 'DESC');
        
        return $this->db->get()->result_array();
    }

    /**
     * Get purchase order by ID
     */
    public function get($id)
    {
        $this->db->select('po.*, s.name as supplier_name, s.email as supplier_email, s.phone as supplier_phone, s.address as supplier_address, l.name as location_name');
        $this->db->from(db_prefix() . 'pos_purchase_orders po');
        $this->db->join(db_prefix() . 'pos_suppliers s', 's.id = po.supplier_id');
        $this->db->join(db_prefix() . 'pos_locations l', 'l.id = po.location_id');
        $this->db->where('po.id', $id);
        
        $purchase_order = $this->db->get()->row_array();
        
        if ($purchase_order) {
            // Get purchase order items
            $purchase_order['items'] = $this->get_purchase_order_items($id);
        }
        
        return $purchase_order;
    }

    /**
     * Get purchase order items
     */
    public function get_purchase_order_items($purchase_order_id)
    {
        $this->db->select('poi.*, p.name as product_name, p.sku, pv.name as variant_name');
        $this->db->from(db_prefix() . 'pos_purchase_order_items poi');
        $this->db->join(db_prefix() . 'pos_products p', 'p.id = poi.product_id');
        $this->db->join(db_prefix() . 'pos_product_variants pv', 'pv.id = poi.variant_id', 'left');
        $this->db->where('poi.purchase_order_id', $purchase_order_id);
        $this->db->order_by('poi.id', 'ASC');
        
        return $this->db->get()->result_array();
    }

    /**
     * Add new purchase order
     */
    public function add($data)
    {
        $this->db->trans_start();

        // Generate PO number
        $po_number = $this->generate_po_number();

        // Get default location if not provided
        if (!isset($data['location_id'])) {
            $this->load->model('pos_inventory_model');
            $default_location = $this->pos_inventory_model->get_default_location();
            $data['location_id'] = $default_location['id'];
        }

        // Calculate totals
        $subtotal = 0;
        if (isset($data['items']) && is_array($data['items'])) {
            foreach ($data['items'] as $item) {
                $subtotal += $item['quantity'] * $item['unit_cost'];
            }
        }

        $tax_amount = isset($data['tax_amount']) ? $data['tax_amount'] : 0;
        $shipping_cost = isset($data['shipping_cost']) ? $data['shipping_cost'] : 0;
        $total = $subtotal + $tax_amount + $shipping_cost;

        // Prepare purchase order data
        $po_data = [
            'po_number' => $po_number,
            'supplier_id' => $data['supplier_id'],
            'location_id' => $data['location_id'],
            'order_date' => isset($data['order_date']) ? $data['order_date'] : date('Y-m-d'),
            'expected_delivery' => isset($data['expected_delivery']) ? $data['expected_delivery'] : null,
            'status' => isset($data['status']) ? $data['status'] : 'pending',
            'subtotal' => $subtotal,
            'tax_amount' => $tax_amount,
            'shipping_cost' => $shipping_cost,
            'total' => $total,
            'notes' => isset($data['notes']) ? $data['notes'] : '',
            'internal_notes' => isset($data['internal_notes']) ? $data['internal_notes'] : '',
            'terms_conditions' => isset($data['terms_conditions']) ? $data['terms_conditions'] : '',
            'payment_terms' => isset($data['payment_terms']) ? $data['payment_terms'] : null,
            'shipping_method' => isset($data['shipping_method']) ? $data['shipping_method'] : null,
            'created_by' => get_staff_user_id()
        ];

        $this->db->insert(db_prefix() . 'pos_purchase_orders', $po_data);
        $po_id = $this->db->insert_id();

        // Add purchase order items
        if (isset($data['items']) && is_array($data['items'])) {
            foreach ($data['items'] as $item) {
                $this->add_purchase_order_item($po_id, $item);
            }
        }

        $this->db->trans_complete();

        if ($this->db->trans_status() === false) {
            return false;
        }

        return $po_id;
    }

    /**
     * Update purchase order
     */
    public function update($data, $id)
    {
        $this->db->trans_start();
        
        // Calculate totals
        $subtotal = 0;
        if (isset($data['items']) && is_array($data['items'])) {
            foreach ($data['items'] as $item) {
                $subtotal += $item['quantity'] * $item['unit_cost'];
            }
        }
        
        $tax_amount = isset($data['tax_amount']) ? $data['tax_amount'] : 0;
        $total = $subtotal + $tax_amount;
        
        // Prepare purchase order data
        $po_data = [
            'supplier_id' => $data['supplier_id'],
            'location_id' => $data['location_id'],
            'order_date' => isset($data['order_date']) ? $data['order_date'] : date('Y-m-d'),
            'expected_delivery' => isset($data['expected_delivery']) ? $data['expected_delivery'] : null,
            'status' => isset($data['status']) ? $data['status'] : 'pending',
            'subtotal' => $subtotal,
            'tax_amount' => $tax_amount,
            'total' => $total,
            'notes' => isset($data['notes']) ? $data['notes'] : ''
        ];
        
        $this->db->where('id', $id);
        $this->db->update(db_prefix() . 'pos_purchase_orders', $po_data);
        
        // Update purchase order items
        if (isset($data['items']) && is_array($data['items'])) {
            // Remove existing items
            $this->db->where('purchase_order_id', $id);
            $this->db->delete(db_prefix() . 'pos_purchase_order_items');
            
            // Add new items
            foreach ($data['items'] as $item) {
                $this->add_purchase_order_item($id, $item);
            }
        }
        
        $this->db->trans_complete();
        
        return $this->db->trans_status();
    }

    /**
     * Add purchase order item
     */
    private function add_purchase_order_item($po_id, $item_data)
    {
        $total_cost = $item_data['quantity'] * $item_data['unit_cost'];
        
        $data = [
            'purchase_order_id' => $po_id,
            'product_id' => $item_data['product_id'],
            'variant_id' => isset($item_data['variant_id']) ? $item_data['variant_id'] : null,
            'quantity' => $item_data['quantity'],
            'unit_cost' => $item_data['unit_cost'],
            'total_cost' => $total_cost,
            'batch_number' => isset($item_data['batch_number']) ? $item_data['batch_number'] : null,
            'expiry_date' => isset($item_data['expiry_date']) ? $item_data['expiry_date'] : null
        ];
        
        return $this->db->insert(db_prefix() . 'pos_purchase_order_items', $data);
    }

    /**
     * Receive stock for purchase order
     */
    public function receive_stock($po_id, $received_items, $delivery_date = null)
    {
        $this->db->trans_start();

        $po = $this->get($po_id);
        if (!$po) {
            return false;
        }

        $this->load->model('pos_inventory_model');

        $all_received = true;
        $partially_received = false;
        $has_backorders = false;

        foreach ($received_items as $item_id => $received_data) {
            $received_quantity = (int)$received_data['received_quantity'];

            if ($received_quantity <= 0) {
                continue;
            }

            // Get the purchase order item
            $this->db->where('id', $item_id);
            $po_item = $this->db->get(db_prefix() . 'pos_purchase_order_items')->row_array();

            if (!$po_item) {
                continue;
            }

            // Update received quantity
            $new_received = $po_item['received_quantity'] + $received_quantity;
            $this->db->where('id', $item_id);
            $this->db->update(db_prefix() . 'pos_purchase_order_items', ['received_quantity' => $new_received]);

            // Add to inventory
            $this->pos_inventory_model->adjust_stock([
                'product_id' => $po_item['product_id'],
                'variant_id' => $po_item['variant_id'],
                'location_id' => $po['location_id'],
                'adjustment_type' => 'increase',
                'quantity' => $received_quantity,
                'reason' => 'Purchase Order Receipt',
                'notes' => 'PO: ' . $po['po_number'],
                'batch_number' => isset($received_data['batch_number']) ? $received_data['batch_number'] : $po_item['batch_number']
            ]);

            // Check if item is fully received
            if ($new_received < $po_item['quantity']) {
                $all_received = false;
                $partially_received = true;

                // Create or update backorder
                $backorder_quantity = $po_item['quantity'] - $new_received;
                $this->create_or_update_backorder($po_id, $po_item['product_id'], $po_item['variant_id'],
                    $po_item['quantity'], $new_received, $backorder_quantity,
                    isset($received_data['expected_date']) ? $received_data['expected_date'] : null);
                $has_backorders = true;
            } else {
                // Mark any existing backorder as fulfilled
                $this->update_backorder_status($po_id, $po_item['product_id'], $po_item['variant_id'], 'fulfilled');
            }
        }

        // Update PO status
        $new_status = 'received';
        if ($has_backorders) {
            $new_status = $partially_received ? 'partially_received' : 'backorder';
        }

        $update_data = [
            'status' => $new_status
        ];

        if ($delivery_date) {
            $update_data['delivery_date'] = $delivery_date;
        } elseif ($all_received) {
            $update_data['delivery_date'] = date('Y-m-d');
        }

        $this->db->where('id', $po_id);
        $this->db->update(db_prefix() . 'pos_purchase_orders', $update_data);

        $this->db->trans_complete();

        return $this->db->trans_status();
    }

    /**
     * Get suppliers
     */
    public function get_suppliers()
    {
        $this->db->where('status', 1);
        $this->db->order_by('name', 'ASC');
        return $this->db->get(db_prefix() . 'pos_suppliers')->result_array();
    }

    /**
     * Add supplier
     */
    public function add_supplier($data)
    {
        $supplier_data = [
            'name' => $data['name'],
            'company' => isset($data['company']) ? $data['company'] : null,
            'email' => isset($data['email']) ? $data['email'] : null,
            'phone' => isset($data['phone']) ? $data['phone'] : null,
            'mobile' => isset($data['mobile']) ? $data['mobile'] : null,
            'website' => isset($data['website']) ? $data['website'] : null,
            'address' => isset($data['address']) ? $data['address'] : null,
            'city' => isset($data['city']) ? $data['city'] : null,
            'state' => isset($data['state']) ? $data['state'] : null,
            'zip' => isset($data['zip']) ? $data['zip'] : null,
            'country' => isset($data['country']) ? $data['country'] : null,
            'contact_person' => isset($data['contact_person']) ? $data['contact_person'] : null,
            'contact_email' => isset($data['contact_email']) ? $data['contact_email'] : null,
            'contact_phone' => isset($data['contact_phone']) ? $data['contact_phone'] : null,
            'tax_number' => isset($data['tax_number']) ? $data['tax_number'] : null,
            'payment_terms' => isset($data['payment_terms']) ? $data['payment_terms'] : 30,
            'credit_limit' => isset($data['credit_limit']) ? $data['credit_limit'] : null,
            'currency' => isset($data['currency']) ? $data['currency'] : null,
            'lead_time_days' => isset($data['lead_time_days']) ? $data['lead_time_days'] : null,
            'minimum_order_amount' => isset($data['minimum_order_amount']) ? $data['minimum_order_amount'] : null,
            'notes' => isset($data['notes']) ? $data['notes'] : null,
            'description' => isset($data['description']) ? $data['description'] : null,
            'discount_percentage' => isset($data['discount_percentage']) ? $data['discount_percentage'] : 0,
            'perfex_contact_id' => isset($data['perfex_contact_id']) ? $data['perfex_contact_id'] : null,
            'auto_sync_contact' => isset($data['auto_sync_contact']) ? $data['auto_sync_contact'] : 0,
            'status' => isset($data['status']) ? $data['status'] : 1,
            'created_at' => date('Y-m-d H:i:s')
        ];

        $this->db->insert(db_prefix() . 'pos_suppliers', $supplier_data);
        return $this->db->insert_id();
    }

    /**
     * Update supplier
     */
    public function update_supplier($data, $id)
    {
        $supplier_data = [];

        // Only update fields that are provided
        $fields = [
            'name', 'company', 'email', 'phone', 'mobile', 'website', 'address',
            'city', 'state', 'zip', 'country', 'contact_person', 'contact_email',
            'contact_phone', 'tax_number', 'payment_terms', 'credit_limit',
            'currency', 'lead_time_days', 'minimum_order_amount', 'notes',
            'perfex_contact_id', 'status', 'description', 'discount_percentage',
            'auto_sync_contact'
        ];

        foreach ($fields as $field) {
            if (isset($data[$field])) {
                $supplier_data[$field] = $data[$field];
            }
        }

        if (empty($supplier_data)) {
            return true; // Nothing to update
        }

        $this->db->where('id', $id);
        return $this->db->update(db_prefix() . 'pos_suppliers', $supplier_data);
    }

    /**
     * Get supplier by ID
     */
    public function get_supplier($id)
    {
        $this->db->where('id', $id);
        return $this->db->get(db_prefix() . 'pos_suppliers')->row_array();
    }

    /**
     * Generate PO number
     */
    private function generate_po_number()
    {
        $prefix = 'PO';
        $date = date('Ymd');
        
        // Get last PO number for today
        $this->db->select('po_number');
        $this->db->from(db_prefix() . 'pos_purchase_orders');
        $this->db->like('po_number', $prefix . $date, 'after');
        $this->db->order_by('id', 'DESC');
        $this->db->limit(1);
        
        $last_po = $this->db->get()->row();
        
        if ($last_po) {
            $last_number = (int)substr($last_po->po_number, -4);
            $new_number = $last_number + 1;
        } else {
            $new_number = 1;
        }
        
        return $prefix . $date . str_pad($new_number, 4, '0', STR_PAD_LEFT);
    }

    /**
     * Get pending purchase orders count
     */
    public function get_pending_count()
    {
        $this->db->where('status', 'pending');
        return $this->db->count_all_results(db_prefix() . 'pos_purchase_orders');
    }

    /**
     * Delete purchase order
     */
    public function delete($id)
    {
        $this->db->trans_start();

        // Delete purchase order documents
        $this->db->where('purchase_order_id', $id);
        $this->db->delete(db_prefix() . 'pos_purchase_order_documents');

        // Delete backorders
        $this->db->where('purchase_order_id', $id);
        $this->db->delete(db_prefix() . 'pos_backorders');

        // Delete purchase order items
        $this->db->where('purchase_order_id', $id);
        $this->db->delete(db_prefix() . 'pos_purchase_order_items');

        // Delete purchase order
        $this->db->where('id', $id);
        $this->db->delete(db_prefix() . 'pos_purchase_orders');

        $this->db->trans_complete();

        return $this->db->trans_status();
    }

    /**
     * Create or update backorder
     */
    public function create_or_update_backorder($po_id, $product_id, $variant_id, $quantity_ordered, $quantity_received, $quantity_backordered, $expected_date = null)
    {
        // Check if backorder already exists
        $this->db->where('purchase_order_id', $po_id);
        $this->db->where('product_id', $product_id);
        if ($variant_id) {
            $this->db->where('variant_id', $variant_id);
        } else {
            $this->db->where('variant_id IS NULL');
        }
        $existing = $this->db->get(db_prefix() . 'pos_backorders')->row_array();

        $backorder_data = [
            'quantity_ordered' => $quantity_ordered,
            'quantity_received' => $quantity_received,
            'quantity_backordered' => $quantity_backordered,
            'expected_date' => $expected_date,
            'status' => $quantity_backordered > 0 ? 'pending' : 'fulfilled'
        ];

        if ($existing) {
            $this->db->where('id', $existing['id']);
            return $this->db->update(db_prefix() . 'pos_backorders', $backorder_data);
        } else {
            $backorder_data['purchase_order_id'] = $po_id;
            $backorder_data['product_id'] = $product_id;
            $backorder_data['variant_id'] = $variant_id;

            $this->db->insert(db_prefix() . 'pos_backorders', $backorder_data);
            return $this->db->insert_id();
        }
    }

    /**
     * Update backorder status
     */
    public function update_backorder_status($po_id, $product_id, $variant_id, $status)
    {
        $this->db->where('purchase_order_id', $po_id);
        $this->db->where('product_id', $product_id);
        if ($variant_id) {
            $this->db->where('variant_id', $variant_id);
        } else {
            $this->db->where('variant_id IS NULL');
        }

        return $this->db->update(db_prefix() . 'pos_backorders', ['status' => $status]);
    }

    /**
     * Get backorders for purchase order
     */
    public function get_backorders($po_id)
    {
        $this->db->select('b.*, p.name as product_name, p.sku, pv.name as variant_name');
        $this->db->from(db_prefix() . 'pos_backorders b');
        $this->db->join(db_prefix() . 'pos_products p', 'p.id = b.product_id');
        $this->db->join(db_prefix() . 'pos_product_variants pv', 'pv.id = b.variant_id', 'left');
        $this->db->where('b.purchase_order_id', $po_id);
        $this->db->order_by('b.created_at', 'ASC');

        return $this->db->get()->result_array();
    }

    /**
     * Get all backorders
     */
    public function get_all_backorders($where = [])
    {
        $this->db->select('b.*, p.name as product_name, p.sku, pv.name as variant_name, po.po_number, s.name as supplier_name');
        $this->db->from(db_prefix() . 'pos_backorders b');
        $this->db->join(db_prefix() . 'pos_products p', 'p.id = b.product_id');
        $this->db->join(db_prefix() . 'pos_product_variants pv', 'pv.id = b.variant_id', 'left');
        $this->db->join(db_prefix() . 'pos_purchase_orders po', 'po.id = b.purchase_order_id');
        $this->db->join(db_prefix() . 'pos_suppliers s', 's.id = po.supplier_id');

        if (!empty($where)) {
            $this->db->where($where);
        }

        $this->db->order_by('b.expected_date', 'ASC');

        return $this->db->get()->result_array();
    }

    /**
     * Add document to purchase order
     */
    public function add_document($po_id, $document_data)
    {
        $data = [
            'purchase_order_id' => $po_id,
            'document_name' => $document_data['document_name'],
            'original_filename' => $document_data['original_filename'],
            'file_path' => $document_data['file_path'],
            'file_size' => $document_data['file_size'],
            'mime_type' => $document_data['mime_type'],
            'document_type' => isset($document_data['document_type']) ? $document_data['document_type'] : 'other',
            'description' => isset($document_data['description']) ? $document_data['description'] : '',
            'uploaded_by' => get_staff_user_id()
        ];

        $this->db->insert(db_prefix() . 'pos_purchase_order_documents', $data);
        return $this->db->insert_id();
    }

    /**
     * Get documents for purchase order
     */
    public function get_documents($po_id)
    {
        $this->db->select('d.*, s.firstname, s.lastname');
        $this->db->from(db_prefix() . 'pos_purchase_order_documents d');
        $this->db->join(db_prefix() . 'staff s', 's.staffid = d.uploaded_by');
        $this->db->where('d.purchase_order_id', $po_id);
        $this->db->order_by('d.created_at', 'DESC');

        return $this->db->get()->result_array();
    }

    /**
     * Delete document
     */
    public function delete_document($document_id)
    {
        $this->db->where('id', $document_id);
        return $this->db->delete(db_prefix() . 'pos_purchase_order_documents');
    }

    /**
     * Convert PO to invoice
     */
    public function convert_to_invoice($po_id)
    {
        $po = $this->get($po_id);
        if (!$po || $po['status'] !== 'received') {
            return false;
        }

        // Check if already converted
        if ($po['invoice_id']) {
            return $po['invoice_id'];
        }

        $this->load->model('invoices_model');

        // Get supplier as contact
        $supplier = $this->get_supplier($po['supplier_id']);
        $client_id = null;

        if ($supplier['perfex_contact_id']) {
            $client_id = $supplier['perfex_contact_id'];
        } else {
            // Create new client from supplier data
            $this->load->model('clients_model');
            $client_data = [
                'company' => $supplier['company'] ?: $supplier['name'],
                'vat' => $supplier['tax_number'],
                'phonenumber' => $supplier['phone'],
                'website' => $supplier['website'],
                'address' => $supplier['address'],
                'city' => $supplier['city'],
                'state' => $supplier['state'],
                'zip' => $supplier['zip'],
                'country' => $supplier['country'] ? get_country_id($supplier['country']) : null,
                'billing_street' => $supplier['address'],
                'billing_city' => $supplier['city'],
                'billing_state' => $supplier['state'],
                'billing_zip' => $supplier['zip'],
                'billing_country' => $supplier['country'] ? get_country_id($supplier['country']) : null
            ];

            $client_id = $this->clients_model->add($client_data);

            // Update supplier with client ID
            $this->db->where('id', $po['supplier_id']);
            $this->db->update(db_prefix() . 'pos_suppliers', ['perfex_contact_id' => $client_id]);
        }

        // Create invoice
        $invoice_data = [
            'clientid' => $client_id,
            'number' => $this->invoices_model->get_next_invoice_number(),
            'date' => date('Y-m-d'),
            'duedate' => date('Y-m-d', strtotime('+30 days')),
            'currency' => $supplier['currency'] ?: get_base_currency()->id,
            'subtotal' => $po['subtotal'],
            'total' => $po['total'],
            'tax' => $po['tax_amount'],
            'terms' => $po['terms_conditions'],
            'clientnote' => 'Invoice generated from Purchase Order: ' . $po['po_number'],
            'adminnote' => 'Auto-generated from PO #' . $po['po_number']
        ];

        $invoice_id = $this->invoices_model->add($invoice_data);

        if ($invoice_id) {
            // Add invoice items
            foreach ($po['items'] as $item) {
                $this->db->insert(db_prefix() . 'itemable', [
                    'description' => $item['product_name'] . ($item['variant_name'] ? ' - ' . $item['variant_name'] : ''),
                    'long_description' => '',
                    'qty' => $item['received_quantity'],
                    'rate' => $item['unit_cost'],
                    'unit' => '',
                    'item_order' => 1,
                    'rel_id' => $invoice_id,
                    'rel_type' => 'invoice'
                ]);
            }

            // Update PO with invoice ID
            $this->db->where('id', $po_id);
            $this->db->update(db_prefix() . 'pos_purchase_orders', ['invoice_id' => $invoice_id]);

            return $invoice_id;
        }

        return false;
    }

    /**
     * Get purchase orders by status
     */
    public function get_by_status($status)
    {
        return $this->get_all(['po.status' => $status]);
    }

    /**
     * Get overdue purchase orders
     */
    public function get_overdue()
    {
        $this->db->where('expected_delivery <', date('Y-m-d'));
        $this->db->where_in('status', ['pending', 'ordered', 'partially_received']);
        return $this->get_all();
    }

    /**
     * Update tracking number
     */
    public function update_tracking($po_id, $tracking_number, $shipping_method = null)
    {
        $update_data = ['tracking_number' => $tracking_number];
        if ($shipping_method) {
            $update_data['shipping_method'] = $shipping_method;
        }

        $this->db->where('id', $po_id);
        return $this->db->update(db_prefix() . 'pos_purchase_orders', $update_data);
    }

    /**
     * Get supplier purchase history
     */
    public function get_supplier_purchase_history($supplier_id, $limit = 10)
    {
        $this->db->where('supplier_id', $supplier_id);
        $this->db->order_by('created_at', 'DESC');
        $this->db->limit($limit);
        return $this->get_all();
    }

    /**
     * Get purchase order statistics
     */
    public function get_statistics($date_from = null, $date_to = null)
    {
        if ($date_from) {
            $this->db->where('order_date >=', $date_from);
        }
        if ($date_to) {
            $this->db->where('order_date <=', $date_to);
        }

        $this->db->select('
            COUNT(*) as total_orders,
            SUM(CASE WHEN status = "pending" THEN 1 ELSE 0 END) as pending_orders,
            SUM(CASE WHEN status = "received" THEN 1 ELSE 0 END) as received_orders,
            SUM(CASE WHEN status = "backorder" THEN 1 ELSE 0 END) as backorder_orders,
            SUM(total) as total_value,
            AVG(total) as average_order_value
        ');

        return $this->db->get(db_prefix() . 'pos_purchase_orders')->row_array();
    }

    /**
     * Get supplier statistics
     */
    public function get_supplier_statistics()
    {
        // Total suppliers
        $total_suppliers = $this->db->count_all_results(db_prefix() . 'pos_suppliers');

        // Active suppliers
        $this->db->where('status', 1);
        $active_suppliers = $this->db->count_all_results(db_prefix() . 'pos_suppliers');

        // Perfex linked suppliers
        $this->db->where('perfex_contact_id IS NOT NULL');
        $linked_suppliers = $this->db->count_all_results(db_prefix() . 'pos_suppliers');

        // Recent orders (last 30 days)
        $this->db->where('order_date >=', date('Y-m-d', strtotime('-30 days')));
        $recent_orders = $this->db->count_all_results(db_prefix() . 'pos_purchase_orders');

        return [
            'total_suppliers' => $total_suppliers,
            'active_suppliers' => $active_suppliers,
            'linked_suppliers' => $linked_suppliers,
            'recent_orders' => $recent_orders
        ];
    }

    /**
     * Get detailed supplier statistics
     */
    public function get_supplier_statistics_detail($supplier_id)
    {
        // Total orders
        $this->db->where('supplier_id', $supplier_id);
        $total_orders = $this->db->count_all_results(db_prefix() . 'pos_purchase_orders');

        // Total amount
        $this->db->select('SUM(total) as total_amount');
        $this->db->where('supplier_id', $supplier_id);
        $total_amount = $this->db->get(db_prefix() . 'pos_purchase_orders')->row()->total_amount ?: 0;

        // Average order value
        $avg_order_value = $total_orders > 0 ? $total_amount / $total_orders : 0;

        // Last order date
        $this->db->select('order_date');
        $this->db->where('supplier_id', $supplier_id);
        $this->db->order_by('order_date', 'DESC');
        $this->db->limit(1);
        $last_order = $this->db->get(db_prefix() . 'pos_purchase_orders')->row();
        $last_order_date = $last_order ? _d($last_order->order_date) : '-';

        // Products count
        $this->db->select('COUNT(DISTINCT poi.product_id) as products_count');
        $this->db->from(db_prefix() . 'pos_purchase_order_items poi');
        $this->db->join(db_prefix() . 'pos_purchase_orders po', 'po.id = poi.purchase_order_id');
        $this->db->where('po.supplier_id', $supplier_id);
        $products_count = $this->db->get()->row()->products_count ?: 0;

        return [
            'total_orders' => $total_orders,
            'total_amount' => app_format_money($total_amount, get_base_currency()),
            'avg_order_value' => app_format_money($avg_order_value, get_base_currency()),
            'last_order' => $last_order_date,
            'products_count' => $products_count
        ];
    }

    /**
     * Delete supplier
     */
    public function delete_supplier($id)
    {
        $this->db->where('id', $id);
        return $this->db->delete(db_prefix() . 'pos_suppliers');
    }

    /**
     * Get suppliers for export
     */
    public function get_suppliers_for_export($status = null, $perfex_linked = null, $country = null, $search = null)
    {
        $this->db->select('s.*, c.short_name as country_name, cur.name as currency_name');
        $this->db->from(db_prefix() . 'pos_suppliers s');
        $this->db->join(db_prefix() . 'countries c', 'c.country_id = s.country', 'left');
        $this->db->join(db_prefix() . 'currencies cur', 'cur.id = s.currency', 'left');

        if ($status !== null && $status !== '') {
            $this->db->where('s.status', $status);
        }

        if ($perfex_linked !== null && $perfex_linked !== '') {
            if ($perfex_linked == '1') {
                $this->db->where('s.perfex_contact_id IS NOT NULL');
            } else {
                $this->db->where('s.perfex_contact_id IS NULL');
            }
        }

        if ($country) {
            $this->db->where('s.country', $country);
        }

        if ($search) {
            $this->db->group_start();
            $this->db->like('s.name', $search);
            $this->db->or_like('s.company', $search);
            $this->db->or_like('s.email', $search);
            $this->db->or_like('s.phone', $search);
            $this->db->group_end();
        }

        $this->db->order_by('s.name', 'ASC');
        return $this->db->get()->result_array();
    }

    /**
     * Import suppliers from CSV
     */
    public function import_suppliers_from_csv($file_path, $update_existing = false, $auto_link_perfex = false)
    {
        if (!file_exists($file_path)) {
            return ['success' => false, 'message' => _l('file_not_found')];
        }

        $handle = fopen($file_path, 'r');
        if (!$handle) {
            return ['success' => false, 'message' => _l('cannot_read_file')];
        }

        $header = fgetcsv($handle);
        if (!$header) {
            fclose($handle);
            return ['success' => false, 'message' => _l('invalid_csv_format')];
        }

        $imported = 0;
        $updated = 0;
        $errors = [];
        $row_number = 1;

        while (($row = fgetcsv($handle)) !== false) {
            $row_number++;

            if (count($row) !== count($header)) {
                $errors[] = "Row $row_number: Column count mismatch";
                continue;
            }

            $data = array_combine($header, $row);

            // Validate required fields
            if (empty($data['name'])) {
                $errors[] = "Row $row_number: Name is required";
                continue;
            }

            // Check if supplier exists (by email or name)
            $existing = null;
            if (!empty($data['email'])) {
                $this->db->where('email', $data['email']);
                $existing = $this->db->get(db_prefix() . 'pos_suppliers')->row_array();
            }

            if (!$existing && !empty($data['name'])) {
                $this->db->where('name', $data['name']);
                $existing = $this->db->get(db_prefix() . 'pos_suppliers')->row_array();
            }

            try {
                if ($existing) {
                    if ($update_existing) {
                        $this->update_supplier($data, $existing['id']);
                        $updated++;
                    }
                } else {
                    // Auto-link to Perfex contact if enabled
                    if ($auto_link_perfex && !empty($data['email'])) {
                        $this->load->model('clients_model');
                        $this->db->where('email', $data['email']);
                        $perfex_contact = $this->db->get(db_prefix() . 'contacts')->row_array();
                        if ($perfex_contact) {
                            $data['perfex_contact_id'] = $perfex_contact['userid'];
                        }
                    }

                    $this->add_supplier($data);
                    $imported++;
                }
            } catch (Exception $e) {
                $errors[] = "Row $row_number: " . $e->getMessage();
            }
        }

        fclose($handle);

        $message = sprintf(_l('import_completed'), $imported, $updated);
        if (!empty($errors)) {
            $message .= ' ' . sprintf(_l('import_errors'), count($errors));
        }

        return [
            'success' => true,
            'message' => $message,
            'imported' => $imported,
            'updated' => $updated,
            'errors' => $errors
        ];
    }
}
