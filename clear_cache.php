<?php

echo "<h2>PHP Cache Clearing</h2>\n";

// Clear OPcache if available
if (function_exists('opcache_reset')) {
    if (opcache_reset()) {
        echo "✅ OPcache cleared successfully<br>\n";
    } else {
        echo "❌ Failed to clear OPcache<br>\n";
    }
} else {
    echo "ℹ️ OPcache not available<br>\n";
}

// Clear APCu cache if available
if (function_exists('apcu_clear_cache')) {
    if (apcu_clear_cache()) {
        echo "✅ APCu cache cleared successfully<br>\n";
    } else {
        echo "❌ Failed to clear APCu cache<br>\n";
    }
} else {
    echo "ℹ️ APCu not available<br>\n";
}

// Show PHP info about caching
echo "<h3>PHP Caching Information:</h3>\n";
echo "OPcache enabled: " . (ini_get('opcache.enable') ? 'Yes' : 'No') . "<br>\n";
echo "OPcache CLI enabled: " . (ini_get('opcache.enable_cli') ? 'Yes' : 'No') . "<br>\n";

if (function_exists('opcache_get_status')) {
    $status = opcache_get_status();
    if ($status) {
        echo "OPcache status: " . ($status['opcache_enabled'] ? 'Enabled' : 'Disabled') . "<br>\n";
        echo "Cache full: " . ($status['cache_full'] ? 'Yes' : 'No') . "<br>\n";
    }
}

echo "<br>Cache clearing completed. Please try the activation again.<br>\n";

?>
