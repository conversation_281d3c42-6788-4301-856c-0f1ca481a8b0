<?php

defined('BASEPATH') or exit('No direct script access allowed');

/*
Module Name: Advanced POS & Central Inventory
Description: Advanced Point of Sale and Central Inventory Management System with WooCommerce & Shopify Integration
Version: 1.0.0
Requires at least: 2.3.*
Author: Professional Developer
Author URI: https://codecanyon.net/
*/

define('POS_INVENTORY_MODULE_NAME', 'pos_inventory');
define('POS_INVENTORY_MODULE_UPLOAD_FOLDER', module_dir_path(POS_INVENTORY_MODULE_NAME, 'uploads'));
define('POS_INVENTORY_PRODUCT_IMAGES_FOLDER', module_dir_path(POS_INVENTORY_MODULE_NAME, 'uploads/products/'));
define('POS_INVENTORY_CATEGORY_IMAGES_FOLDER', module_dir_path(POS_INVENTORY_MODULE_NAME, 'uploads/categories/'));
define('POS_INVENTORY_SUPPLIER_IMAGES_FOLDER', module_dir_path(POS_INVENTORY_MODULE_NAME, 'uploads/suppliers/'));
define('POS_INVENTORY_RECEIPTS_FOLDER', module_dir_path(POS_INVENTORY_MODULE_NAME, 'uploads/receipts/'));
define('POS_INVENTORY_PATH', 'modules/pos_inventory/uploads/');
define('VERSION_POS_INVENTORY', 100); // Version 1.0.0

// Load version management
if (file_exists(__DIR__ . '/config/version.php')) {
    require_once(__DIR__ . '/config/version.php');
}

// Load module configuration
if (file_exists(__DIR__ . '/config/module_config.php')) {
    require_once(__DIR__ . '/config/module_config.php');
}

// Load logging helper
if (file_exists(__DIR__ . '/helpers/pos_inventory_logging_helper.php')) {
    require_once(__DIR__ . '/helpers/pos_inventory_logging_helper.php');
}

// Register hooks
hooks()->add_action('admin_init', 'pos_inventory_permissions');
hooks()->add_action('app_admin_head', 'pos_inventory_add_head_components');
hooks()->add_action('app_admin_footer', 'pos_inventory_load_js');
hooks()->add_action('admin_init', 'pos_inventory_module_init_menu_items');
hooks()->add_action('before_render_dashboard', 'pos_inventory_add_dashboard_widgets');

// Register activation and deactivation hooks
register_activation_hook(POS_INVENTORY_MODULE_NAME, 'pos_inventory_module_activation_hook');
register_deactivation_hook(POS_INVENTORY_MODULE_NAME, 'pos_inventory_module_deactivation_hook');

function pos_inventory_module_activation_hook()
{
    $CI = &get_instance();

    try {
        // Log activation start with version info
        $version = defined('POS_INVENTORY_VERSION') ? POS_INVENTORY_VERSION : '1.0.0';
        pos_inventory_log_info('Starting POS Inventory module activation - Version: ' . $version);

        // Minimal system requirements check - skip version detection for now
        pos_inventory_log_info('POS Inventory: Starting activation process');

        // Basic PHP version check only
        if (version_compare(PHP_VERSION, '7.4.0', '<')) {
            throw new Exception('PHP version 7.4.0 or higher is required. Current version: ' . PHP_VERSION);
        }

        // Use safe logging if available
        if (function_exists('pos_inventory_log_info')) {
            pos_inventory_log_info('PHP version check passed: ' . PHP_VERSION);
            pos_inventory_log_info('Proceeding with activation');
        }

        // Load database and dbforge
        $CI->load->database();
        $CI->load->dbforge();

        // Check if this is an upgrade (if version management is loaded)
        $installed_version = function_exists('pos_inventory_get_installed_version') ? pos_inventory_get_installed_version() : null;
        $is_upgrade = $installed_version && function_exists('pos_inventory_needs_upgrade') && pos_inventory_needs_upgrade();

        if ($is_upgrade) {
            if (function_exists('pos_inventory_log_info')) {
                pos_inventory_log_info("Upgrading POS Inventory from version $installed_version to " . $version);
            }
            if (function_exists('pos_inventory_handle_upgrade')) {
                pos_inventory_handle_upgrade($installed_version);
            }
        } else {
            if (function_exists('pos_inventory_log_info')) {
                pos_inventory_log_info('Installing POS Inventory module for the first time');
            }

            // Install base POS & Inventory module with enhanced error handling
            if (file_exists(__DIR__ . '/install/install.php')) {
                require_once(__DIR__ . '/install/install.php');
            } else {
                throw new Exception('POS Inventory: install.php file not found');
            }
        }

        // Verify critical tables were created
        pos_inventory_verify_critical_tables();

        // Verify all table structures and add missing fields
        pos_inventory_verify_all_table_structures();

        // Ensure migration record exists to prevent "Upgrade Database" button
        pos_inventory_ensure_migration_record();

        // Update module version in database
        pos_inventory_update_module_version();

        // Log success using safe logging
        pos_inventory_log_info('POS Inventory module activated successfully - Version: ' . $version);

    } catch (Exception $e) {
        // Log error using safe logging
        pos_inventory_log_error('POS Inventory module activation failed: ' . $e->getMessage());
        throw $e; // Re-throw to prevent activation if critical error
    }
}

/**
 * Handle module upgrade
 */
function pos_inventory_handle_upgrade($from_version)
{
    $upgrade_steps = pos_inventory_get_upgrade_path($from_version);

    if (empty($upgrade_steps)) {
        pos_inventory_log_info('No upgrade steps required');
        return;
    }

    foreach ($upgrade_steps as $step) {
        pos_inventory_log_info("Applying upgrade step: {$step['version']} - {$step['description']}");

        // Run migration files if they exist
        foreach ($step['migration_files'] as $migration_file) {
            $migration_path = __DIR__ . '/migrations/' . $migration_file;
            if (file_exists($migration_path)) {
                try {
                    require_once($migration_path);
                    pos_inventory_log_info("Applied migration: $migration_file");
                } catch (Exception $e) {
                    pos_inventory_log_error("Failed to apply migration $migration_file: " . $e->getMessage());
                    throw $e;
                }
            }
        }
    }
}

/**
 * Verify critical tables exist
 */
function pos_inventory_verify_critical_tables()
{
    $CI = &get_instance();

    $critical_tables = [
        'pos_categories',
        'pos_products',
        'pos_product_variants',
        'pos_locations',
        'pos_inventory',
        'pos_stock_movements',
        'pos_suppliers',
        'pos_purchase_orders',
        'pos_purchase_order_items',
        'pos_transactions',
        'pos_transaction_items',
        'pos_held_orders',
        'pos_integrations',
        'pos_sync_logs'
    ];

    $missing_tables = [];
    foreach ($critical_tables as $table) {
        if (!$CI->db->table_exists(db_prefix() . $table)) {
            $missing_tables[] = $table;
        }
    }

    if (!empty($missing_tables)) {
        throw new Exception('Critical tables missing after installation: ' . implode(', ', $missing_tables));
    }

    if (function_exists('pos_inventory_log_info')) {
        pos_inventory_log_info('All critical tables verified');
    }
}

/**
 * Update module version in database
 */
function pos_inventory_update_module_version()
{
    $CI = &get_instance();

    try {
        // Update in modules table
        $module_exists = $CI->db->get_where(db_prefix() . 'modules', ['module_name' => 'pos_inventory'])->row();
        if (!$module_exists) {
            $module_exists = $CI->db->get_where(db_prefix() . 'modules', ['system_name' => 'pos_inventory'])->row();
        }

        if ($module_exists) {
            $where_field = isset($module_exists->module_name) ? 'module_name' : 'system_name';
            $CI->db->where($where_field, 'pos_inventory');
            $CI->db->update(db_prefix() . 'modules', [
                'installed_version' => pos_inventory_get_version_code()
            ]);
            if (function_exists('pos_inventory_log_info')) {
                pos_inventory_log_info('Updated module version in modules table');
            }
        }
    } catch (Exception $e) {
        if (function_exists('pos_inventory_log_error')) {
            pos_inventory_log_error('Failed to update module version: ' . $e->getMessage());
        }
    }
}

/**
 * Ensure migration record exists to prevent "Upgrade Database" button
 * This is critical for CodeCanyon marketplace - no manual fixes allowed
 */
function pos_inventory_ensure_migration_record()
{
    $CI = &get_instance();

    try {
        // Check if migration record exists
        $migration_exists = $CI->db->get_where(db_prefix() . 'migrations', ['version' => VERSION_POS_INVENTORY])->row();

        if (!$migration_exists) {
            // Check available columns in migrations table
            $migration_fields = $CI->db->field_data(db_prefix() . 'migrations');
            $available_columns = [];
            foreach ($migration_fields as $field) {
                $available_columns[] = $field->name;
            }

            // Start with only the version (which should always exist)
            $migration_data = [];

            // Add columns only if they exist in the table
            if (in_array('version', $available_columns)) {
                $migration_data['version'] = VERSION_POS_INVENTORY;
            }

            if (in_array('time', $available_columns)) {
                $migration_data['time'] = time();
            }

            if (in_array('timestamp', $available_columns)) {
                $migration_data['timestamp'] = time();
            }

            if (in_array('batch', $available_columns)) {
                $migration_data['batch'] = 1;
            }

            if (in_array('class', $available_columns)) {
                $migration_data['class'] = 'Migration_Version_' . VERSION_POS_INVENTORY;
            }

            if (in_array('namespace', $available_columns)) {
                $migration_data['namespace'] = '';
            }

            if (in_array('group', $available_columns)) {
                $migration_data['`group`'] = 'pos_inventory';
            }

            // Only insert if we have at least the version
            if (!empty($migration_data)) {
                try {
                    $CI->db->insert(db_prefix() . 'migrations', $migration_data);

                    // Only log if not in CLI mode
                    if (!is_cli() && function_exists('pos_inventory_log_info')) {
                        pos_inventory_log_info('Added migration record for version ' . VERSION_POS_INVENTORY);
                    }
                } catch (Exception $insert_e) {
                    // If migration record creation fails, log but don't fail activation
                    if (!is_cli() && function_exists('pos_inventory_log_error')) {
                        pos_inventory_log_error('Failed to ensure migration record: ' . $insert_e->getMessage());
                    }
                }
            }
        }

        // Also ensure module version is correct in modules table
        $module_exists = $CI->db->get_where(db_prefix() . 'modules', ['module_name' => 'pos_inventory'])->row();
        if (!$module_exists) {
            $module_exists = $CI->db->get_where(db_prefix() . 'modules', ['system_name' => 'pos_inventory'])->row();
        }

        if ($module_exists && $module_exists->installed_version != VERSION_POS_INVENTORY) {
            // Update module version
            if (isset($module_exists->module_name)) {
                $CI->db->where('module_name', 'pos_inventory');
            } else {
                $CI->db->where('system_name', 'pos_inventory');
            }

            $CI->db->update(db_prefix() . 'modules', [
                'installed_version' => VERSION_POS_INVENTORY
            ]);

            if (function_exists('pos_inventory_log_info')) {
                pos_inventory_log_info('Updated module version to ' . VERSION_POS_INVENTORY);
            }
        }

    } catch (Exception $e) {
        if (function_exists('pos_inventory_log_error')) {
            pos_inventory_log_error('Failed to ensure migration record: ' . $e->getMessage());
        }
    }
}

/**
 * Deactivation hook for cleanup
 */
function pos_inventory_module_deactivation_hook()
{
    // Clean up any temporary files or settings if needed
    if (function_exists('pos_inventory_log_info')) {
        pos_inventory_log_info('POS Inventory module deactivated');
    }
}

/**
 * Verify all table structures and add missing fields
 */
function pos_inventory_verify_all_table_structures()
{
    $CI = &get_instance();

    // Define all table structures with required fields
    $table_structures = [
        'pos_categories' => [
            'id' => 'int(11) NOT NULL AUTO_INCREMENT',
            'name' => 'varchar(191) NOT NULL',
            'description' => 'text',
            'parent_id' => 'int(11) DEFAULT NULL',
            'image' => 'varchar(255) DEFAULT NULL',
            'status' => 'tinyint(1) NOT NULL DEFAULT 1',
            'sort_order' => 'int(11) DEFAULT 0',
            'created_at' => 'datetime NOT NULL DEFAULT CURRENT_TIMESTAMP',
            'updated_at' => 'datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP'
        ],
        'pos_products' => [
            'id' => 'int(11) NOT NULL AUTO_INCREMENT',
            'name' => 'varchar(191) NOT NULL',
            'description' => 'text',
            'sku' => 'varchar(100) NOT NULL',
            'barcode' => 'varchar(100) DEFAULT NULL',
            'category_id' => 'int(11) DEFAULT NULL',
            'unit' => 'varchar(50) DEFAULT NULL',
            'cost_price' => 'decimal(15,2) DEFAULT 0.00',
            'sale_price' => 'decimal(15,2) NOT NULL DEFAULT 0.00',
            'weight' => 'decimal(8,2) DEFAULT NULL',
            'dimensions' => 'varchar(100) DEFAULT NULL',
            'image' => 'varchar(255) DEFAULT NULL',
            'track_inventory' => 'tinyint(1) NOT NULL DEFAULT 1',
            'allow_backorders' => 'tinyint(1) NOT NULL DEFAULT 0',
            'low_stock_threshold' => 'int(11) DEFAULT 5',
            'status' => 'tinyint(1) NOT NULL DEFAULT 1',
            'created_by' => 'int(11) NOT NULL',
            'created_at' => 'datetime NOT NULL DEFAULT CURRENT_TIMESTAMP',
            'updated_at' => 'datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP'
        ]
    ];

    foreach ($table_structures as $table_name => $fields) {
        pos_inventory_verify_table_structure($table_name, $fields);
    }
}

/**
 * Verify individual table structure and add missing fields
 */
function pos_inventory_verify_table_structure($table_name, $required_fields)
{
    $CI = &get_instance();
    $full_table_name = db_prefix() . $table_name;

    // Check if table exists
    if (!$CI->db->table_exists($full_table_name)) {
        if (function_exists('pos_inventory_log_warning')) {
            pos_inventory_log_warning("Table $full_table_name does not exist - should be created by install.php");
        }
        return;
    }

    // Get existing fields
    $existing_fields = $CI->db->list_fields($full_table_name);

    // Check each required field
    foreach ($required_fields as $field_name => $field_definition) {
        if (!in_array($field_name, $existing_fields)) {
            try {
                // Add missing field
                $sql = "ALTER TABLE `$full_table_name` ADD `$field_name` $field_definition";

                // Handle AUTO_INCREMENT specially
                if (strpos($field_definition, 'AUTO_INCREMENT') !== false) {
                    $sql .= ' PRIMARY KEY';
                }

                $CI->db->query($sql);
                if (function_exists('pos_inventory_log_info')) {
                    pos_inventory_log_info("Added missing field '$field_name' to table '$table_name'");
                }

            } catch (Exception $e) {
                if (function_exists('pos_inventory_log_error')) {
                    pos_inventory_log_error("Failed to add field '$field_name' to table '$table_name': " . $e->getMessage());
                }
            }
        }
    }

    if (function_exists('pos_inventory_log_info')) {
        pos_inventory_log_info("Verified table structure for '$table_name'");
    }
}

/**
 * Create missing critical tables
 */
function pos_inventory_create_missing_tables($missing_tables)
{
    $CI = &get_instance();

    if (function_exists('pos_inventory_log_warning')) {
        pos_inventory_log_warning('Missing tables detected: ' . implode(', ', $missing_tables));
        pos_inventory_log_info('These tables should be created by install.php during module activation');
    }

    // The install.php should handle table creation
    // This function serves as a fallback and logging mechanism
    foreach ($missing_tables as $table) {
        if (function_exists('pos_inventory_log_error')) {
            pos_inventory_log_error("Critical table missing: $table - check install.php");
        }
    }
}

// Register language files
register_language_files(POS_INVENTORY_MODULE_NAME, [POS_INVENTORY_MODULE_NAME]);

$CI = & get_instance();
$CI->load->helper(POS_INVENTORY_MODULE_NAME . '/pos_inventory');

// Load the logging helper to ensure safe logging functions are available
if (file_exists(__DIR__ . '/helpers/pos_inventory_logging_helper.php')) {
    require_once(__DIR__ . '/helpers/pos_inventory_logging_helper.php');
}

/**
 * Init POS inventory module menu items
 * @return null
 */
function pos_inventory_module_init_menu_items()
{
    $CI = &get_instance();

    if(has_permission('pos_dashboard','','view') ||
       has_permission('pos_products','','view') ||
       has_permission('pos_categories','','view') ||
       has_permission('pos_inventory','','view') ||
       has_permission('pos_transactions','','view') ||
       has_permission('pos_purchase_orders','','view') ||
       has_permission('pos_suppliers','','view') ||
       has_permission('pos_reports','','view')) {

        $CI->app_menu->add_sidebar_menu_item('pos_inventory', [
            'name'     => _l('pos_inventory'),
            'icon'     => 'fa fa-shopping-cart',
            'position' => 6,
        ]);
    }

    if(has_permission('pos_dashboard','','view')){
        $CI->app_menu->add_sidebar_children_item('pos_inventory', [
            'slug'     => 'pos_dashboard',
            'name'     => _l('pos_dashboard'),
            'icon'     => 'fa fa-dashboard',
            'href'     => admin_url('pos_inventory/dashboard'),
            'position' => 1,
        ]);
    }

    if(has_permission('pos_interface','','view')){
        $CI->app_menu->add_sidebar_children_item('pos_inventory', [
            'slug'     => 'pos_interface',
            'name'     => _l('pos_interface'),
            'icon'     => 'fa fa-calculator',
            'href'     => admin_url('pos_inventory/pos'),
            'position' => 2,
        ]);
    }

    if(has_permission('pos_products','','view')){
        $CI->app_menu->add_sidebar_children_item('pos_inventory', [
            'slug'     => 'pos_products',
            'name'     => _l('products'),
            'icon'     => 'fa fa-cube',
            'href'     => admin_url('pos_inventory/products'),
            'position' => 3,
        ]);
    }

    if(has_permission('pos_categories','','view')){
        $CI->app_menu->add_sidebar_children_item('pos_inventory', [
            'slug'     => 'pos_categories',
            'name'     => _l('categories'),
            'icon'     => 'fa fa-tags',
            'href'     => admin_url('pos_inventory/categories'),
            'position' => 4,
        ]);
    }

    if(has_permission('pos_inventory','','view')){
        $CI->app_menu->add_sidebar_children_item('pos_inventory', [
            'slug'     => 'pos_inventory_management',
            'name'     => _l('inventory_management'),
            'icon'     => 'fa fa-cubes',
            'href'     => admin_url('pos_inventory/inventory'),
            'position' => 5,
        ]);
    }

    if(has_permission('pos_suppliers','','view')){
        $CI->app_menu->add_sidebar_children_item('pos_inventory', [
            'slug'     => 'pos_suppliers',
            'name'     => _l('suppliers'),
            'icon'     => 'fa fa-truck',
            'href'     => admin_url('pos_inventory/suppliers'),
            'position' => 6,
        ]);
    }

    if(has_permission('pos_purchase_orders','','view')){
        $CI->app_menu->add_sidebar_children_item('pos_inventory', [
            'slug'     => 'pos_purchase_orders',
            'name'     => _l('purchase_orders'),
            'icon'     => 'fa fa-shopping-cart',
            'href'     => admin_url('pos_inventory/purchase_orders'),
            'position' => 7,
        ]);
    }

    if(has_permission('pos_transactions','','view')){
        $CI->app_menu->add_sidebar_children_item('pos_inventory', [
            'slug'     => 'pos_transactions',
            'name'     => _l('transactions'),
            'icon'     => 'fa fa-list-alt',
            'href'     => admin_url('pos_inventory/transactions'),
            'position' => 8,
        ]);
    }

    if(has_permission('pos_integrations','','view')){
        $CI->app_menu->add_sidebar_children_item('pos_inventory', [
            'slug'     => 'pos_integrations',
            'name'     => _l('integrations'),
            'icon'     => 'fa fa-plug',
            'href'     => admin_url('pos_inventory/integrations'),
            'position' => 9,
        ]);
    }

    if(has_permission('pos_reports','','view')){
        $CI->app_menu->add_sidebar_children_item('pos_inventory', [
            'slug'     => 'pos_reports',
            'name'     => _l('reports'),
            'icon'     => 'fa fa-bar-chart',
            'href'     => admin_url('pos_inventory/reports'),
            'position' => 10,
        ]);
    }
}

/**
 * Load JavaScript files
 */
function pos_inventory_load_js(){
    $CI = &get_instance();
    $viewuri = $_SERVER['REQUEST_URI'];

    if(!(strpos($viewuri,'admin/pos_inventory/dashboard') === false)){
        echo '<script src="'.module_dir_url(POS_INVENTORY_MODULE_NAME, 'assets/js/dashboard.js').'?v=' . VERSION_POS_INVENTORY.'"></script>';
    }

    if(!(strpos($viewuri,'admin/pos_inventory/products') === false)){
        echo '<script src="'.module_dir_url(POS_INVENTORY_MODULE_NAME, 'assets/js/products.js').'?v=' . VERSION_POS_INVENTORY.'"></script>';
    }

    if(!(strpos($viewuri,'admin/pos_inventory/pos') === false)){
        echo '<script src="'.module_dir_url(POS_INVENTORY_MODULE_NAME, 'assets/js/pos_inventory.js').'?v=' . VERSION_POS_INVENTORY.'"></script>';
    }

    // Load common POS scripts
    if(!(strpos($viewuri,'admin/pos_inventory') === false)){
        echo '<script src="'.module_dir_url(POS_INVENTORY_MODULE_NAME, 'assets/js/common.js').'?v=' . VERSION_POS_INVENTORY.'"></script>';
    }
}

/**
 * Add CSS and other head components
 */
function pos_inventory_add_head_components(){
    $CI = &get_instance();
    $viewuri = $_SERVER['REQUEST_URI'];

    if(!(strpos($viewuri,'admin/pos_inventory') === false)){
        echo '<link href="' . module_dir_url(POS_INVENTORY_MODULE_NAME, 'assets/css/pos_inventory.css') . '?v=' . VERSION_POS_INVENTORY. '"  rel="stylesheet" type="text/css" />';
    }

    if(!(strpos($viewuri,'admin/pos_inventory/dashboard') === false)){
        echo '<link href="' . module_dir_url(POS_INVENTORY_MODULE_NAME, 'assets/css/dashboard.css') . '?v=' . VERSION_POS_INVENTORY. '"  rel="stylesheet" type="text/css" />';
    }

    if(!(strpos($viewuri,'admin/pos_inventory/pos') === false)){
        echo '<link href="' . module_dir_url(POS_INVENTORY_MODULE_NAME, 'assets/css/pos.css') . '?v=' . VERSION_POS_INVENTORY. '"  rel="stylesheet" type="text/css" />';
    }
}

/**
 * Register permissions
 */
function pos_inventory_permissions()
{
    $capabilities = [];
    $capabilities['capabilities'] = [
        'view'   => _l('permission_view') . '(' . _l('permission_global') . ')',
        'create' => _l('permission_create'),
        'edit'   => _l('permission_edit'),
        'delete' => _l('permission_delete'),
    ];

    $dashboard_capabilities = [];
    $dashboard_capabilities['capabilities'] = [
        'view'   => _l('permission_view') . '(' . _l('permission_global') . ')',
    ];

    // Register permissions
    register_staff_capabilities('pos_dashboard', $dashboard_capabilities, _l('pos_dashboard'));
    register_staff_capabilities('pos_interface', $capabilities, _l('pos_interface'));
    register_staff_capabilities('pos_products', $capabilities, _l('products'));
    register_staff_capabilities('pos_categories', $capabilities, _l('categories'));
    register_staff_capabilities('pos_inventory', $capabilities, _l('inventory_management'));
    register_staff_capabilities('pos_suppliers', $capabilities, _l('suppliers'));
    register_staff_capabilities('pos_purchase_orders', $capabilities, _l('purchase_orders'));
    register_staff_capabilities('pos_transactions', $capabilities, _l('transactions'));
    register_staff_capabilities('pos_integrations', $capabilities, _l('integrations'));
    register_staff_capabilities('pos_reports', $dashboard_capabilities, _l('reports'));
}

/**
 * Add dashboard widgets
 */
function pos_inventory_add_dashboard_widgets()
{
    if(has_permission('pos_dashboard','','view')){
        $CI = &get_instance();
        $CI->load->model('pos_inventory_model');

        // Add POS stats widget
        add_dashboard_widget('pos_stats', 'pos_inventory/dashboard/widgets/stats');

        // Add inventory alerts widget
        add_dashboard_widget('pos_inventory_alerts', 'pos_inventory/dashboard/widgets/inventory_alerts');
    }
}

// Register language files
register_language_files(POS_INVENTORY_MODULE_NAME, [POS_INVENTORY_MODULE_NAME]);

// Load helper
$CI = &get_instance();
$CI->load->helper(POS_INVENTORY_MODULE_NAME . '/pos_inventory');
