# 🚨 CRITICAL ISSUE FOUND: Directory Name Mismatch

## Problem
The module is not being recognized by Perfex CRM because of a **directory name mismatch**:

- **Directory Name**: `Advanced POS & Central Inventory`
- **Module Name in Code**: `pos_inventory`

## Solution
The directory name MUST match the module name defined in the code.

## Steps to Fix

### Option 1: Rename Directory (RECOMMENDED)

1. **Navigate to the modules directory**:
   ```
   C:\xampp\htdocs\perfex\modules\
   ```

2. **Rename the directory**:
   - From: `Advanced POS & Central Inventory`
   - To: `pos_inventory`

3. **Windows Command**:
   ```cmd
   cd C:\xampp\htdocs\perfex\modules
   ren "Advanced POS & Central Inventory" pos_inventory
   ```

4. **Verify the structure**:
   ```
   C:\xampp\htdocs\perfex\modules\pos_inventory\
   ├── pos_inventory.php
   ├── controllers/
   ├── models/
   ├── views/
   ├── install/
   └── ...
   ```

### Option 2: Change Module Name in Code (Alternative)

If you prefer to keep the current directory name, change the module name in the code:

1. **Edit `pos_inventory.php`**:
   ```php
   define('POS_INVENTORY_MODULE_NAME', 'advanced_pos_central_inventory');
   ```

2. **Update all references** in the codebase from `pos_inventory` to `advanced_pos_central_inventory`

## Why This Happens

Perfex CRM discovers modules by:
1. Scanning the `/modules/` directory
2. Looking for directories that contain a PHP file with the same name as the directory
3. The module name defined in the code must match the directory name

## After Fixing

Once you rename the directory to `pos_inventory`:

1. **Clear Perfex CRM cache** (if any)
2. **Go to Setup > Modules** in Perfex CRM admin
3. **The module should now appear** in the modules list
4. **Click "Install"** to activate it

## Verification

After renaming, the module should be located at:
```
C:\xampp\htdocs\perfex\modules\pos_inventory\pos_inventory.php
```

And the module name in the file should be:
```php
define('POS_INVENTORY_MODULE_NAME', 'pos_inventory');
```

## 🎯 RECOMMENDATION

**Use Option 1** (rename directory to `pos_inventory`) as it:
- Follows Perfex CRM naming conventions
- Matches other modules like `perfex_ecommerce`
- Is cleaner and more professional
- Avoids potential issues with special characters in directory names

Once you rename the directory, the module will be immediately recognized by Perfex CRM! 🚀
