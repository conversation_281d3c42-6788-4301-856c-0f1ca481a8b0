<?php defined('BASEPATH') or exit('No direct script access allowed'); ?>
<?php init_head(); ?>
<div id="wrapper">
    <div class="content">
        <div class="row">
            <div class="col-md-12">
                <div class="panel_s">
                    <div class="panel-body">
                        <div class="row">
                            <div class="col-md-12">
                                <h4 class="no-margin">
                                    <?php echo $title; ?>
                                </h4>
                                <hr class="hr-panel-heading" />
                            </div>
                        </div>
                        
                        <!-- Filter Section -->
                        <div class="row">
                            <div class="col-md-12">
                                <div class="panel panel-default">
                                    <div class="panel-body">
                                        <div class="row">
                                            <div class="col-md-2">
                                                <div class="form-group">
                                                    <label for="filter-date-from"><?php echo _l('date_from'); ?></label>
                                                    <input type="date" class="form-control" id="filter-date-from" value="<?php echo date('Y-m-01'); ?>">
                                                </div>
                                            </div>
                                            <div class="col-md-2">
                                                <div class="form-group">
                                                    <label for="filter-date-to"><?php echo _l('date_to'); ?></label>
                                                    <input type="date" class="form-control" id="filter-date-to" value="<?php echo date('Y-m-d'); ?>">
                                                </div>
                                            </div>
                                            <div class="col-md-2">
                                                <div class="form-group">
                                                    <label for="filter-movement-type"><?php echo _l('movement_type'); ?></label>
                                                    <select class="form-control" id="filter-movement-type">
                                                        <option value=""><?php echo _l('all'); ?></option>
                                                        <option value="in"><?php echo _l('stock_in'); ?></option>
                                                        <option value="out"><?php echo _l('stock_out'); ?></option>
                                                        <option value="transfer"><?php echo _l('transfer'); ?></option>
                                                        <option value="adjustment"><?php echo _l('adjustment'); ?></option>
                                                    </select>
                                                </div>
                                            </div>
                                            <div class="col-md-2">
                                                <div class="form-group">
                                                    <label for="filter-product"><?php echo _l('product'); ?></label>
                                                    <input type="text" class="form-control" id="filter-product" placeholder="<?php echo _l('search_products'); ?>">
                                                </div>
                                            </div>
                                            <div class="col-md-2">
                                                <div class="form-group">
                                                    <label for="filter-location"><?php echo _l('location'); ?></label>
                                                    <select class="form-control" id="filter-location">
                                                        <option value=""><?php echo _l('all_locations'); ?></option>
                                                        <!-- Locations will be loaded via AJAX -->
                                                    </select>
                                                </div>
                                            </div>
                                            <div class="col-md-2">
                                                <div class="form-group">
                                                    <label>&nbsp;</label><br>
                                                    <button type="button" class="btn btn-info" id="filter-movements">
                                                        <i class="fa fa-filter"></i> <?php echo _l('filter'); ?>
                                                    </button>
                                                    <button type="button" class="btn btn-default" id="reset-filters">
                                                        <i class="fa fa-refresh"></i> <?php echo _l('reset'); ?>
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-12">
                                <div class="table-responsive">
                                    <table class="table dt-table" id="stock-movements-table">
                                        <thead>
                                            <tr>
                                                <th><?php echo _l('date'); ?></th>
                                                <th><?php echo _l('product'); ?></th>
                                                <th><?php echo _l('location'); ?></th>
                                                <th><?php echo _l('movement_type'); ?></th>
                                                <th><?php echo _l('quantity'); ?></th>
                                                <th><?php echo _l('unit_cost'); ?></th>
                                                <th><?php echo _l('reference'); ?></th>
                                                <th><?php echo _l('notes'); ?></th>
                                                <th><?php echo _l('created_by'); ?></th>
                                            </tr>
                                        </thead>
                                        <tbody></tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
$(document).ready(function() {
    // Initialize DataTable
    var stockMovementsTable = $('#stock-movements-table').DataTable({
        "processing": true,
        "serverSide": true,
        "ajax": {
            "url": "<?php echo admin_url('pos_inventory/stock_movements_table'); ?>",
            "type": "POST",
            "data": function(d) {
                d.date_from = $('#filter-date-from').val();
                d.date_to = $('#filter-date-to').val();
                d.movement_type = $('#filter-movement-type').val();
                d.product = $('#filter-product').val();
                d.location_id = $('#filter-location').val();
            }
        },
        "columns": [
            {"data": "created_at"},
            {"data": "product_name"},
            {"data": "location_name"},
            {"data": "movement_type"},
            {"data": "quantity"},
            {"data": "unit_cost"},
            {"data": "reference"},
            {"data": "notes"},
            {"data": "created_by_name"}
        ],
        "order": [[0, "desc"]]
    });

    // Load locations for filter
    $.get('<?php echo admin_url('pos_inventory/get_locations'); ?>', function(locations) {
        var locationSelect = $('#filter-location');
        $.each(locations, function(index, location) {
            locationSelect.append('<option value="' + location.id + '">' + location.name + '</option>');
        });
    }, 'json');

    // Filter movements
    $('#filter-movements').on('click', function() {
        stockMovementsTable.ajax.reload();
    });

    // Reset filters
    $('#reset-filters').on('click', function() {
        $('#filter-date-from').val('<?php echo date('Y-m-01'); ?>');
        $('#filter-date-to').val('<?php echo date('Y-m-d'); ?>');
        $('#filter-movement-type').val('');
        $('#filter-product').val('');
        $('#filter-location').val('');
        stockMovementsTable.ajax.reload();
    });
});
</script>

<?php init_tail(); ?>
