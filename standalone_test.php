<?php

// Standalone test script to verify fixes
define('BASEPATH', true); // Define BASEPATH to allow loading helper

echo "<h2>POS Inventory Module - Standalone Fix Verification</h2>\n";

// Test 1: Check if files exist
echo "<h3>Test 1: File Existence Check</h3>\n";
$files_to_check = [
    'index.php' => 'Module index file',
    '.htaccess' => 'Security configuration file',
    'helpers/pos_inventory_logging_helper.php' => 'Logging helper file',
    'pos_inventory.php' => 'Main module file',
    'config/module_config.php' => 'Module configuration',
    'config/version.php' => 'Version configuration'
];

$all_files_exist = true;
foreach ($files_to_check as $file => $description) {
    if (file_exists($file)) {
        echo "✅ $description exists: $file<br>\n";
    } else {
        echo "❌ $description missing: $file<br>\n";
        $all_files_exist = false;
    }
}

// Test 2: Check logging helper functions
echo "<h3>Test 2: Logging Helper Functions Check</h3>\n";
if (file_exists('helpers/pos_inventory_logging_helper.php')) {
    require_once('helpers/pos_inventory_logging_helper.php');
    
    $functions_to_check = [
        'pos_inventory_safe_log',
        'pos_inventory_log_info',
        'pos_inventory_log_error',
        'pos_inventory_log_warning',
        'pos_inventory_log_debug',
        'pos_inventory_init_logging'
    ];
    
    $all_functions_exist = true;
    foreach ($functions_to_check as $function) {
        if (function_exists($function)) {
            echo "✅ Function exists: $function<br>\n";
        } else {
            echo "❌ Function missing: $function<br>\n";
            $all_functions_exist = false;
        }
    }
    
    // Test 3: Check log level constants
    echo "<h3>Test 3: Log Level Constants Check</h3>\n";
    $log_constants = ['LOG_LEVEL_ERROR', 'LOG_LEVEL_DEBUG', 'LOG_LEVEL_INFO', 'LOG_LEVEL_WARNING'];
    $all_constants_exist = true;
    foreach ($log_constants as $constant) {
        if (defined($constant)) {
            echo "✅ $constant = " . constant($constant) . "<br>\n";
        } else {
            echo "❌ $constant not defined<br>\n";
            $all_constants_exist = false;
        }
    }
    
    // Test 4: Check global log levels array
    echo "<h3>Test 4: Global Log Levels Array Check</h3>\n";
    if (isset($GLOBALS['_log_levels'])) {
        echo "✅ Global _log_levels array exists<br>\n";
        foreach ($GLOBALS['_log_levels'] as $level => $value) {
            echo "  - $level = $value<br>\n";
        }
    } else {
        echo "❌ Global _log_levels array not found<br>\n";
        $all_constants_exist = false;
    }
    
} else {
    echo "❌ Cannot test logging functions - helper file missing<br>\n";
    $all_functions_exist = false;
    $all_constants_exist = false;
}

// Test 5: Check .htaccess content
echo "<h3>Test 5: .htaccess Configuration Check</h3>\n";
if (file_exists('.htaccess')) {
    $htaccess_content = file_get_contents('.htaccess');
    $required_directives = [
        'Options -Indexes',
        'DirectoryIndex index.php',
        'ErrorDocument 404',
        'X-Frame-Options',
        'X-Content-Type-Options'
    ];
    
    $htaccess_ok = true;
    foreach ($required_directives as $directive) {
        if (strpos($htaccess_content, $directive) !== false) {
            echo "✅ .htaccess contains: $directive<br>\n";
        } else {
            echo "❌ .htaccess missing: $directive<br>\n";
            $htaccess_ok = false;
        }
    }
} else {
    echo "❌ .htaccess file not found<br>\n";
    $htaccess_ok = false;
}

// Test 6: Check index.php content
echo "<h3>Test 6: Index.php Content Check</h3>\n";
if (file_exists('index.php')) {
    $index_content = file_get_contents('index.php');
    $required_elements = [
        'defined(\'BASEPATH\')',
        'redirect(admin_url(\'pos_inventory\'))',
        'has_permission(\'pos_inventory\'',
        'is_staff_logged_in()'
    ];
    
    $index_ok = true;
    foreach ($required_elements as $element) {
        if (strpos($index_content, $element) !== false) {
            echo "✅ index.php contains: $element<br>\n";
        } else {
            echo "❌ index.php missing: $element<br>\n";
            $index_ok = false;
        }
    }
} else {
    echo "❌ index.php file not found<br>\n";
    $index_ok = false;
}

// Test 7: Check if main module file was updated
echo "<h3>Test 7: Main Module File Updates Check</h3>\n";
if (file_exists('pos_inventory.php')) {
    $module_content = file_get_contents('pos_inventory.php');
    $required_updates = [
        'pos_inventory_logging_helper.php',
        'pos_inventory_log_info',
        'pos_inventory_log_error'
    ];
    
    $module_ok = true;
    foreach ($required_updates as $update) {
        if (strpos($module_content, $update) !== false) {
            echo "✅ pos_inventory.php contains: $update<br>\n";
        } else {
            echo "❌ pos_inventory.php missing: $update<br>\n";
            $module_ok = false;
        }
    }
} else {
    echo "❌ pos_inventory.php file not found<br>\n";
    $module_ok = false;
}

// Summary
echo "<h3>Summary</h3>\n";
$overall_status = $all_files_exist && $all_functions_exist && $all_constants_exist && $htaccess_ok && $index_ok && $module_ok;

if ($overall_status) {
    echo "🎉 <strong style='color: green;'>All fixes appear to be working correctly!</strong><br>\n";
    echo "<br>\n";
    echo "<strong>What was fixed:</strong><br>\n";
    echo "1. ✅ Created safe logging helper to prevent 'Undefined array key WARNING' errors<br>\n";
    echo "2. ✅ Added index.php to handle direct module access and prevent 404 errors<br>\n";
    echo "3. ✅ Created .htaccess for security and proper routing<br>\n";
    echo "4. ✅ Updated module files to use safe logging functions<br>\n";
    echo "<br>\n";
    echo "<strong>Expected results:</strong><br>\n";
    echo "- No more 'Undefined array key WARNING' errors in logs<br>\n";
    echo "- No more '404 Page Not Found: /index' errors<br>\n";
    echo "- Module should load and work properly<br>\n";
    echo "- Better security with .htaccess protection<br>\n";
} else {
    echo "⚠️ <strong style='color: orange;'>Some issues were found. Please review the test results above.</strong><br>\n";
}

echo "<br>\n";
echo "<strong>Legend:</strong><br>\n";
echo "✅ = Working correctly<br>\n";
echo "❌ = Issue found<br>\n";

?>
