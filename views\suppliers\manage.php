<?php defined('BASEPATH') or exit('No direct script access allowed'); ?>
<?php init_head(); ?>
<div id="wrapper">
    <div class="content">
        <div class="row">
            <div class="col-md-12">
                <div class="panel_s">
                    <div class="panel-body">
                        <div class="row">
                            <div class="col-md-8">
                                <h4 class="no-margin"><?php echo _l('suppliers_management'); ?></h4>
                            </div>
                            <div class="col-md-4 text-right">
                                <?php if (has_permission('pos_suppliers', '', 'create')): ?>
                                    <a href="<?php echo admin_url('pos_inventory/supplier'); ?>" class="btn btn-primary">
                                        <i class="fa fa-plus"></i> <?php echo _l('new_supplier'); ?>
                                    </a>
                                <?php endif; ?>
                            </div>
                        </div>
                        <hr class="hr-panel-heading" />

                        <!-- Statistics Cards -->
                        <div class="row">
                            <div class="col-md-3">
                                <div class="panel panel-default">
                                    <div class="panel-body text-center">
                                        <h3 class="no-margin text-primary" id="total-suppliers">0</h3>
                                        <p class="text-muted"><?php echo _l('total_suppliers'); ?></p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="panel panel-default">
                                    <div class="panel-body text-center">
                                        <h3 class="no-margin text-success" id="active-suppliers">0</h3>
                                        <p class="text-muted"><?php echo _l('active_suppliers'); ?></p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="panel panel-default">
                                    <div class="panel-body text-center">
                                        <h3 class="no-margin text-info" id="linked-suppliers">0</h3>
                                        <p class="text-muted"><?php echo _l('perfex_linked_suppliers'); ?></p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="panel panel-default">
                                    <div class="panel-body text-center">
                                        <h3 class="no-margin text-warning" id="recent-orders">0</h3>
                                        <p class="text-muted"><?php echo _l('recent_orders_30_days'); ?></p>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Filters -->
                        <div class="row">
                            <div class="col-md-12">
                                <div class="panel panel-default">
                                    <div class="panel-heading">
                                        <h4 class="panel-title">
                                            <a data-toggle="collapse" href="#filters-panel">
                                                <i class="fa fa-filter"></i> <?php echo _l('filters'); ?>
                                            </a>
                                        </h4>
                                    </div>
                                    <div id="filters-panel" class="panel-collapse collapse">
                                        <div class="panel-body">
                                            <div class="row">
                                                <div class="col-md-3">
                                                    <div class="form-group">
                                                        <label for="filter_status"><?php echo _l('status'); ?></label>
                                                        <select id="filter_status" class="form-control">
                                                            <option value=""><?php echo _l('all_statuses'); ?></option>
                                                            <option value="1"><?php echo _l('active'); ?></option>
                                                            <option value="0"><?php echo _l('inactive'); ?></option>
                                                        </select>
                                                    </div>
                                                </div>
                                                <div class="col-md-3">
                                                    <div class="form-group">
                                                        <label for="filter_perfex_linked"><?php echo _l('perfex_crm_linked'); ?></label>
                                                        <select id="filter_perfex_linked" class="form-control">
                                                            <option value=""><?php echo _l('all'); ?></option>
                                                            <option value="1"><?php echo _l('linked'); ?></option>
                                                            <option value="0"><?php echo _l('not_linked'); ?></option>
                                                        </select>
                                                    </div>
                                                </div>
                                                <div class="col-md-3">
                                                    <div class="form-group">
                                                        <label for="filter_country"><?php echo _l('country'); ?></label>
                                                        <select id="filter_country" class="form-control selectpicker" data-live-search="true">
                                                            <option value=""><?php echo _l('all_countries'); ?></option>
                                                            <?php foreach(get_all_countries() as $country): ?>
                                                                <option value="<?php echo $country['country_id']; ?>"><?php echo $country['short_name']; ?></option>
                                                            <?php endforeach; ?>
                                                        </select>
                                                    </div>
                                                </div>
                                                <div class="col-md-3">
                                                    <div class="form-group">
                                                        <label for="filter_search"><?php echo _l('search'); ?></label>
                                                        <input type="text" id="filter_search" class="form-control" placeholder="<?php echo _l('search_suppliers'); ?>">
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="row">
                                                <div class="col-md-12">
                                                    <button type="button" class="btn btn-primary" onclick="applyFilters()">
                                                        <i class="fa fa-filter"></i> <?php echo _l('apply_filters'); ?>
                                                    </button>
                                                    <button type="button" class="btn btn-default" onclick="clearFilters()">
                                                        <i class="fa fa-refresh"></i> <?php echo _l('clear_filters'); ?>
                                                    </button>
                                                    <button type="button" class="btn btn-info" onclick="exportSuppliers()">
                                                        <i class="fa fa-download"></i> <?php echo _l('export'); ?>
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Suppliers Table -->
                        <div class="row">
                            <div class="col-md-12">
                                <div class="table-responsive">
                                    <table class="table dt-table table-suppliers" id="suppliers-table">
                                        <thead>
                                            <tr>
                                                <th>
                                                    <span class="hide"> - </span>
                                                    <div class="checkbox mass_select_all_wrap">
                                                        <input type="checkbox" id="mass_select_all" data-to-table="suppliers">
                                                        <label></label>
                                                    </div>
                                                </th>
                                                <th><?php echo _l('name'); ?></th>
                                                <th><?php echo _l('company'); ?></th>
                                                <th><?php echo _l('email'); ?></th>
                                                <th><?php echo _l('phone'); ?></th>
                                                <th><?php echo _l('country'); ?></th>
                                                <th><?php echo _l('perfex_crm_linked'); ?></th>
                                                <th><?php echo _l('status'); ?></th>
                                                <th><?php echo _l('actions'); ?></th>
                                            </tr>
                                        </thead>
                                        <tbody></tbody>
                                    </table>
                                </div>
                            </div>
                        </div>

                        <!-- Bulk Actions -->
                        <div class="row">
                            <div class="col-md-12">
                                <div class="panel panel-default">
                                    <div class="panel-heading">
                                        <h4 class="panel-title"><?php echo _l('bulk_actions'); ?></h4>
                                    </div>
                                    <div class="panel-body">
                                        <div class="btn-group">
                                            <button type="button" class="btn btn-success" onclick="bulkAction('activate')">
                                                <i class="fa fa-check"></i> <?php echo _l('activate_selected'); ?>
                                            </button>
                                            <button type="button" class="btn btn-warning" onclick="bulkAction('deactivate')">
                                                <i class="fa fa-ban"></i> <?php echo _l('deactivate_selected'); ?>
                                            </button>
                                            <button type="button" class="btn btn-info" onclick="bulkAction('link_perfex')">
                                                <i class="fa fa-link"></i> <?php echo _l('link_to_perfex'); ?>
                                            </button>
                                            <button type="button" class="btn btn-danger" onclick="bulkAction('delete')">
                                                <i class="fa fa-trash"></i> <?php echo _l('delete_selected'); ?>
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Import Suppliers Modal -->
<div class="modal fade" id="import-suppliers-modal" tabindex="-1" role="dialog">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal">&times;</button>
                <h4 class="modal-title"><?php echo _l('import_suppliers'); ?></h4>
            </div>
            <div class="modal-body">
                <form id="import-suppliers-form" enctype="multipart/form-data">
                    <div class="form-group">
                        <label for="import_file"><?php echo _l('csv_file'); ?></label>
                        <input type="file" name="import_file" id="import_file" class="form-control" accept=".csv" required>
                        <small class="text-muted"><?php echo _l('csv_import_instructions'); ?></small>
                    </div>
                    
                    <div class="checkbox">
                        <input type="checkbox" name="update_existing" id="update_existing" value="1">
                        <label for="update_existing"><?php echo _l('update_existing_suppliers'); ?></label>
                    </div>
                    
                    <div class="checkbox">
                        <input type="checkbox" name="auto_link_perfex" id="auto_link_perfex" value="1">
                        <label for="auto_link_perfex"><?php echo _l('auto_link_to_perfex_contacts'); ?></label>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-default" data-dismiss="modal"><?php echo _l('cancel'); ?></button>
                <button type="button" class="btn btn-primary" onclick="importSuppliers()"><?php echo _l('import'); ?></button>
                <a href="<?php echo admin_url('pos_inventory/download_supplier_template'); ?>" class="btn btn-info">
                    <i class="fa fa-download"></i> <?php echo _l('download_template'); ?>
                </a>
            </div>
        </div>
    </div>
</div>

<script>
$(document).ready(function() {
    initDataTable('.table-suppliers', admin_url + 'pos_inventory/suppliers_table', [8], [8]);
    loadStatistics();
    
    // Auto-refresh statistics every 30 seconds
    setInterval(loadStatistics, 30000);
});

function loadStatistics() {
    $.post(admin_url + 'pos_inventory/get_supplier_statistics', {}, function(response) {
        if (response.success) {
            var stats = response.statistics;
            $('#total-suppliers').text(stats.total_suppliers || 0);
            $('#active-suppliers').text(stats.active_suppliers || 0);
            $('#linked-suppliers').text(stats.linked_suppliers || 0);
            $('#recent-orders').text(stats.recent_orders || 0);
        }
    }, 'json');
}

function applyFilters() {
    var table = $('#suppliers-table').DataTable();
    
    // Get filter values
    var status = $('#filter_status').val();
    var perfexLinked = $('#filter_perfex_linked').val();
    var country = $('#filter_country').val();
    var search = $('#filter_search').val();
    
    // Apply filters to DataTable
    table.ajax.url(admin_url + 'pos_inventory/suppliers_table?' + 
        'status=' + (status || '') +
        '&perfex_linked=' + (perfexLinked || '') +
        '&country=' + (country || '') +
        '&search=' + (search || '')
    ).load();
    
    // Update statistics with filters
    loadStatistics();
}

function clearFilters() {
    $('#filter_status').val('');
    $('#filter_perfex_linked').val('');
    $('#filter_country').selectpicker('val', '');
    $('#filter_search').val('');
    
    // Reset table
    var table = $('#suppliers-table').DataTable();
    table.ajax.url(admin_url + 'pos_inventory/suppliers_table').load();
    
    // Reset statistics
    loadStatistics();
}

function deleteSupplier(id) {
    if (confirm('<?php echo _l('confirm_delete_supplier'); ?>')) {
        $.post(admin_url + 'pos_inventory/delete_supplier', {
            id: id
        }, function(response) {
            if (response.success) {
                alert_float('success', response.message);
                $('#suppliers-table').DataTable().ajax.reload();
                loadStatistics();
            } else {
                alert_float('danger', response.message);
            }
        }, 'json');
    }
}

function toggleSupplierStatus(id, status) {
    $.post(admin_url + 'pos_inventory/toggle_supplier_status', {
        id: id,
        status: status
    }, function(response) {
        if (response.success) {
            alert_float('success', response.message);
            $('#suppliers-table').DataTable().ajax.reload();
            loadStatistics();
        } else {
            alert_float('danger', response.message);
        }
    }, 'json');
}

function linkToPerfex(id) {
    // Implementation for linking supplier to Perfex CRM contact
    window.open(admin_url + 'pos_inventory/link_supplier_to_perfex/' + id, '_blank', 'width=800,height=600');
}

function exportSuppliers() {
    var status = $('#filter_status').val();
    var perfexLinked = $('#filter_perfex_linked').val();
    var country = $('#filter_country').val();
    var search = $('#filter_search').val();
    
    var url = admin_url + 'pos_inventory/export_suppliers?' +
        'status=' + (status || '') +
        '&perfex_linked=' + (perfexLinked || '') +
        '&country=' + (country || '') +
        '&search=' + (search || '');
    
    window.open(url, '_blank');
}

function importSuppliers() {
    var formData = new FormData($('#import-suppliers-form')[0]);
    
    $.ajax({
        url: admin_url + 'pos_inventory/import_suppliers',
        type: 'POST',
        data: formData,
        processData: false,
        contentType: false,
        success: function(response) {
            if (response.success) {
                alert_float('success', response.message);
                $('#import-suppliers-modal').modal('hide');
                $('#suppliers-table').DataTable().ajax.reload();
                loadStatistics();
            } else {
                alert_float('danger', response.message);
            }
        },
        error: function() {
            alert_float('danger', 'Error importing suppliers');
        }
    });
}

// Bulk actions
function bulkAction(action) {
    var selectedIds = [];
    $('.individual-checkbox:checked').each(function() {
        selectedIds.push($(this).val());
    });
    
    if (selectedIds.length === 0) {
        alert_float('warning', '<?php echo _l('no_items_selected'); ?>');
        return;
    }
    
    var confirmMessage = '';
    switch (action) {
        case 'delete':
            confirmMessage = '<?php echo _l('confirm_bulk_delete_suppliers'); ?>';
            break;
        case 'activate':
            confirmMessage = '<?php echo _l('confirm_bulk_activate_suppliers'); ?>';
            break;
        case 'deactivate':
            confirmMessage = '<?php echo _l('confirm_bulk_deactivate_suppliers'); ?>';
            break;
        case 'link_perfex':
            confirmMessage = '<?php echo _l('confirm_bulk_link_perfex'); ?>';
            break;
    }
    
    if (confirm(confirmMessage)) {
        $.post(admin_url + 'pos_inventory/bulk_supplier_action', {
            action: action,
            ids: selectedIds
        }, function(response) {
            if (response.success) {
                alert_float('success', response.message);
                $('#suppliers-table').DataTable().ajax.reload();
                loadStatistics();
            } else {
                alert_float('danger', response.message);
            }
        }, 'json');
    }
}
</script>

<?php init_tail(); ?>
