<?php defined('BASEPATH') or exit('No direct script access allowed'); ?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title><?php echo _l('pos_interface') . ' - ' . get_option('companyname'); ?></title>
    
    <!-- CSS -->
    <link href="<?php echo base_url('assets/plugins/bootstrap/css/bootstrap.min.css'); ?>" rel="stylesheet">
    <link href="<?php echo base_url('assets/plugins/font-awesome/css/font-awesome.min.css'); ?>" rel="stylesheet">
    <link href="<?php echo module_dir_url(POS_INVENTORY_MODULE_NAME, 'assets/css/pos_inventory.css'); ?>" rel="stylesheet">
    
    <style>
        body {
            font-family: 'Helvetica Neue', Arial, sans-serif;
            background: #f8f9fa;
            margin: 0;
            padding: 0;
        }
        .pos-interface {
            height: 100vh;
            overflow: hidden;
            display: flex;
            flex-direction: column;
        }
        .pos-header {
            background: #fff;
            border-bottom: 1px solid #e9ecef;
            padding: 15px 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            z-index: 1000;
        }
        .pos-content {
            flex: 1;
            display: flex;
            overflow: hidden;
        }
        .pos-products-section {
            flex: 2;
            background: #fff;
            border-right: 1px solid #e9ecef;
            display: flex;
            flex-direction: column;
        }
        .pos-cart-section {
            flex: 1;
            background: #f8f9fa;
            display: flex;
            flex-direction: column;
            min-width: 400px;
        }
        .pos-search {
            position: relative;
            max-width: 400px;
        }
        .pos-search input {
            padding-right: 40px;
            height: 45px;
            font-size: 16px;
            border-radius: 25px;
        }
        .pos-search .search-icon {
            position: absolute;
            right: 15px;
            top: 50%;
            transform: translateY(-50%);
            color: #6c757d;
        }
        .category-tabs {
            background: #fff;
            border-bottom: 1px solid #e9ecef;
            padding: 10px 20px;
            overflow-x: auto;
            white-space: nowrap;
        }
        .category-tabs .nav-pills > li > a {
            border-radius: 20px;
            margin-right: 10px;
            padding: 8px 16px;
            background: #f8f9fa;
            color: #495057;
            border: 1px solid #dee2e6;
        }
        .category-tabs .nav-pills > li.active > a {
            background: #007bff;
            color: white;
            border-color: #007bff;
        }
        .products-grid {
            flex: 1;
            padding: 20px;
            overflow-y: auto;
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(180px, 1fr));
            gap: 15px;
            align-content: start;
        }
        .product-card {
            background: white;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 15px;
            cursor: pointer;
            transition: all 0.2s ease;
            text-align: center;
            position: relative;
        }
        .product-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            border-color: #007bff;
        }
        .product-card.out-of-stock {
            opacity: 0.6;
            cursor: not-allowed;
        }
        .product-card.out-of-stock:hover {
            transform: none;
            box-shadow: none;
        }
        .product-image {
            width: 100%;
            height: 120px;
            object-fit: cover;
            border-radius: 4px;
            margin-bottom: 10px;
            background: #f8f9fa;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .product-name {
            font-weight: 600;
            font-size: 14px;
            margin-bottom: 5px;
            color: #495057;
            line-height: 1.3;
        }
        .product-price {
            font-size: 16px;
            font-weight: 700;
            color: #28a745;
            margin-bottom: 5px;
        }
        .product-stock {
            font-size: 12px;
            color: #6c757d;
        }
        .product-stock.low-stock {
            color: #ffc107;
            font-weight: 600;
        }
        .customer-section {
            padding: 20px;
            border-bottom: 1px solid #e9ecef;
        }
        .customer-select {
            width: 100%;
            padding: 10px;
            border: 1px solid #ced4da;
            border-radius: 4px;
            margin-bottom: 10px;
        }
        .cart-header {
            padding: 15px 20px;
            border-bottom: 1px solid #e9ecef;
            display: flex;
            justify-content: space-between;
            align-items: center;
            background: white;
        }
        .cart-items {
            flex: 1;
            overflow-y: auto;
            background: white;
        }
        .cart-item {
            padding: 15px 20px;
            border-bottom: 1px solid #f8f9fa;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .cart-item-info {
            flex: 1;
        }
        .cart-item-name {
            font-weight: 600;
            color: #495057;
            margin-bottom: 5px;
        }
        .cart-item-sku {
            font-size: 12px;
            color: #6c757d;
        }
        .cart-item-controls {
            display: flex;
            align-items: center;
            gap: 10px;
        }
        .qty-control {
            display: flex;
            align-items: center;
            border: 1px solid #ced4da;
            border-radius: 4px;
        }
        .qty-control button {
            background: none;
            border: none;
            padding: 5px 10px;
            cursor: pointer;
            color: #495057;
        }
        .qty-control button:hover {
            background: #f8f9fa;
        }
        .qty-control input {
            border: none;
            width: 50px;
            text-align: center;
            padding: 5px;
        }
        .cart-summary {
            background: white;
            padding: 20px;
            border-top: 1px solid #e9ecef;
        }
        .summary-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 10px;
            padding: 5px 0;
        }
        .summary-row.total {
            border-top: 2px solid #e9ecef;
            padding-top: 15px;
            margin-top: 15px;
            font-size: 18px;
            font-weight: 700;
        }
        .payment-section {
            background: white;
            padding: 20px;
            border-top: 1px solid #e9ecef;
        }
        .payment-methods {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 10px;
            margin-top: 10px;
        }
        .payment-method {
            padding: 15px;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            text-align: center;
            cursor: pointer;
            transition: all 0.2s ease;
        }
        .payment-method:hover {
            border-color: #007bff;
        }
        .payment-method.active {
            border-color: #007bff;
            background: #e3f2fd;
        }
        .payment-method i {
            font-size: 24px;
            margin-bottom: 5px;
            display: block;
        }
        .pos-actions {
            padding: 20px;
            background: white;
            border-top: 1px solid #e9ecef;
        }
        .btn-pos {
            width: 100%;
            padding: 15px;
            font-size: 16px;
            font-weight: 600;
            border: none;
            border-radius: 8px;
            margin-bottom: 10px;
            cursor: pointer;
            transition: all 0.2s ease;
        }
        .btn-pos-success {
            background: #28a745;
            color: white;
        }
        .btn-pos-success:hover:not(:disabled) {
            background: #218838;
        }
        .btn-pos-success:disabled {
            background: #6c757d;
            cursor: not-allowed;
        }
        .btn-pos-warning {
            background: #ffc107;
            color: #212529;
        }
        .btn-pos-warning:hover {
            background: #e0a800;
        }
        .btn-pos-primary {
            background: #007bff;
            color: white;
        }
        .btn-pos-primary:hover {
            background: #0056b3;
        }
        @media (max-width: 768px) {
            .pos-content {
                flex-direction: column;
            }
            .pos-cart-section {
                min-width: auto;
                max-height: 50vh;
            }
            .products-grid {
                grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
            }
        }
    </style>
</head>
<body>
    <div class="pos-interface">
        <!-- POS Header -->
        <div class="pos-header">
            <div class="row">
                <div class="col-md-3">
                    <h4 class="no-margin">
                        <i class="fa fa-shopping-cart"></i> <?php echo _l('pos_system'); ?>
                        <small class="text-muted" style="display: block; font-size: 12px;">
                            <?php echo get_option('companyname'); ?>
                        </small>
                    </h4>
                </div>
                <div class="col-md-4 text-center">
                    <div class="pos-search">
                        <input type="text" id="product-search" class="form-control"
                               placeholder="<?php echo _l('search_products_or_scan_barcode'); ?>"
                               autocomplete="off">
                        <i class="fa fa-search search-icon"></i>
                    </div>
                </div>
                <div class="col-md-2 text-center">
                    <div class="pos-location-info">
                        <small class="text-muted"><?php echo _l('location'); ?>:</small>
                        <div class="current-location">
                            <strong><?php echo $current_location['name']; ?></strong>
                        </div>
                    </div>
                </div>
                <div class="col-md-3 text-right">
                    <button type="button" class="btn btn-warning btn-sm" id="held-orders-btn">
                        <i class="fa fa-pause"></i> <?php echo _l('held_orders'); ?>
                        <span class="badge" id="held-orders-count" style="display: none;">0</span>
                    </button>
                    <button type="button" class="btn btn-info btn-sm" id="calculator-btn">
                        <i class="fa fa-calculator"></i>
                    </button>
                    <div class="btn-group">
                        <button type="button" class="btn btn-default btn-sm dropdown-toggle" data-toggle="dropdown">
                            <i class="fa fa-cog"></i> <span class="caret"></span>
                        </button>
                        <ul class="dropdown-menu dropdown-menu-right">
                            <li><a href="<?php echo admin_url('pos_inventory'); ?>"><i class="fa fa-dashboard"></i> <?php echo _l('dashboard'); ?></a></li>
                            <li><a href="<?php echo admin_url('pos_inventory/products'); ?>"><i class="fa fa-cube"></i> <?php echo _l('products'); ?></a></li>
                            <li><a href="<?php echo admin_url('pos_inventory/inventory'); ?>"><i class="fa fa-cubes"></i> <?php echo _l('inventory'); ?></a></li>
                            <li class="divider"></li>
                            <li><a href="#" id="pos-settings"><i class="fa fa-cog"></i> <?php echo _l('pos_settings'); ?></a></li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>

        <!-- POS Content -->
        <div class="pos-content">
            <!-- Products Section -->
            <div class="pos-products-section">
                <!-- Category Tabs -->
                <div class="category-tabs">
                    <ul class="nav nav-pills">
                        <li class="active">
                            <a href="#" data-category="" class="category-filter">
                                <?php echo _l('all_categories'); ?>
                            </a>
                        </li>
                        <?php foreach ($categories as $category) { ?>
                        <li>
                            <a href="#" data-category="<?php echo $category['id']; ?>" class="category-filter">
                                <?php echo $category['name']; ?>
                            </a>
                        </li>
                        <?php } ?>
                    </ul>
                </div>

                <!-- Products Grid -->
                <div class="products-grid" id="products-grid">
                    <?php foreach ($products as $product) {
                        $stock_class = '';
                        $stock_status = '';
                        if ($product['track_inventory']) {
                            if ($product['stock_quantity'] <= 0) {
                                $stock_class = 'out-of-stock';
                                $stock_status = 'out-of-stock';
                            } elseif ($product['stock_quantity'] <= $product['low_stock_threshold']) {
                                $stock_class = 'low-stock';
                                $stock_status = 'low-stock';
                            } else {
                                $stock_status = 'in-stock';
                            }
                        }
                    ?>
                    <div class="product-card <?php echo $stock_class; ?>"
                         data-product-id="<?php echo $product['id']; ?>"
                         data-product-name="<?php echo htmlspecialchars($product['name']); ?>"
                         data-product-price="<?php echo $product['sale_price']; ?>"
                         data-product-cost="<?php echo $product['cost_price']; ?>"
                         data-product-sku="<?php echo $product['sku']; ?>"
                         data-product-barcode="<?php echo $product['barcode']; ?>"
                         data-product-stock="<?php echo $product['stock_quantity']; ?>"
                         data-track-inventory="<?php echo $product['track_inventory']; ?>"
                         data-allow-backorders="<?php echo $product['allow_backorders']; ?>"
                         data-category="<?php echo $product['category_id']; ?>"
                         data-stock-status="<?php echo $stock_status; ?>">

                        <?php if (!empty($product['image'])) { ?>
                            <img src="<?php echo base_url('uploads/pos_products/' . $product['image']); ?>"
                                 alt="<?php echo $product['name']; ?>" class="product-image">
                        <?php } else { ?>
                            <div class="product-image">
                                <i class="fa fa-cube fa-3x" style="color: #dee2e6;"></i>
                            </div>
                        <?php } ?>

                        <div class="product-name" title="<?php echo htmlspecialchars($product['name']); ?>">
                            <?php echo character_limiter($product['name'], 25); ?>
                        </div>
                        <div class="product-price"><?php echo app_format_money($product['sale_price'], get_base_currency()); ?></div>

                        <?php if ($product['track_inventory']) { ?>
                            <div class="product-stock <?php echo $stock_class; ?>">
                                <?php if ($product['stock_quantity'] <= 0) { ?>
                                    <span class="text-danger"><?php echo _l('out_of_stock'); ?></span>
                                <?php } elseif ($product['stock_quantity'] <= $product['low_stock_threshold']) { ?>
                                    <span class="text-warning"><?php echo _l('stock'); ?>: <?php echo $product['stock_quantity']; ?></span>
                                <?php } else { ?>
                                    <span class="text-success"><?php echo _l('stock'); ?>: <?php echo $product['stock_quantity']; ?></span>
                                <?php } ?>
                            </div>
                        <?php } else { ?>
                            <div class="product-stock">
                                <span class="text-muted"><?php echo _l('not_tracked'); ?></span>
                            </div>
                        <?php } ?>

                        <?php if (!empty($product['barcode'])) { ?>
                            <div class="product-barcode" style="font-size: 10px; color: #6c757d; margin-top: 5px;">
                                <?php echo $product['barcode']; ?>
                            </div>
                        <?php } ?>

                        <!-- Quick add button for touch devices -->
                        <div class="quick-add-btn" style="position: absolute; top: 10px; right: 10px; opacity: 0; transition: opacity 0.2s;">
                            <button class="btn btn-primary btn-xs">
                                <i class="fa fa-plus"></i>
                            </button>
                        </div>
                    </div>
                    <?php } ?>

                    <!-- Loading indicator for dynamic loading -->
                    <div class="loading-products text-center" style="display: none; grid-column: 1 / -1; padding: 40px;">
                        <i class="fa fa-spinner fa-spin fa-2x"></i>
                        <p><?php echo _l('loading_products'); ?></p>
                    </div>

                    <!-- No products found -->
                    <div class="no-products-found text-center" style="display: none; grid-column: 1 / -1; padding: 40px;">
                        <i class="fa fa-search fa-2x text-muted"></i>
                        <p class="text-muted"><?php echo _l('no_products_found'); ?></p>
                    </div>
                </div>
            </div>

            <!-- Cart Section -->
            <div class="pos-cart-section">
                <!-- Customer Selection -->
                <div class="customer-section">
                    <label for="customer-select"><?php echo _l('customer'); ?></label>
                    <div class="input-group">
                        <select class="customer-select form-control" id="customer-select" data-live-search="true">
                            <option value=""><?php echo _l('walk_in_customer'); ?></option>
                            <?php foreach ($customers as $customer) { ?>
                                <option value="<?php echo $customer['userid']; ?>"
                                        data-email="<?php echo isset($customer['email']) ? $customer['email'] : ''; ?>"
                                        data-phone="<?php echo isset($customer['phonenumber']) ? $customer['phonenumber'] : ''; ?>">
                                    <?php echo $customer['company']; ?>
                                </option>
                            <?php } ?>
                        </select>
                        <span class="input-group-btn">
                            <button type="button" class="btn btn-success" id="add-customer-btn">
                                <i class="fa fa-plus"></i>
                            </button>
                        </span>
                    </div>

                    <!-- Customer Info Display -->
                    <div class="customer-info" id="customer-info" style="display: none; margin-top: 10px; padding: 10px; background: #f8f9fa; border-radius: 4px;">
                        <small class="text-muted">
                            <i class="fa fa-user"></i> <span id="customer-name"></span><br>
                            <i class="fa fa-envelope"></i> <span id="customer-email"></span><br>
                            <i class="fa fa-phone"></i> <span id="customer-phone"></span>
                        </small>
                    </div>
                </div>

                <!-- Cart Header -->
                <div class="cart-header">
                    <h5><?php echo _l('cart'); ?> (<span id="cart-count">0</span> <?php echo _l('items'); ?>)</h5>
                    <div class="cart-actions">
                        <button type="button" class="btn btn-sm btn-warning" id="apply-discount-btn" title="<?php echo _l('apply_discount'); ?>">
                            <i class="fa fa-percent"></i>
                        </button>
                        <button type="button" class="btn btn-sm btn-default" id="clear-cart" title="<?php echo _l('clear_cart'); ?>">
                            <i class="fa fa-trash"></i>
                        </button>
                    </div>
                </div>

                <!-- Cart Items -->
                <div class="cart-items" id="cart-items">
                    <div class="empty-cart text-center text-muted" style="padding: 50px 20px;">
                        <i class="fa fa-shopping-cart fa-3x" style="margin-bottom: 15px; opacity: 0.3;"></i>
                        <p><?php echo _l('cart_is_empty'); ?></p>
                        <small><?php echo _l('scan_or_search_products'); ?></small>
                    </div>
                </div>

                <!-- Cart Summary -->
                <div class="cart-summary">
                    <div class="summary-row">
                        <span><?php echo _l('subtotal'); ?>:</span>
                        <span id="cart-subtotal"><?php echo app_format_money(0, get_base_currency()); ?></span>
                    </div>
                    <div class="summary-row" id="discount-row" style="display: none;">
                        <span>
                            <?php echo _l('discount'); ?>
                            (<span id="discount-percentage">0</span>%):
                            <button type="button" class="btn btn-xs btn-link" id="remove-discount" style="padding: 0; margin-left: 5px;">
                                <i class="fa fa-times text-danger"></i>
                            </button>
                        </span>
                        <span id="cart-discount" class="text-success">-<?php echo app_format_money(0, get_base_currency()); ?></span>
                    </div>
                    <div class="summary-row" id="tax-row">
                        <span>
                            <?php echo _l('tax'); ?>
                            (<span id="tax-percentage"><?php echo get_option('default_tax_rate') ?: 0; ?></span>%):
                        </span>
                        <span id="cart-tax"><?php echo app_format_money(0, get_base_currency()); ?></span>
                    </div>
                    <div class="summary-row total">
                        <span><?php echo _l('total'); ?>:</span>
                        <span id="cart-total"><?php echo app_format_money(0, get_base_currency()); ?></span>
                    </div>

                    <!-- Quick calculation display -->
                    <div class="calculation-info" style="margin-top: 10px; padding-top: 10px; border-top: 1px solid #e9ecef;">
                        <small class="text-muted">
                            <span id="items-count">0</span> <?php echo _l('items'); ?> •
                            <?php echo _l('avg_item_price'); ?>: <span id="avg-price"><?php echo app_format_money(0, get_base_currency()); ?></span>
                        </small>
                    </div>
                </div>

                <!-- Payment Methods -->
                <div class="payment-section">
                    <label><?php echo _l('payment_method'); ?></label>
                    <div class="payment-methods">
                        <?php
                        $payment_methods = [
                            'cash' => ['label' => _l('cash'), 'icon' => 'money'],
                            'card' => ['label' => _l('card'), 'icon' => 'credit-card'],
                            'bank_transfer' => ['label' => _l('bank_transfer'), 'icon' => 'bank'],
                            'mobile_payment' => ['label' => _l('mobile_payment'), 'icon' => 'mobile']
                        ];

                        foreach ($payment_methods as $method => $details) { ?>
                        <div class="payment-method <?php echo $method == 'cash' ? 'active' : ''; ?>" data-method="<?php echo $method; ?>">
                            <i class="fa fa-<?php echo $details['icon']; ?>"></i>
                            <span><?php echo $details['label']; ?></span>
                        </div>
                        <?php } ?>
                    </div>

                    <!-- Cash payment details -->
                    <div class="cash-payment-details" id="cash-payment-details" style="margin-top: 15px;">
                        <div class="row">
                            <div class="col-md-6">
                                <label for="amount-received"><?php echo _l('amount_received'); ?></label>
                                <input type="number" class="form-control" id="amount-received" step="0.01" min="0">
                            </div>
                            <div class="col-md-6">
                                <label><?php echo _l('change'); ?></label>
                                <div class="form-control-static">
                                    <strong id="change-amount"><?php echo app_format_money(0, get_base_currency()); ?></strong>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Action Buttons -->
                <div class="pos-actions">
                    <button type="button" class="btn btn-pos btn-pos-success" id="complete-sale" disabled>
                        <i class="fa fa-check"></i> <?php echo _l('complete_sale'); ?>
                        <small style="display: block; font-size: 12px; margin-top: 2px;" id="sale-total-display">
                            <?php echo app_format_money(0, get_base_currency()); ?>
                        </small>
                    </button>

                    <div class="row" style="margin-top: 10px;">
                        <div class="col-md-6">
                            <button type="button" class="btn btn-pos btn-pos-warning" id="hold-order">
                                <i class="fa fa-pause"></i> <?php echo _l('hold_order'); ?>
                            </button>
                        </div>
                        <div class="col-md-6">
                            <button type="button" class="btn btn-pos btn-pos-primary" id="park-sale">
                                <i class="fa fa-bookmark"></i> <?php echo _l('park_sale'); ?>
                            </button>
                        </div>
                    </div>

                    <div class="row" style="margin-top: 10px;">
                        <div class="col-md-6">
                            <button type="button" class="btn btn-pos btn-default" id="print-receipt" style="display: none;">
                                <i class="fa fa-print"></i> <?php echo _l('print'); ?>
                            </button>
                        </div>
                        <div class="col-md-6">
                            <button type="button" class="btn btn-pos btn-default" id="email-receipt" style="display: none;">
                                <i class="fa fa-envelope"></i> <?php echo _l('email'); ?>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Barcode Scanner Input (Hidden) -->
    <input type="text" id="barcode-input" style="position: absolute; left: -9999px;" autocomplete="off">

    <!-- Discount Modal -->
    <div class="modal fade" id="discountModal" tabindex="-1" role="dialog">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                    <h4 class="modal-title"><?php echo _l('apply_discount'); ?></h4>
                </div>
                <div class="modal-body">
                    <div class="form-group">
                        <label for="discount-type"><?php echo _l('discount_type'); ?></label>
                        <select class="form-control" id="discount-type">
                            <option value="percentage"><?php echo _l('percentage'); ?></option>
                            <option value="fixed"><?php echo _l('fixed_amount'); ?></option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="discount-value"><?php echo _l('discount_value'); ?></label>
                        <input type="number" class="form-control" id="discount-value" step="0.01" min="0">
                    </div>
                    <div class="form-group">
                        <label for="discount-reason"><?php echo _l('reason'); ?></label>
                        <input type="text" class="form-control" id="discount-reason" placeholder="<?php echo _l('optional'); ?>">
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal"><?php echo _l('cancel'); ?></button>
                    <button type="button" class="btn btn-primary" id="apply-discount"><?php echo _l('apply'); ?></button>
                </div>
            </div>
        </div>
    </div>

    <!-- Held Orders Modal -->
    <div class="modal fade" id="heldOrdersModal" tabindex="-1" role="dialog">
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                    <h4 class="modal-title"><?php echo _l('held_orders'); ?></h4>
                </div>
                <div class="modal-body">
                    <div class="table-responsive">
                        <table class="table table-striped" id="held-orders-table">
                            <thead>
                                <tr>
                                    <th><?php echo _l('order_number'); ?></th>
                                    <th><?php echo _l('customer'); ?></th>
                                    <th><?php echo _l('items'); ?></th>
                                    <th><?php echo _l('total'); ?></th>
                                    <th><?php echo _l('date'); ?></th>
                                    <th><?php echo _l('actions'); ?></th>
                                </tr>
                            </thead>
                            <tbody id="held-orders-list">
                                <!-- Held orders will be loaded here -->
                            </tbody>
                        </table>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal"><?php echo _l('close'); ?></button>
                </div>
            </div>
        </div>
    </div>

    <!-- Calculator Modal -->
    <div class="modal fade" id="calculatorModal" tabindex="-1" role="dialog">
        <div class="modal-dialog modal-sm" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                    <h4 class="modal-title"><?php echo _l('calculator'); ?></h4>
                </div>
                <div class="modal-body">
                    <div class="calculator">
                        <input type="text" class="form-control calculator-display" id="calculator-display" readonly>
                        <div class="calculator-buttons" style="margin-top: 15px;">
                            <div class="row">
                                <div class="col-xs-3"><button class="btn btn-default btn-block calc-btn" data-value="7">7</button></div>
                                <div class="col-xs-3"><button class="btn btn-default btn-block calc-btn" data-value="8">8</button></div>
                                <div class="col-xs-3"><button class="btn btn-default btn-block calc-btn" data-value="9">9</button></div>
                                <div class="col-xs-3"><button class="btn btn-warning btn-block calc-btn" data-value="/">/</button></div>
                            </div>
                            <div class="row" style="margin-top: 5px;">
                                <div class="col-xs-3"><button class="btn btn-default btn-block calc-btn" data-value="4">4</button></div>
                                <div class="col-xs-3"><button class="btn btn-default btn-block calc-btn" data-value="5">5</button></div>
                                <div class="col-xs-3"><button class="btn btn-default btn-block calc-btn" data-value="6">6</button></div>
                                <div class="col-xs-3"><button class="btn btn-warning btn-block calc-btn" data-value="*">×</button></div>
                            </div>
                            <div class="row" style="margin-top: 5px;">
                                <div class="col-xs-3"><button class="btn btn-default btn-block calc-btn" data-value="1">1</button></div>
                                <div class="col-xs-3"><button class="btn btn-default btn-block calc-btn" data-value="2">2</button></div>
                                <div class="col-xs-3"><button class="btn btn-default btn-block calc-btn" data-value="3">3</button></div>
                                <div class="col-xs-3"><button class="btn btn-warning btn-block calc-btn" data-value="-">-</button></div>
                            </div>
                            <div class="row" style="margin-top: 5px;">
                                <div class="col-xs-3"><button class="btn btn-default btn-block calc-btn" data-value="0">0</button></div>
                                <div class="col-xs-3"><button class="btn btn-default btn-block calc-btn" data-value=".">.</button></div>
                                <div class="col-xs-3"><button class="btn btn-danger btn-block" id="calc-clear">C</button></div>
                                <div class="col-xs-3"><button class="btn btn-warning btn-block calc-btn" data-value="+">+</button></div>
                            </div>
                            <div class="row" style="margin-top: 5px;">
                                <div class="col-xs-12"><button class="btn btn-success btn-block" id="calc-equals">=</button></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Add Customer Modal -->
    <div class="modal fade" id="addCustomerModal" tabindex="-1" role="dialog">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                    <h4 class="modal-title"><?php echo _l('add_new_customer'); ?></h4>
                </div>
                <form id="add-customer-form">
                    <div class="modal-body">
                        <div class="form-group">
                            <label for="customer-company"><?php echo _l('company_name'); ?> <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="customer-company" name="company" required>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="customer-email"><?php echo _l('email'); ?></label>
                                    <input type="email" class="form-control" id="customer-email" name="email">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="customer-phone"><?php echo _l('phone'); ?></label>
                                    <input type="text" class="form-control" id="customer-phone" name="phonenumber">
                                </div>
                            </div>
                        </div>
                        <div class="form-group">
                            <label for="customer-address"><?php echo _l('address'); ?></label>
                            <textarea class="form-control" id="customer-address" name="address" rows="3"></textarea>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-default" data-dismiss="modal"><?php echo _l('cancel'); ?></button>
                        <button type="submit" class="btn btn-primary"><?php echo _l('add_customer'); ?></button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- JavaScript -->
    <script src="<?php echo base_url('assets/plugins/jquery/jquery.min.js'); ?>"></script>
    <script src="<?php echo base_url('assets/plugins/bootstrap/js/bootstrap.min.js'); ?>"></script>
    <script>
        var admin_url = '<?php echo admin_url(); ?>';
        var currency_symbol = '<?php echo get_base_currency()->symbol; ?>';
        var default_tax_rate = <?php echo get_option('default_tax_rate') ?: 0; ?>;
        var current_location_id = <?php echo $current_location['id']; ?>;
        var lang = {
            'all_categories': '<?php echo _l('all_categories'); ?>',
            'product_not_found': '<?php echo _l('product_not_found'); ?>',
            'cart_is_empty': '<?php echo _l('cart_is_empty'); ?>',
            'sale_completed': '<?php echo _l('sale_completed'); ?>',
            'order_held': '<?php echo _l('order_held'); ?>',
            'insufficient_stock': '<?php echo _l('insufficient_stock'); ?>',
            'out_of_stock': '<?php echo _l('out_of_stock'); ?>',
            'confirm_clear_cart': '<?php echo _l('confirm_clear_cart'); ?>',
            'confirm_remove_item': '<?php echo _l('confirm_remove_item'); ?>',
            'discount_applied': '<?php echo _l('discount_applied'); ?>',
            'invalid_discount': '<?php echo _l('invalid_discount'); ?>',
            'customer_added': '<?php echo _l('customer_added_successfully'); ?>',
            'error_adding_customer': '<?php echo _l('error_adding_customer'); ?>',
            'loading': '<?php echo _l('loading'); ?>',
            'no_held_orders': '<?php echo _l('no_held_orders'); ?>',
            'order_retrieved': '<?php echo _l('order_retrieved_successfully'); ?>',
            'error_retrieving_order': '<?php echo _l('error_retrieving_order'); ?>',
            'payment_required': '<?php echo _l('payment_amount_required'); ?>',
            'insufficient_payment': '<?php echo _l('insufficient_payment_amount'); ?>'
        };
    </script>
    <script src="<?php echo module_dir_url(POS_INVENTORY_MODULE_NAME, 'assets/js/pos_inventory.js'); ?>"></script>
    
    <script>
    $(document).ready(function() {
        // Initialize POS
        POS.init();

        // Category filter
        $('.category-filter').click(function(e) {
            e.preventDefault();
            var category = $(this).data('category');

            $('.category-filter').parent().removeClass('active');
            $(this).parent().addClass('active');

            POS.filterProductsByCategory(category);
        });

        // Product search
        $('#product-search').on('input', function() {
            var searchTerm = $(this).val().toLowerCase();
            POS.searchProducts(searchTerm);
        });

        // Barcode scanner handling
        $('#barcode-input').on('input', function() {
            var barcode = $(this).val();
            if (barcode.length >= 8) { // Minimum barcode length
                POS.searchByBarcode(barcode);
                $(this).val(''); // Clear input
            }
        });

        // Focus barcode input for scanner
        $(document).click(function(e) {
            if (!$(e.target).is('input, textarea, select')) {
                $('#barcode-input').focus();
            }
        });

        // Customer selection change
        $('#customer-select').change(function() {
            POS.updateCustomerInfo();
        });

        // Discount modal
        $('#apply-discount-btn').click(function() {
            $('#discountModal').modal('show');
        });

        $('#apply-discount').click(function() {
            POS.applyDiscount();
        });

        $('#remove-discount').click(function() {
            POS.removeDiscount();
        });

        // Held orders
        $('#held-orders-btn').click(function() {
            POS.loadHeldOrders();
        });

        // Calculator
        $('#calculator-btn').click(function() {
            $('#calculatorModal').modal('show');
        });

        // Calculator functionality
        $('.calc-btn').click(function() {
            var value = $(this).data('value');
            var display = $('#calculator-display');
            display.val(display.val() + value);
        });

        $('#calc-clear').click(function() {
            $('#calculator-display').val('');
        });

        $('#calc-equals').click(function() {
            try {
                var result = eval($('#calculator-display').val());
                $('#calculator-display').val(result);
            } catch (e) {
                $('#calculator-display').val('Error');
            }
        });

        // Add customer
        $('#add-customer-btn').click(function() {
            $('#addCustomerModal').modal('show');
        });

        $('#add-customer-form').submit(function(e) {
            e.preventDefault();
            POS.addCustomer();
        });

        // Cash payment calculation
        $('#amount-received').on('input', function() {
            POS.calculateChange();
        });

        // Payment method selection
        $('.payment-method').click(function() {
            $('.payment-method').removeClass('active');
            $(this).addClass('active');
            POS.paymentMethod = $(this).data('method');

            if (POS.paymentMethod === 'cash') {
                $('#cash-payment-details').show();
            } else {
                $('#cash-payment-details').hide();
            }
        });

        // Auto-focus barcode input
        $('#barcode-input').focus();

        // Load held orders count
        POS.updateHeldOrdersCount();

        // Keyboard shortcuts
        $(document).keydown(function(e) {
            // F1 - Complete Sale
            if (e.which === 112) {
                e.preventDefault();
                if (!$('#complete-sale').prop('disabled')) {
                    POS.completeSale();
                }
            }
            // F2 - Hold Order
            else if (e.which === 113) {
                e.preventDefault();
                POS.holdOrder();
            }
            // F3 - Clear Cart
            else if (e.which === 114) {
                e.preventDefault();
                POS.clearCart();
            }
            // F4 - Held Orders
            else if (e.which === 115) {
                e.preventDefault();
                POS.loadHeldOrders();
            }
            // Escape - Clear search
            else if (e.which === 27) {
                $('#product-search').val('').trigger('input');
                $('#barcode-input').focus();
            }
        });
    });
    </script>
</body>
</html>
