/**
 * POS Inventory Module - Dashboard JavaScript
 * Handles dashboard-specific functionality
 */

// Ensure jQuery is available
if (typeof $ === 'undefined' && typeof jQuery !== 'undefined') {
    var $ = jQuery;
}

var Dashboard = Dashboard || {};

/**
 * Dashboard functionality
 */
Dashboard = {
    
    /**
     * Initialize dashboard
     */
    init: function() {
        this.loadStatistics();
        this.initCharts();
        this.initRefreshTimer();
    },
    
    /**
     * Load dashboard statistics
     */
    loadStatistics: function() {
        $.get(admin_url + 'pos_inventory/dashboard_stats', function(response) {
            if (response.success) {
                var stats = response.stats;
                
                // Update KPI cards
                $('#total-products').text(stats.total_products || 0);
                $('#total-categories').text(stats.total_categories || 0);
                $('#low-stock-items').text(stats.low_stock_items || 0);
                $('#total-locations').text(stats.total_locations || 0);
                $('#total-stock-value').text(stats.total_stock_value || '$0.00');
                $('#recent-transactions').text(stats.recent_transactions || 0);
            }
        }, 'json').fail(function() {
            console.log('Failed to load dashboard statistics');
        });
    },
    
    /**
     * Initialize charts
     */
    initCharts: function() {
        this.initStockLevelsChart();
        this.initCategoryDistributionChart();
        this.initRecentActivityChart();
    },
    
    /**
     * Initialize stock levels chart
     */
    initStockLevelsChart: function() {
        if ($('#stock-levels-chart').length) {
            $.get(admin_url + 'pos_inventory/stock_levels_chart', function(response) {
                if (response.success && typeof Chart !== 'undefined') {
                    var ctx = document.getElementById('stock-levels-chart').getContext('2d');
                    new Chart(ctx, {
                        type: 'doughnut',
                        data: {
                            labels: ['In Stock', 'Low Stock', 'Out of Stock'],
                            datasets: [{
                                data: [
                                    response.data.in_stock || 0,
                                    response.data.low_stock || 0,
                                    response.data.out_of_stock || 0
                                ],
                                backgroundColor: ['#28a745', '#ffc107', '#dc3545'],
                                borderWidth: 2,
                                borderColor: '#fff'
                            }]
                        },
                        options: {
                            responsive: true,
                            maintainAspectRatio: false,
                            plugins: {
                                legend: {
                                    position: 'bottom'
                                }
                            }
                        }
                    });
                }
            }, 'json');
        }
    },
    
    /**
     * Initialize category distribution chart
     */
    initCategoryDistributionChart: function() {
        if ($('#category-distribution-chart').length) {
            $.get(admin_url + 'pos_inventory/category_distribution_chart', function(response) {
                if (response.success && typeof Chart !== 'undefined') {
                    var ctx = document.getElementById('category-distribution-chart').getContext('2d');
                    new Chart(ctx, {
                        type: 'bar',
                        data: {
                            labels: response.data.labels || [],
                            datasets: [{
                                label: 'Products',
                                data: response.data.values || [],
                                backgroundColor: '#007bff',
                                borderColor: '#0056b3',
                                borderWidth: 1
                            }]
                        },
                        options: {
                            responsive: true,
                            maintainAspectRatio: false,
                            scales: {
                                y: {
                                    beginAtZero: true,
                                    ticks: {
                                        stepSize: 1
                                    }
                                }
                            },
                            plugins: {
                                legend: {
                                    display: false
                                }
                            }
                        }
                    });
                }
            }, 'json');
        }
    },
    
    /**
     * Initialize recent activity chart
     */
    initRecentActivityChart: function() {
        if ($('#recent-activity-chart').length) {
            $.get(admin_url + 'pos_inventory/recent_activity_chart', function(response) {
                if (response.success && typeof Chart !== 'undefined') {
                    var ctx = document.getElementById('recent-activity-chart').getContext('2d');
                    new Chart(ctx, {
                        type: 'line',
                        data: {
                            labels: response.data.labels || [],
                            datasets: [{
                                label: 'Stock Movements',
                                data: response.data.values || [],
                                borderColor: '#28a745',
                                backgroundColor: 'rgba(40, 167, 69, 0.1)',
                                borderWidth: 2,
                                fill: true,
                                tension: 0.4
                            }]
                        },
                        options: {
                            responsive: true,
                            maintainAspectRatio: false,
                            scales: {
                                y: {
                                    beginAtZero: true
                                }
                            },
                            plugins: {
                                legend: {
                                    display: false
                                }
                            }
                        }
                    });
                }
            }, 'json');
        }
    },
    
    /**
     * Initialize auto-refresh timer
     */
    initRefreshTimer: function() {
        // Refresh dashboard every 5 minutes
        setInterval(function() {
            Dashboard.loadStatistics();
        }, 300000);
    },
    
    /**
     * Refresh dashboard manually
     */
    refresh: function() {
        this.loadStatistics();
        this.initCharts();
    }
};

// Initialize when document is ready
$(document).ready(function() {
    if ($('.pos-dashboard').length) {
        Dashboard.init();
    }
});

// Export for global use
window.Dashboard = Dashboard;
