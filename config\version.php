<?php

defined('BASEPATH') or exit('No direct script access allowed');

/**
 * Advanced POS & Central Inventory Module Version Management
 * 
 * This file contains version constants and migration tracking
 * for the POS Inventory module
 */

// Module Version Information
define('POS_INVENTORY_VERSION', '1.0.0');
define('POS_INVENTORY_VERSION_CODE', 100); // Used for migrations and database versioning
define('POS_INVENTORY_MIN_PERFEX_VERSION', '2.0.0'); // Lowered requirement to be more compatible
define('POS_INVENTORY_MIN_PHP_VERSION', '7.4.0');
define('POS_INVENTORY_MIN_MYSQL_VERSION', '5.7.0');

// Database Schema Version
define('POS_INVENTORY_DB_SCHEMA_VERSION', 100);

// Migration Information
if (!defined('POS_INVENTORY_MIGRATION_GROUP')) {
    define('POS_INVENTORY_MIGRATION_GROUP', 'pos_inventory');
}
define('POS_INVENTORY_MIGRATION_NAMESPACE', '');

// Version History
$pos_inventory_version_history = [
    '1.0.0' => [
        'version_code' => 100,
        'release_date' => '2024-01-01',
        'description' => 'Initial release with core POS and inventory management features',
        'features' => [
            'Point of Sale interface',
            'Inventory management',
            'Purchase order system',
            'WooCommerce integration',
            'Shopify integration framework',
            'Multi-location support',
            'Barcode scanning',
            'Receipt generation',
            'Reports and analytics'
        ],
        'database_changes' => [
            'Created all core tables',
            'Added foreign key constraints',
            'Added performance indexes'
        ],
        'migration_files' => [
            '100_version_100_initial_setup.php',
            '101_version_101_pos_transactions.php'
        ]
    ]
];

// Store version history in global variable
$GLOBALS['pos_inventory_version_history'] = $pos_inventory_version_history;

/**
 * Get current module version
 * 
 * @return string Current version
 */
function pos_inventory_get_version()
{
    return POS_INVENTORY_VERSION;
}

/**
 * Get current version code
 * 
 * @return int Current version code
 */
function pos_inventory_get_version_code()
{
    return POS_INVENTORY_VERSION_CODE;
}

/**
 * Get database schema version
 * 
 * @return int Database schema version
 */
function pos_inventory_get_db_schema_version()
{
    return POS_INVENTORY_DB_SCHEMA_VERSION;
}

/**
 * Get version history
 * 
 * @return array Version history
 */
function pos_inventory_get_version_history()
{
    global $pos_inventory_version_history;
    return $pos_inventory_version_history;
}

/**
 * Get version information for a specific version
 * 
 * @param string $version Version number
 * @return array|null Version information or null if not found
 */
function pos_inventory_get_version_info($version)
{
    global $pos_inventory_version_history;
    return isset($pos_inventory_version_history[$version]) ? $pos_inventory_version_history[$version] : null;
}

/**
 * Check if system meets minimum requirements
 *
 * @return array Requirements check result
 */
function pos_inventory_check_requirements()
{
    // Get Perfex CRM version using multiple methods
    $perfex_version = pos_inventory_get_perfex_version();

    $requirements = [
        'php_version' => [
            'required' => POS_INVENTORY_MIN_PHP_VERSION,
            'current' => PHP_VERSION,
            'met' => version_compare(PHP_VERSION, POS_INVENTORY_MIN_PHP_VERSION, '>=')
        ],
        'perfex_version' => [
            'required' => POS_INVENTORY_MIN_PERFEX_VERSION,
            'current' => $perfex_version,
            'met' => version_compare($perfex_version, POS_INVENTORY_MIN_PERFEX_VERSION, '>=')
        ]
    ];

    // Check MySQL version if available
    $CI = &get_instance();
    if ($CI->db->conn_id) {
        try {
            $mysql_version = $CI->db->query('SELECT VERSION() as version')->row()->version;
            $requirements['mysql_version'] = [
                'required' => POS_INVENTORY_MIN_MYSQL_VERSION,
                'current' => $mysql_version,
                'met' => version_compare($mysql_version, POS_INVENTORY_MIN_MYSQL_VERSION, '>=')
            ];
        } catch (Exception $e) {
            // MySQL version check failed, but don't block activation
            log_message('warning', 'Could not check MySQL version: ' . $e->getMessage());
        }
    }

    return $requirements;
}

/**
 * Get Perfex CRM version using multiple detection methods
 *
 * @return string Perfex CRM version
 */
function pos_inventory_get_perfex_version()
{
    // Method 1: Try get_option function if available
    if (function_exists('get_option')) {
        $version = get_option('perfex_current_version');
        if (!empty($version)) {
            return $version;
        }
    }

    // Method 2: Check migrations table for highest version
    $CI = &get_instance();
    if ($CI->db->table_exists(db_prefix() . 'migrations')) {
        try {
            $migration = $CI->db->select('version')
                               ->from(db_prefix() . 'migrations')
                               ->where('`group`', '')
                               ->order_by('version', 'DESC')
                               ->limit(1)
                               ->get()
                               ->row();

            if ($migration && $migration->version) {
                // Convert migration version to semantic version
                $version_code = (int)$migration->version;
                if ($version_code >= 321) return '3.2.1';
                if ($version_code >= 320) return '3.2.0';
                if ($version_code >= 310) return '3.1.0';
                if ($version_code >= 300) return '3.0.0';
                if ($version_code >= 280) return '2.8.0';
                if ($version_code >= 270) return '2.7.0';
                if ($version_code >= 260) return '2.6.0';
                if ($version_code >= 250) return '2.5.0';
                if ($version_code >= 240) return '2.4.0';
                if ($version_code >= 230) return '2.3.0';
                if ($version_code >= 220) return '2.2.0';
                if ($version_code >= 210) return '2.1.0';
                if ($version_code >= 200) return '2.0.0';
            }
        } catch (Exception $e) {
            // Use safe logging if available
            if (function_exists('pos_inventory_log_warning')) {
                pos_inventory_log_warning('Could not check migrations table: ' . $e->getMessage());
            }
        }
    }

    // Method 3: Assume minimum compatible version if we can't detect
    if (function_exists('pos_inventory_log_warning')) {
        pos_inventory_log_warning('Could not detect Perfex CRM version, assuming 3.2.1');
    }
    return '3.2.1';
}

/**
 * Check if all requirements are met
 *
 * @return bool True if all requirements are met
 */
function pos_inventory_requirements_met()
{
    $requirements = pos_inventory_check_requirements();

    foreach ($requirements as $requirement) {
        if (!$requirement['met']) {
            return false;
        }
    }

    return true;
}

/**
 * Get installed version from database
 * 
 * @return string|null Installed version or null if not found
 */
function pos_inventory_get_installed_version()
{
    $CI = &get_instance();
    
    // Try to get from modules table first
    $module = $CI->db->get_where(db_prefix() . 'modules', ['module_name' => 'pos_inventory'])->row();
    if (!$module) {
        $module = $CI->db->get_where(db_prefix() . 'modules', ['system_name' => 'pos_inventory'])->row();
    }
    
    if ($module && isset($module->installed_version)) {
        return $module->installed_version;
    }
    
    // Fallback to migration table
    $migration = $CI->db->select('version')
                       ->from(db_prefix() . 'migrations')
                       ->where('`group`', POS_INVENTORY_MIGRATION_GROUP)
                       ->order_by('version', 'DESC')
                       ->limit(1)
                       ->get()
                       ->row();
    
    if ($migration) {
        // Convert version code back to version string
        $version_code = (int)$migration->version;
        foreach ($pos_inventory_version_history as $version => $info) {
            if ($info['version_code'] == $version_code) {
                return $version;
            }
        }
    }
    
    return null;
}

/**
 * Check if module needs upgrade
 * 
 * @return bool True if upgrade is needed
 */
function pos_inventory_needs_upgrade()
{
    $installed_version = pos_inventory_get_installed_version();
    
    if (!$installed_version) {
        return false; // Not installed
    }
    
    return version_compare($installed_version, POS_INVENTORY_VERSION, '<');
}

/**
 * Get upgrade path from current version to latest
 * 
 * @param string $from_version Starting version
 * @return array Upgrade steps
 */
function pos_inventory_get_upgrade_path($from_version)
{
    global $pos_inventory_version_history;
    
    $upgrade_steps = [];
    
    foreach ($pos_inventory_version_history as $version => $info) {
        if (version_compare($version, $from_version, '>') && 
            version_compare($version, POS_INVENTORY_VERSION, '<=')) {
            $upgrade_steps[] = [
                'version' => $version,
                'version_code' => $info['version_code'],
                'description' => $info['description'],
                'migration_files' => $info['migration_files'] ?? []
            ];
        }
    }
    
    // Sort by version
    usort($upgrade_steps, function($a, $b) {
        return version_compare($a['version'], $b['version']);
    });
    
    return $upgrade_steps;
}

/**
 * Log version information
 */
function pos_inventory_log_version_info()
{
    $installed_version = pos_inventory_get_installed_version();
    $needs_upgrade = pos_inventory_needs_upgrade();

    // Use safe logging if available
    if (function_exists('pos_inventory_log_info')) {
        pos_inventory_log_info('POS Inventory Module Version Info:');
        pos_inventory_log_info('  Current Version: ' . POS_INVENTORY_VERSION);
        pos_inventory_log_info('  Installed Version: ' . ($installed_version ?: 'Not installed'));
        pos_inventory_log_info('  Needs Upgrade: ' . ($needs_upgrade ? 'Yes' : 'No'));
        pos_inventory_log_info('  DB Schema Version: ' . POS_INVENTORY_DB_SCHEMA_VERSION);
    } elseif (function_exists('log_message')) {
        // Fallback to standard logging with error handling
        try {
            log_message('info', '[POS_INVENTORY] Module Version Info:');
            log_message('info', '[POS_INVENTORY]   Current Version: ' . POS_INVENTORY_VERSION);
            log_message('info', '[POS_INVENTORY]   Installed Version: ' . ($installed_version ?: 'Not installed'));
            log_message('info', '[POS_INVENTORY]   Needs Upgrade: ' . ($needs_upgrade ? 'Yes' : 'No'));
            log_message('info', '[POS_INVENTORY]   DB Schema Version: ' . POS_INVENTORY_DB_SCHEMA_VERSION);
        } catch (Exception $e) {
            // Ignore logging errors
        }
    }
}

// Log version information when this file is loaded (only if not in CLI mode)
if (function_exists('log_message') && !is_cli()) {
    try {
        pos_inventory_log_version_info();
    } catch (Exception $e) {
        // Silently ignore logging errors to prevent activation issues
    }
}
