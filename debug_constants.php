<?php

// Debug script to check what constants are actually loaded
define('BASEPATH', true);

echo "<h2>POS Inventory Module - Constants Debug</h2>\n";

// Load our module files
require_once('pos_inventory.php');

echo "<h3>Constants Check:</h3>\n";

$constants_to_check = [
    'POS_INVENTORY_VERSION',
    'POS_INVENTORY_MIN_PERFEX_VERSION',
    'POS_INVENTORY_MIN_PHP_VERSION',
    'POS_INVENTORY_MIGRATION_GROUP'
];

foreach ($constants_to_check as $constant) {
    if (defined($constant)) {
        echo "✅ $constant = " . constant($constant) . "<br>\n";
    } else {
        echo "❌ $constant = NOT DEFINED<br>\n";
    }
}

echo "<h3>File Modification Times:</h3>\n";
$files_to_check = [
    'pos_inventory.php',
    'config/version.php',
    'config/module_config.php'
];

foreach ($files_to_check as $file) {
    if (file_exists($file)) {
        $mtime = filemtime($file);
        echo "📄 $file = " . date('Y-m-d H:i:s', $mtime) . "<br>\n";
    } else {
        echo "❌ $file = NOT FOUND<br>\n";
    }
}

echo "<h3>Function Availability:</h3>\n";
$functions_to_check = [
    'pos_inventory_check_requirements',
    'pos_inventory_requirements_met',
    'pos_inventory_get_perfex_version'
];

foreach ($functions_to_check as $function) {
    if (function_exists($function)) {
        echo "✅ $function = AVAILABLE<br>\n";
    } else {
        echo "❌ $function = NOT AVAILABLE<br>\n";
    }
}

// Test the actual requirement check
echo "<h3>Live Requirements Test:</h3>\n";
if (function_exists('pos_inventory_check_requirements')) {
    try {
        $requirements = pos_inventory_check_requirements();
        foreach ($requirements as $name => $req) {
            $status = $req['met'] ? '✅' : '❌';
            echo "$status $name: current='{$req['current']}', required='{$req['required']}', met=" . ($req['met'] ? 'true' : 'false') . "<br>\n";
        }
    } catch (Exception $e) {
        echo "❌ Error checking requirements: " . $e->getMessage() . "<br>\n";
    }
} else {
    echo "❌ pos_inventory_check_requirements function not available<br>\n";
}

?>
