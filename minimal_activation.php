<?php

// Minimal activation script - only core functionality
define('BASEPATH', true);

// Include Perfex CRM bootstrap
require_once('../../index.php');

echo "<h2>POS Inventory - Minimal Activation</h2>\n";

$CI = &get_instance();

echo "<h3>Step 1: Create Core Tables Only</h3>\n";

// Create only the essential tables
$core_tables = [
    'pos_categories' => 'CREATE TABLE `' . db_prefix() . 'pos_categories` (
        `id` int(11) NOT NULL AUTO_INCREMENT,
        `name` varchar(191) NOT NULL,
        `description` text,
        `status` tinyint(1) NOT NULL DEFAULT 1,
        `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
        PRIMARY KEY (`id`)
    ) ENGINE=InnoDB DEFAULT CHARSET=' . $CI->db->char_set,
    
    'pos_products' => 'CREATE TABLE `' . db_prefix() . 'pos_products` (
        `id` int(11) NOT NULL AUTO_INCREMENT,
        `name` varchar(191) NOT NULL,
        `sku` varchar(100) NOT NULL,
        `category_id` int(11) DEFAULT NULL,
        `cost_price` decimal(15,2) DEFAULT 0.00,
        `sale_price` decimal(15,2) NOT NULL DEFAULT 0.00,
        `status` tinyint(1) NOT NULL DEFAULT 1,
        `created_by` int(11) NOT NULL DEFAULT 1,
        `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
        PRIMARY KEY (`id`),
        UNIQUE KEY `sku` (`sku`)
    ) ENGINE=InnoDB DEFAULT CHARSET=' . $CI->db->char_set,
    
    'pos_locations' => 'CREATE TABLE `' . db_prefix() . 'pos_locations` (
        `id` int(11) NOT NULL AUTO_INCREMENT,
        `name` varchar(191) NOT NULL,
        `is_default` tinyint(1) NOT NULL DEFAULT 0,
        `status` tinyint(1) NOT NULL DEFAULT 1,
        `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
        PRIMARY KEY (`id`)
    ) ENGINE=InnoDB DEFAULT CHARSET=' . $CI->db->char_set,
    
    'pos_inventory' => 'CREATE TABLE `' . db_prefix() . 'pos_inventory` (
        `id` int(11) NOT NULL AUTO_INCREMENT,
        `product_id` int(11) NOT NULL,
        `location_id` int(11) NOT NULL,
        `quantity` int(11) NOT NULL DEFAULT 0,
        `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        PRIMARY KEY (`id`),
        UNIQUE KEY `product_location` (`product_id`, `location_id`)
    ) ENGINE=InnoDB DEFAULT CHARSET=' . $CI->db->char_set
];

foreach ($core_tables as $table_name => $sql) {
    if (!$CI->db->table_exists(db_prefix() . $table_name)) {
        try {
            $result = $CI->db->query($sql);
            if ($result) {
                echo "✅ Created $table_name<br>\n";
            } else {
                echo "❌ Failed to create $table_name<br>\n";
            }
        } catch (Exception $e) {
            echo "❌ Error creating $table_name: " . $e->getMessage() . "<br>\n";
        }
    } else {
        echo "ℹ️ $table_name already exists<br>\n";
    }
}

echo "<h3>Step 2: Insert Default Data</h3>\n";

// Create default category
if ($CI->db->table_exists(db_prefix() . 'pos_categories')) {
    $existing_category = $CI->db->get(db_prefix() . 'pos_categories')->row();
    if (!$existing_category) {
        $CI->db->insert(db_prefix() . 'pos_categories', [
            'name' => 'General',
            'description' => 'Default category',
            'status' => 1
        ]);
        echo "✅ Default category created<br>\n";
    } else {
        echo "ℹ️ Category already exists<br>\n";
    }
}

// Create default location
if ($CI->db->table_exists(db_prefix() . 'pos_locations')) {
    $existing_location = $CI->db->get(db_prefix() . 'pos_locations')->row();
    if (!$existing_location) {
        $CI->db->insert(db_prefix() . 'pos_locations', [
            'name' => 'Main Store',
            'is_default' => 1,
            'status' => 1
        ]);
        echo "✅ Default location created<br>\n";
    } else {
        echo "ℹ️ Location already exists<br>\n";
    }
}

echo "<h3>Step 3: Register Module</h3>\n";

// Register module in database
$existing_module = $CI->db->get_where(db_prefix() . 'modules', ['module_name' => 'pos_inventory'])->row();
if (!$existing_module) {
    $existing_module = $CI->db->get_where(db_prefix() . 'modules', ['system_name' => 'pos_inventory'])->row();
}

if ($existing_module) {
    // Update existing module
    $where_field = isset($existing_module->module_name) ? 'module_name' : 'system_name';
    $CI->db->where($where_field, 'pos_inventory');
    $CI->db->update(db_prefix() . 'modules', [
        'active' => 1,
        'installed_version' => '1.0.0'
    ]);
    echo "✅ Module updated in database<br>\n";
} else {
    // Insert new module record
    $CI->db->insert(db_prefix() . 'modules', [
        'module_name' => 'pos_inventory',
        'system_name' => 'pos_inventory',
        'active' => 1,
        'installed_version' => '1.0.0'
    ]);
    echo "✅ Module registered in database<br>\n";
}

echo "<h3>Step 4: Create Migration Record</h3>\n";

// Create migration record
try {
    // Check available columns in migrations table
    $migration_fields = $CI->db->field_data(db_prefix() . 'migrations');
    $available_columns = [];
    foreach ($migration_fields as $field) {
        $available_columns[] = $field->name;
    }

    // Build where clause based on available columns
    $where_clause = ['version' => 100];
    if (in_array('group', $available_columns)) {
        $where_clause['`group`'] = 'pos_inventory';
    }

    $existing_migration = $CI->db->get_where(db_prefix() . 'migrations', $where_clause)->row();
    if (!$existing_migration) {
        // Start with only the version (which should always exist)
        $migration_data = [];

        // Add columns only if they exist in the table
        if (in_array('version', $available_columns)) {
            $migration_data['version'] = 100;
        }

        if (in_array('time', $available_columns)) {
            $migration_data['time'] = time();
        }

        if (in_array('timestamp', $available_columns)) {
            $migration_data['timestamp'] = time();
        }

        if (in_array('batch', $available_columns)) {
            $migration_data['batch'] = 1;
        }

        if (in_array('class', $available_columns)) {
            $migration_data['class'] = 'Migration_Version_100';
        }

        if (in_array('namespace', $available_columns)) {
            $migration_data['namespace'] = '';
        }

        if (in_array('group', $available_columns)) {
            $migration_data['`group`'] = 'pos_inventory';
        }

        // Only insert if we have at least the version
        if (!empty($migration_data)) {
            try {
                $CI->db->insert(db_prefix() . 'migrations', $migration_data);
                echo "✅ Migration record created<br>\n";
            } catch (Exception $insert_e) {
                echo "⚠️ Migration record creation failed: " . $insert_e->getMessage() . "<br>\n";
            }
        } else {
            echo "⚠️ No compatible migration table structure found<br>\n";
        }
    } else {
        echo "ℹ️ Migration record already exists<br>\n";
    }
} catch (Exception $e) {
    echo "⚠️ Migration record creation failed: " . $e->getMessage() . "<br>\n";
}

echo "<h3>Final Verification</h3>\n";

// Check core tables
$core_table_names = ['pos_categories', 'pos_products', 'pos_locations', 'pos_inventory'];
$all_core_exist = true;

foreach ($core_table_names as $table) {
    $exists = $CI->db->table_exists(db_prefix() . $table);
    $status = $exists ? '✅' : '❌';
    echo "$status " . db_prefix() . $table . "<br>\n";
    if (!$exists) $all_core_exist = false;
}

// Check module status
$module = $CI->db->get_where(db_prefix() . 'modules', ['module_name' => 'pos_inventory'])->row();
if (!$module) {
    $module = $CI->db->get_where(db_prefix() . 'modules', ['system_name' => 'pos_inventory'])->row();
}

if ($module && $module->active) {
    echo "✅ Module is active<br>\n";
} else {
    echo "❌ Module is not active<br>\n";
}

if ($all_core_exist && $module && $module->active) {
    echo "<br>🎉 <strong>Minimal activation completed successfully!</strong><br>\n";
    echo "<a href='../../admin/modules'>← Back to Modules</a><br>\n";
} else {
    echo "<br>⚠️ <strong>Activation completed with issues.</strong><br>\n";
}

?>
