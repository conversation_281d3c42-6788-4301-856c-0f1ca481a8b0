<?php

defined('BASEPATH') or exit('No direct script access allowed');

/**
 * POS Inventory Logging Helper
 * 
 * This helper provides safe logging functions that prevent
 * "Undefined array key WARNING" errors in CodeIgniter's Log.php
 */

if (!function_exists('pos_inventory_safe_log')) {
    /**
     * Safe logging function that prevents undefined array key errors
     * 
     * @param string $level Log level (error, debug, info, warning)
     * @param string $message Log message
     * @return bool True if successful, false otherwise
     */
    function pos_inventory_safe_log($level, $message)
    {
        // Validate log level
        $valid_levels = ['error', 'debug', 'info', 'warning'];
        if (!in_array(strtolower($level), $valid_levels)) {
            $level = 'info';
        }
        
        // Ensure we're not in CLI mode unless explicitly allowed
        if (is_cli() && !defined('POS_INVENTORY_ALLOW_CLI_LOGGING')) {
            return true; // Skip logging in CLI mode
        }
        
        // Check if logging function exists
        if (!function_exists('log_message')) {
            return false;
        }
        
        try {
            // Get CodeIgniter instance
            $CI = &get_instance();
            
            // Check if logging is enabled
            if (isset($CI->config) && method_exists($CI->config, 'item')) {
                $log_threshold = $CI->config->item('log_threshold');
                if ($log_threshold === 0) {
                    return true; // Logging disabled
                }
            }
            
            // Prefix message with module name for easier identification
            $prefixed_message = '[POS_INVENTORY] ' . $message;
            
            // Use CodeIgniter's log_message function
            log_message($level, $prefixed_message);
            
            return true;
            
        } catch (Exception $e) {
            // If logging fails, fail silently to prevent breaking the application
            return false;
        } catch (Error $e) {
            // Handle PHP 7+ Error objects
            return false;
        }
    }
}

if (!function_exists('pos_inventory_log_info')) {
    /**
     * Log info message safely
     * 
     * @param string $message Log message
     * @return bool True if successful
     */
    function pos_inventory_log_info($message)
    {
        return pos_inventory_safe_log('info', $message);
    }
}

if (!function_exists('pos_inventory_log_error')) {
    /**
     * Log error message safely
     * 
     * @param string $message Log message
     * @return bool True if successful
     */
    function pos_inventory_log_error($message)
    {
        return pos_inventory_safe_log('error', $message);
    }
}

if (!function_exists('pos_inventory_log_warning')) {
    /**
     * Log warning message safely
     * 
     * @param string $message Log message
     * @return bool True if successful
     */
    function pos_inventory_log_warning($message)
    {
        return pos_inventory_safe_log('warning', $message);
    }
}

if (!function_exists('pos_inventory_log_debug')) {
    /**
     * Log debug message safely
     * 
     * @param string $message Log message
     * @return bool True if successful
     */
    function pos_inventory_log_debug($message)
    {
        return pos_inventory_safe_log('debug', $message);
    }
}

if (!function_exists('pos_inventory_init_logging')) {
    /**
     * Initialize logging configuration for the module
     * This function ensures proper log level constants are available
     */
    function pos_inventory_init_logging()
    {
        // Define log level constants if not already defined
        if (!defined('LOG_LEVEL_ERROR')) {
            define('LOG_LEVEL_ERROR', 1);
        }
        if (!defined('LOG_LEVEL_DEBUG')) {
            define('LOG_LEVEL_DEBUG', 2);
        }
        if (!defined('LOG_LEVEL_INFO')) {
            define('LOG_LEVEL_INFO', 3);
        }
        if (!defined('LOG_LEVEL_WARNING')) {
            define('LOG_LEVEL_WARNING', 4);
        }
        
        // Ensure log level mapping array exists in global scope
        if (!isset($GLOBALS['_log_levels'])) {
            $GLOBALS['_log_levels'] = [
                'ERROR' => LOG_LEVEL_ERROR,
                'DEBUG' => LOG_LEVEL_DEBUG,
                'INFO' => LOG_LEVEL_INFO,
                'WARNING' => LOG_LEVEL_WARNING
            ];
        }
        
        return true;
    }
}

// Initialize logging when this helper is loaded
pos_inventory_init_logging();
