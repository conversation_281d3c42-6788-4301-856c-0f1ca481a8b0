<?php defined('BASEPATH') or exit('No direct script access allowed'); ?>
<?php init_head(); ?>
<div id="wrapper">
    <div class="content">
        <div class="row">
            <div class="col-md-12">
                <div class="panel_s">
                    <div class="panel-body">
                        <div class="row">
                            <div class="col-md-8">
                                <h4 class="no-margin">
                                    <i class="fa fa-link"></i> <?php echo $title; ?>
                                </h4>
                            </div>
                            <div class="col-md-4 text-right">
                                <div class="btn-group">
                                    <button type="button" class="btn btn-primary" onclick="autoMapProducts()">
                                        <i class="fa fa-magic"></i> <?php echo _l('auto_map_products'); ?>
                                    </button>
                                    <button type="button" class="btn btn-info" onclick="syncMappedProducts()">
                                        <i class="fa fa-refresh"></i> <?php echo _l('sync_mapped_products'); ?>
                                    </button>
                                    <a href="<?php echo admin_url('pos_inventory/integrations'); ?>" class="btn btn-default">
                                        <i class="fa fa-arrow-left"></i> <?php echo _l('back_to_integrations'); ?>
                                    </a>
                                </div>
                            </div>
                        </div>
                        <hr class="hr-panel-heading" />

                        <!-- Mapping Statistics -->
                        <div class="row">
                            <div class="col-md-3">
                                <div class="panel panel-primary">
                                    <div class="panel-body text-center">
                                        <h3 class="no-margin text-primary" id="total-products"><?php echo $statistics['total_products']; ?></h3>
                                        <p class="text-muted"><?php echo _l('total_pos_products'); ?></p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="panel panel-success">
                                    <div class="panel-body text-center">
                                        <h3 class="no-margin text-success" id="mapped-products"><?php echo $statistics['mapped_products']; ?></h3>
                                        <p class="text-muted"><?php echo _l('mapped_products'); ?></p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="panel panel-warning">
                                    <div class="panel-body text-center">
                                        <h3 class="no-margin text-warning" id="unmapped-products"><?php echo $statistics['unmapped_products']; ?></h3>
                                        <p class="text-muted"><?php echo _l('unmapped_products'); ?></p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="panel panel-info">
                                    <div class="panel-body text-center">
                                        <h3 class="no-margin text-info" id="platform-products"><?php echo $statistics['platform_products']; ?></h3>
                                        <p class="text-muted"><?php echo _l('platform_products'); ?></p>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Filters -->
                        <div class="row">
                            <div class="col-md-12">
                                <div class="panel panel-default">
                                    <div class="panel-body">
                                        <div class="row">
                                            <div class="col-md-3">
                                                <div class="form-group">
                                                    <label for="filter-status"><?php echo _l('mapping_status'); ?></label>
                                                    <select class="form-control" id="filter-status">
                                                        <option value=""><?php echo _l('all_statuses'); ?></option>
                                                        <option value="mapped"><?php echo _l('mapped'); ?></option>
                                                        <option value="unmapped"><?php echo _l('unmapped'); ?></option>
                                                        <option value="conflict"><?php echo _l('conflict'); ?></option>
                                                    </select>
                                                </div>
                                            </div>
                                            <div class="col-md-3">
                                                <div class="form-group">
                                                    <label for="filter-category"><?php echo _l('category'); ?></label>
                                                    <select class="form-control" id="filter-category">
                                                        <option value=""><?php echo _l('all_categories'); ?></option>
                                                        <?php foreach ($categories as $category): ?>
                                                            <option value="<?php echo $category['id']; ?>"><?php echo $category['name']; ?></option>
                                                        <?php endforeach; ?>
                                                    </select>
                                                </div>
                                            </div>
                                            <div class="col-md-3">
                                                <div class="form-group">
                                                    <label for="filter-sync-status"><?php echo _l('sync_status'); ?></label>
                                                    <select class="form-control" id="filter-sync-status">
                                                        <option value=""><?php echo _l('all_sync_statuses'); ?></option>
                                                        <option value="synced"><?php echo _l('synced'); ?></option>
                                                        <option value="pending"><?php echo _l('pending_sync'); ?></option>
                                                        <option value="failed"><?php echo _l('sync_failed'); ?></option>
                                                    </select>
                                                </div>
                                            </div>
                                            <div class="col-md-3">
                                                <div class="form-group">
                                                    <label>&nbsp;</label>
                                                    <div>
                                                        <button type="button" class="btn btn-info btn-block" onclick="applyFilters()">
                                                            <i class="fa fa-filter"></i> <?php echo _l('apply_filters'); ?>
                                                        </button>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Product Mappings Table -->
                        <div class="row">
                            <div class="col-md-12">
                                <div class="panel panel-default">
                                    <div class="panel-body">
                                        <div class="table-responsive">
                                            <table class="table dt-table" id="product-mappings-table">
                                                <thead>
                                                    <tr>
                                                        <th><input type="checkbox" id="select-all"></th>
                                                        <th><?php echo _l('pos_product'); ?></th>
                                                        <th><?php echo _l('platform_product'); ?></th>
                                                        <th><?php echo _l('mapping_status'); ?></th>
                                                        <th><?php echo _l('sync_status'); ?></th>
                                                        <th><?php echo _l('last_sync'); ?></th>
                                                        <th><?php echo _l('actions'); ?></th>
                                                    </tr>
                                                </thead>
                                                <tbody></tbody>
                                            </table>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Bulk Actions -->
                        <div class="row">
                            <div class="col-md-12">
                                <div class="panel panel-default">
                                    <div class="panel-body">
                                        <div class="btn-group">
                                            <button type="button" class="btn btn-primary" onclick="bulkMapSelected()">
                                                <i class="fa fa-link"></i> <?php echo _l('map_selected'); ?>
                                            </button>
                                            <button type="button" class="btn btn-warning" onclick="bulkUnmapSelected()">
                                                <i class="fa fa-unlink"></i> <?php echo _l('unmap_selected'); ?>
                                            </button>
                                            <button type="button" class="btn btn-success" onclick="bulkSyncSelected()">
                                                <i class="fa fa-refresh"></i> <?php echo _l('sync_selected'); ?>
                                            </button>
                                        </div>
                                        <span class="text-muted" id="selected-count">0 <?php echo _l('items_selected'); ?></span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Product Mapping Modal -->
<div class="modal fade" id="product-mapping-modal" tabindex="-1" role="dialog">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
                <h4 class="modal-title"><?php echo _l('map_product'); ?></h4>
            </div>
            <div class="modal-body">
                <form id="mapping-form">
                    <input type="hidden" id="pos-product-id" name="pos_product_id">
                    
                    <div class="row">
                        <div class="col-md-6">
                            <h5><?php echo _l('pos_product'); ?></h5>
                            <div id="pos-product-info">
                                <!-- POS product info will be loaded here -->
                            </div>
                        </div>
                        <div class="col-md-6">
                            <h5><?php echo _l('platform_product'); ?></h5>
                            <div class="form-group">
                                <label for="platform-product-search"><?php echo _l('search_platform_products'); ?></label>
                                <input type="text" class="form-control" id="platform-product-search" 
                                       placeholder="<?php echo _l('type_to_search'); ?>">
                            </div>
                            <div id="platform-products-list" style="max-height: 300px; overflow-y: auto;">
                                <!-- Platform products will be loaded here -->
                            </div>
                            <input type="hidden" id="platform-product-id" name="platform_product_id">
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-12">
                            <h5><?php echo _l('mapping_options'); ?></h5>
                            <div class="checkbox">
                                <label>
                                    <input type="checkbox" name="sync_price" value="1" checked>
                                    <?php echo _l('sync_price'); ?>
                                </label>
                            </div>
                            <div class="checkbox">
                                <label>
                                    <input type="checkbox" name="sync_inventory" value="1" checked>
                                    <?php echo _l('sync_inventory'); ?>
                                </label>
                            </div>
                            <div class="checkbox">
                                <label>
                                    <input type="checkbox" name="sync_description" value="1" checked>
                                    <?php echo _l('sync_description'); ?>
                                </label>
                            </div>
                            <div class="checkbox">
                                <label>
                                    <input type="checkbox" name="sync_images" value="1" checked>
                                    <?php echo _l('sync_images'); ?>
                                </label>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-default" data-dismiss="modal"><?php echo _l('cancel'); ?></button>
                <button type="button" class="btn btn-primary" onclick="saveMapping()"><?php echo _l('save_mapping'); ?></button>
            </div>
        </div>
    </div>
</div>

<script>
$(document).ready(function() {
    // Initialize DataTable
    var mappingsTable = $('#product-mappings-table').DataTable({
        "processing": true,
        "serverSide": true,
        "ajax": {
            "url": "<?php echo admin_url('pos_inventory/product_mappings_table/' . $integration_id); ?>",
            "type": "POST",
            "data": function(d) {
                d.status_filter = $('#filter-status').val();
                d.category_filter = $('#filter-category').val();
                d.sync_status_filter = $('#filter-sync-status').val();
            }
        },
        "columns": [
            {"data": "checkbox", "orderable": false, "searchable": false},
            {"data": "pos_product"},
            {"data": "platform_product"},
            {"data": "mapping_status"},
            {"data": "sync_status"},
            {"data": "last_sync"},
            {"data": "actions", "orderable": false, "searchable": false}
        ],
        "order": [[1, "asc"]]
    });

    // Select all checkbox
    $('#select-all').change(function() {
        $('.product-checkbox').prop('checked', this.checked);
        updateSelectedCount();
    });

    // Individual checkboxes
    $(document).on('change', '.product-checkbox', function() {
        updateSelectedCount();
    });

    // Platform product search
    $('#platform-product-search').on('input', function() {
        var query = $(this).val();
        if (query.length >= 2) {
            searchPlatformProducts(query);
        } else {
            $('#platform-products-list').empty();
        }
    });
});

function updateSelectedCount() {
    var count = $('.product-checkbox:checked').length;
    $('#selected-count').text(count + ' <?php echo _l('items_selected'); ?>');
}

function applyFilters() {
    $('#product-mappings-table').DataTable().ajax.reload();
}

function mapProduct(posProductId) {
    $('#pos-product-id').val(posProductId);
    
    // Load POS product info
    $.post(admin_url + 'pos_inventory/get_pos_product_info', {
        product_id: posProductId
    }, function(response) {
        if (response.success) {
            $('#pos-product-info').html(response.html);
        }
    }, 'json');

    $('#product-mapping-modal').modal('show');
}

function searchPlatformProducts(query) {
    $.post(admin_url + 'pos_inventory/search_platform_products', {
        integration_id: <?php echo $integration_id; ?>,
        query: query
    }, function(response) {
        if (response.success) {
            var html = '';
            response.products.forEach(function(product) {
                html += '<div class="platform-product-item" onclick="selectPlatformProduct(' + product.id + ', \'' + product.name + '\')">';
                html += '<strong>' + product.name + '</strong><br>';
                html += '<small>' + product.sku + ' - ' + product.price + '</small>';
                html += '</div>';
            });
            $('#platform-products-list').html(html);
        }
    }, 'json');
}

function selectPlatformProduct(id, name) {
    $('#platform-product-id').val(id);
    $('.platform-product-item').removeClass('selected');
    event.target.closest('.platform-product-item').classList.add('selected');
}

function saveMapping() {
    var formData = $('#mapping-form').serialize();
    
    $.post(admin_url + 'pos_inventory/save_product_mapping', formData, function(response) {
        if (response.success) {
            $('#product-mapping-modal').modal('hide');
            $('#product-mappings-table').DataTable().ajax.reload();
            alert_float('success', response.message);
        } else {
            alert_float('danger', response.message);
        }
    }, 'json');
}

function unmapProduct(mappingId) {
    if (confirm('<?php echo _l('confirm_unmap_product'); ?>')) {
        $.post(admin_url + 'pos_inventory/unmap_product', {
            mapping_id: mappingId
        }, function(response) {
            if (response.success) {
                $('#product-mappings-table').DataTable().ajax.reload();
                alert_float('success', response.message);
            } else {
                alert_float('danger', response.message);
            }
        }, 'json');
    }
}

function syncProduct(mappingId) {
    $.post(admin_url + 'pos_inventory/sync_single_product', {
        mapping_id: mappingId
    }, function(response) {
        if (response.success) {
            $('#product-mappings-table').DataTable().ajax.reload();
            alert_float('success', response.message);
        } else {
            alert_float('danger', response.message);
        }
    }, 'json');
}

function autoMapProducts() {
    if (confirm('<?php echo _l('confirm_auto_map_products'); ?>')) {
        $.post(admin_url + 'pos_inventory/auto_map_products', {
            integration_id: <?php echo $integration_id; ?>
        }, function(response) {
            if (response.success) {
                $('#product-mappings-table').DataTable().ajax.reload();
                alert_float('success', response.message);
            } else {
                alert_float('danger', response.message);
            }
        }, 'json');
    }
}

function syncMappedProducts() {
    if (confirm('<?php echo _l('confirm_sync_mapped_products'); ?>')) {
        $.post(admin_url + 'pos_inventory/sync_mapped_products', {
            integration_id: <?php echo $integration_id; ?>
        }, function(response) {
            if (response.success) {
                alert_float('success', response.message);
            } else {
                alert_float('danger', response.message);
            }
        }, 'json');
    }
}

function bulkMapSelected() {
    var selected = [];
    $('.product-checkbox:checked').each(function() {
        selected.push($(this).val());
    });
    
    if (selected.length === 0) {
        alert('<?php echo _l('no_products_selected'); ?>');
        return;
    }
    
    // Implement bulk mapping logic
    alert('Bulk mapping feature coming soon');
}

function bulkUnmapSelected() {
    var selected = [];
    $('.product-checkbox:checked').each(function() {
        selected.push($(this).val());
    });
    
    if (selected.length === 0) {
        alert('<?php echo _l('no_products_selected'); ?>');
        return;
    }
    
    if (confirm('<?php echo _l('confirm_bulk_unmap'); ?>')) {
        $.post(admin_url + 'pos_inventory/bulk_unmap_products', {
            product_ids: selected
        }, function(response) {
            if (response.success) {
                $('#product-mappings-table').DataTable().ajax.reload();
                alert_float('success', response.message);
            } else {
                alert_float('danger', response.message);
            }
        }, 'json');
    }
}

function bulkSyncSelected() {
    var selected = [];
    $('.product-checkbox:checked').each(function() {
        selected.push($(this).val());
    });
    
    if (selected.length === 0) {
        alert('<?php echo _l('no_products_selected'); ?>');
        return;
    }
    
    if (confirm('<?php echo _l('confirm_bulk_sync'); ?>')) {
        $.post(admin_url + 'pos_inventory/bulk_sync_products', {
            product_ids: selected
        }, function(response) {
            if (response.success) {
                alert_float('success', response.message);
            } else {
                alert_float('danger', response.message);
            }
        }, 'json');
    }
}
</script>

<style>
.platform-product-item {
    padding: 10px;
    border: 1px solid #ddd;
    margin-bottom: 5px;
    cursor: pointer;
    border-radius: 3px;
}

.platform-product-item:hover {
    background-color: #f5f5f5;
}

.platform-product-item.selected {
    background-color: #d4edda;
    border-color: #28a745;
}
</style>

<?php init_tail(); ?>
