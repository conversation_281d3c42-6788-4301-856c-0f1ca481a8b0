<?php
defined('BASEPATH') or exit('No direct script access allowed');

/**
 * Migration: Version 1.0.1 - POS Transactions System
 * 
 * This migration adds POS transaction tables and related functionality
 */

class Migration_Version_101 extends App_module_migration
{
    public function up()
    {
        $CI = &get_instance();
        
        // Create pos_purchase_orders table
        if (!$CI->db->table_exists(db_prefix() . 'pos_purchase_orders')) {
            $CI->db->query('CREATE TABLE `' . db_prefix() . "pos_purchase_orders` (
                `id` int(11) NOT NULL AUTO_INCREMENT,
                `po_number` varchar(50) NOT NULL,
                `supplier_id` int(11) NOT NULL,
                `location_id` int(11) NOT NULL,
                `order_date` date NOT NULL,
                `expected_delivery_date` date DEFAULT NULL,
                `status` enum('draft','pending','ordered','partially_received','received','cancelled') NOT NULL DEFAULT 'draft',
                `subtotal` decimal(15,2) NOT NULL DEFAULT 0.00,
                `tax_amount` decimal(15,2) NOT NULL DEFAULT 0.00,
                `shipping_cost` decimal(15,2) NOT NULL DEFAULT 0.00,
                `total` decimal(15,2) NOT NULL DEFAULT 0.00,
                `currency` varchar(10) NOT NULL DEFAULT 'USD',
                `notes` text,
                `created_by` int(11) NOT NULL,
                `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
                `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                PRIMARY KEY (`id`),
                UNIQUE KEY `po_number` (`po_number`),
                KEY `supplier_id` (`supplier_id`),
                KEY `location_id` (`location_id`),
                KEY `status` (`status`)
            ) ENGINE=InnoDB DEFAULT CHARSET=" . $CI->db->char_set);
        }

        // Create pos_purchase_order_items table
        if (!$CI->db->table_exists(db_prefix() . 'pos_purchase_order_items')) {
            $CI->db->query('CREATE TABLE `' . db_prefix() . "pos_purchase_order_items` (
                `id` int(11) NOT NULL AUTO_INCREMENT,
                `purchase_order_id` int(11) NOT NULL,
                `product_id` int(11) NOT NULL,
                `variant_id` int(11) DEFAULT NULL,
                `quantity` int(11) NOT NULL,
                `unit_cost` decimal(15,2) NOT NULL,
                `total_cost` decimal(15,2) NOT NULL,
                `received_quantity` int(11) NOT NULL DEFAULT 0,
                `notes` text,
                `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
                `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                PRIMARY KEY (`id`),
                KEY `purchase_order_id` (`purchase_order_id`),
                KEY `product_id` (`product_id`)
            ) ENGINE=InnoDB DEFAULT CHARSET=" . $CI->db->char_set);
        }

        // Create pos_transactions table
        if (!$CI->db->table_exists(db_prefix() . 'pos_transactions')) {
            $CI->db->query('CREATE TABLE `' . db_prefix() . "pos_transactions` (
                `id` int(11) NOT NULL AUTO_INCREMENT,
                `transaction_number` varchar(50) NOT NULL,
                `transaction_type` enum('sale','return','exchange') NOT NULL DEFAULT 'sale',
                `customer_id` int(11) DEFAULT NULL,
                `location_id` int(11) NOT NULL,
                `cashier_id` int(11) NOT NULL,
                `subtotal` decimal(15,2) NOT NULL DEFAULT 0.00,
                `discount_amount` decimal(15,2) NOT NULL DEFAULT 0.00,
                `tax_amount` decimal(15,2) NOT NULL DEFAULT 0.00,
                `total` decimal(15,2) NOT NULL DEFAULT 0.00,
                `payment_method` varchar(50) NOT NULL,
                `payment_status` enum('pending','paid','partial','refunded') NOT NULL DEFAULT 'pending',
                `notes` text,
                `receipt_printed` tinyint(1) NOT NULL DEFAULT 0,
                `email_sent` tinyint(1) NOT NULL DEFAULT 0,
                `transaction_date` datetime NOT NULL,
                `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
                `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                PRIMARY KEY (`id`),
                UNIQUE KEY `transaction_number` (`transaction_number`),
                KEY `customer_id` (`customer_id`),
                KEY `location_id` (`location_id`),
                KEY `cashier_id` (`cashier_id`),
                KEY `transaction_type` (`transaction_type`),
                KEY `transaction_date` (`transaction_date`)
            ) ENGINE=InnoDB DEFAULT CHARSET=" . $CI->db->char_set);
        }

        // Create pos_transaction_items table
        if (!$CI->db->table_exists(db_prefix() . 'pos_transaction_items')) {
            $CI->db->query('CREATE TABLE `' . db_prefix() . "pos_transaction_items` (
                `id` int(11) NOT NULL AUTO_INCREMENT,
                `transaction_id` int(11) NOT NULL,
                `product_id` int(11) NOT NULL,
                `variant_id` int(11) DEFAULT NULL,
                `quantity` int(11) NOT NULL,
                `unit_price` decimal(15,2) NOT NULL,
                `discount_amount` decimal(15,2) NOT NULL DEFAULT 0.00,
                `tax_amount` decimal(15,2) NOT NULL DEFAULT 0.00,
                `total_amount` decimal(15,2) NOT NULL,
                `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
                PRIMARY KEY (`id`),
                KEY `transaction_id` (`transaction_id`),
                KEY `product_id` (`product_id`)
            ) ENGINE=InnoDB DEFAULT CHARSET=" . $CI->db->char_set);
        }

        // Create pos_held_orders table
        if (!$CI->db->table_exists(db_prefix() . 'pos_held_orders')) {
            $CI->db->query('CREATE TABLE `' . db_prefix() . "pos_held_orders` (
                `id` int(11) NOT NULL AUTO_INCREMENT,
                `order_number` varchar(50) NOT NULL,
                `customer_id` int(11) DEFAULT NULL,
                `location_id` int(11) NOT NULL,
                `cashier_id` int(11) NOT NULL,
                `order_data` longtext NOT NULL,
                `notes` text,
                `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
                `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                PRIMARY KEY (`id`),
                UNIQUE KEY `order_number` (`order_number`),
                KEY `customer_id` (`customer_id`),
                KEY `location_id` (`location_id`),
                KEY `cashier_id` (`cashier_id`)
            ) ENGINE=InnoDB DEFAULT CHARSET=" . $CI->db->char_set);
        }

        // Create pos_integrations table
        if (!$CI->db->table_exists(db_prefix() . 'pos_integrations')) {
            $CI->db->query('CREATE TABLE `' . db_prefix() . "pos_integrations` (
                `id` int(11) NOT NULL AUTO_INCREMENT,
                `platform` enum('woocommerce','shopify') NOT NULL,
                `name` varchar(191) NOT NULL,
                `api_url` varchar(255) NOT NULL,
                `api_key` varchar(255) NOT NULL,
                `api_secret` varchar(255) DEFAULT NULL,
                `access_token` varchar(255) DEFAULT NULL,
                `webhook_secret` varchar(255) DEFAULT NULL,
                `settings` longtext,
                `sync_products` tinyint(1) NOT NULL DEFAULT 1,
                `sync_inventory` tinyint(1) NOT NULL DEFAULT 1,
                `sync_orders` tinyint(1) NOT NULL DEFAULT 0,
                `auto_sync` tinyint(1) NOT NULL DEFAULT 0,
                `sync_frequency` int(11) NOT NULL DEFAULT 60,
                `last_sync` datetime DEFAULT NULL,
                `status` tinyint(1) NOT NULL DEFAULT 1,
                `created_by` int(11) NOT NULL,
                `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
                `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                PRIMARY KEY (`id`),
                KEY `platform` (`platform`),
                KEY `status` (`status`)
            ) ENGINE=InnoDB DEFAULT CHARSET=" . $CI->db->char_set);
        }

        // Create pos_sync_logs table
        if (!$CI->db->table_exists(db_prefix() . 'pos_sync_logs')) {
            $CI->db->query('CREATE TABLE `' . db_prefix() . "pos_sync_logs` (
                `id` int(11) NOT NULL AUTO_INCREMENT,
                `integration_id` int(11) NOT NULL,
                `sync_type` varchar(50) NOT NULL,
                `status` enum('pending','running','completed','failed') NOT NULL DEFAULT 'pending',
                `records_processed` int(11) NOT NULL DEFAULT 0,
                `records_success` int(11) NOT NULL DEFAULT 0,
                `records_failed` int(11) NOT NULL DEFAULT 0,
                `error_message` text,
                `started_at` datetime DEFAULT NULL,
                `completed_at` datetime DEFAULT NULL,
                `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
                PRIMARY KEY (`id`),
                KEY `integration_id` (`integration_id`),
                KEY `sync_type` (`sync_type`),
                KEY `status` (`status`)
            ) ENGINE=InnoDB DEFAULT CHARSET=" . $CI->db->char_set);
        }

        return true;
    }

    public function down()
    {
        $CI = &get_instance();
        
        // Drop tables in reverse order
        $tables = [
            'pos_sync_logs',
            'pos_integrations',
            'pos_held_orders',
            'pos_transaction_items',
            'pos_transactions',
            'pos_purchase_order_items',
            'pos_purchase_orders'
        ];

        foreach ($tables as $table) {
            if ($CI->db->table_exists(db_prefix() . $table)) {
                $CI->db->query('DROP TABLE `' . db_prefix() . $table . '`');
            }
        }

        return true;
    }
}
