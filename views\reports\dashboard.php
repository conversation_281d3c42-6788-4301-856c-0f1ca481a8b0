<?php defined('BASEPATH') or exit('No direct script access allowed'); ?>
<?php init_head(); ?>
<div id="wrapper">
    <div class="content">
        <!-- Page Header -->
        <div class="row">
            <div class="col-md-12">
                <div class="panel_s">
                    <div class="panel-body">
                        <div class="row">
                            <div class="col-md-8">
                                <h4 class="no-margin">
                                    <i class="fa fa-bar-chart"></i> <?php echo _l('reports_analytics'); ?>
                                </h4>
                                <p class="text-muted"><?php echo _l('comprehensive_business_insights'); ?></p>
                            </div>
                            <div class="col-md-4 text-right">
                                <div class="btn-group">
                                    <button type="button" class="btn btn-default dropdown-toggle" data-toggle="dropdown">
                                        <i class="fa fa-download"></i> <?php echo _l('export_reports'); ?> <span class="caret"></span>
                                    </button>
                                    <ul class="dropdown-menu dropdown-menu-right">
                                        <li><a href="#" id="export-pdf"><i class="fa fa-file-pdf-o"></i> <?php echo _l('export_pdf'); ?></a></li>
                                        <li><a href="#" id="export-excel"><i class="fa fa-file-excel-o"></i> <?php echo _l('export_excel'); ?></a></li>
                                        <li><a href="#" id="export-csv"><i class="fa fa-file-text-o"></i> <?php echo _l('export_csv'); ?></a></li>
                                    </ul>
                                </div>
                                <button type="button" class="btn btn-primary" id="refresh-dashboard">
                                    <i class="fa fa-refresh"></i> <?php echo _l('refresh'); ?>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Date Range Filter -->
        <div class="row">
            <div class="col-md-12">
                <div class="panel_s">
                    <div class="panel-body">
                        <form id="date-filter-form" class="form-inline">
                            <div class="form-group">
                                <label for="date_from"><?php echo _l('date_from'); ?>:</label>
                                <input type="date" class="form-control" id="date_from" name="date_from" 
                                       value="<?php echo date('Y-m-01'); ?>">
                            </div>
                            <div class="form-group" style="margin-left: 15px;">
                                <label for="date_to"><?php echo _l('date_to'); ?>:</label>
                                <input type="date" class="form-control" id="date_to" name="date_to" 
                                       value="<?php echo date('Y-m-d'); ?>">
                            </div>
                            <div class="form-group" style="margin-left: 15px;">
                                <label for="location_filter"><?php echo _l('location'); ?>:</label>
                                <select class="form-control selectpicker" id="location_filter" name="location_id">
                                    <option value=""><?php echo _l('all_locations'); ?></option>
                                    <?php foreach ($locations as $location) { ?>
                                        <option value="<?php echo $location['id']; ?>"><?php echo $location['name']; ?></option>
                                    <?php } ?>
                                </select>
                            </div>
                            <div class="form-group" style="margin-left: 15px;">
                                <button type="submit" class="btn btn-info">
                                    <i class="fa fa-filter"></i> <?php echo _l('apply_filter'); ?>
                                </button>
                                <button type="button" class="btn btn-default" id="reset-filters">
                                    <i class="fa fa-times"></i> <?php echo _l('reset'); ?>
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>

        <!-- KPI Cards -->
        <div class="row" id="kpi-cards">
            <div class="col-md-3">
                <div class="panel_s">
                    <div class="panel-body text-center">
                        <div class="kpi-icon" style="color: #28a745; font-size: 48px; margin-bottom: 15px;">
                            <i class="fa fa-dollar"></i>
                        </div>
                        <h3 class="no-margin" id="total-sales">
                            <span class="loading-placeholder">Loading...</span>
                        </h3>
                        <p class="text-muted"><?php echo _l('total_sales'); ?></p>
                        <div class="progress" style="height: 4px; margin-top: 10px;">
                            <div class="progress-bar bg-success" id="sales-progress" style="width: 0%"></div>
                        </div>
                        <small class="text-muted" id="sales-change"></small>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="panel_s">
                    <div class="panel-body text-center">
                        <div class="kpi-icon" style="color: #007bff; font-size: 48px; margin-bottom: 15px;">
                            <i class="fa fa-shopping-cart"></i>
                        </div>
                        <h3 class="no-margin" id="total-transactions">
                            <span class="loading-placeholder">Loading...</span>
                        </h3>
                        <p class="text-muted"><?php echo _l('total_transactions'); ?></p>
                        <div class="progress" style="height: 4px; margin-top: 10px;">
                            <div class="progress-bar bg-primary" id="transactions-progress" style="width: 0%"></div>
                        </div>
                        <small class="text-muted" id="transactions-change"></small>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="panel_s">
                    <div class="panel-body text-center">
                        <div class="kpi-icon" style="color: #ffc107; font-size: 48px; margin-bottom: 15px;">
                            <i class="fa fa-calculator"></i>
                        </div>
                        <h3 class="no-margin" id="average-sale">
                            <span class="loading-placeholder">Loading...</span>
                        </h3>
                        <p class="text-muted"><?php echo _l('average_sale_value'); ?></p>
                        <div class="progress" style="height: 4px; margin-top: 10px;">
                            <div class="progress-bar bg-warning" id="average-progress" style="width: 0%"></div>
                        </div>
                        <small class="text-muted" id="average-change"></small>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="panel_s">
                    <div class="panel-body text-center">
                        <div class="kpi-icon" style="color: #dc3545; font-size: 48px; margin-bottom: 15px;">
                            <i class="fa fa-cubes"></i>
                        </div>
                        <h3 class="no-margin" id="items-sold">
                            <span class="loading-placeholder">Loading...</span>
                        </h3>
                        <p class="text-muted"><?php echo _l('items_sold'); ?></p>
                        <div class="progress" style="height: 4px; margin-top: 10px;">
                            <div class="progress-bar bg-danger" id="items-progress" style="width: 0%"></div>
                        </div>
                        <small class="text-muted" id="items-change"></small>
                    </div>
                </div>
            </div>
        </div>

        <!-- Charts Row -->
        <div class="row">
            <div class="col-md-8">
                <div class="panel_s">
                    <div class="panel-body">
                        <div class="panel-heading-custom">
                            <h5 class="panel-title">
                                <i class="fa fa-line-chart"></i> <?php echo _l('sales_trend'); ?>
                                <div class="pull-right">
                                    <div class="btn-group btn-group-xs">
                                        <button type="button" class="btn btn-default chart-period" data-period="7">7D</button>
                                        <button type="button" class="btn btn-default chart-period" data-period="30">30D</button>
                                        <button type="button" class="btn btn-primary chart-period" data-period="90">90D</button>
                                    </div>
                                </div>
                            </h5>
                        </div>
                        <div style="height: 400px;">
                            <canvas id="sales-trend-chart"></canvas>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="panel_s">
                    <div class="panel-body">
                        <h5 class="panel-title">
                            <i class="fa fa-pie-chart"></i> <?php echo _l('payment_methods'); ?>
                        </h5>
                        <div style="height: 400px;">
                            <canvas id="payment-methods-chart"></canvas>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Additional Charts -->
        <div class="row">
            <div class="col-md-6">
                <div class="panel_s">
                    <div class="panel-body">
                        <h5 class="panel-title">
                            <i class="fa fa-star"></i> <?php echo _l('top_products'); ?>
                        </h5>
                        <div style="height: 350px;">
                            <canvas id="top-products-chart"></canvas>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="panel_s">
                    <div class="panel-body">
                        <h5 class="panel-title">
                            <i class="fa fa-clock-o"></i> <?php echo _l('hourly_sales'); ?>
                        </h5>
                        <div style="height: 350px;">
                            <canvas id="hourly-sales-chart"></canvas>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Quick Reports -->
        <div class="row">
            <div class="col-md-12">
                <div class="panel_s">
                    <div class="panel-body">
                        <h5 class="panel-title">
                            <i class="fa fa-file-text"></i> <?php echo _l('quick_reports'); ?>
                        </h5>
                        <div class="row" style="margin-top: 20px;">
                            <div class="col-md-3">
                                <a href="<?php echo admin_url('pos_inventory/reports/sales'); ?>" class="btn btn-block btn-outline btn-primary">
                                    <i class="fa fa-line-chart"></i><br>
                                    <?php echo _l('sales_report'); ?>
                                </a>
                            </div>
                            <div class="col-md-3">
                                <a href="<?php echo admin_url('pos_inventory/reports/inventory'); ?>" class="btn btn-block btn-outline btn-success">
                                    <i class="fa fa-cubes"></i><br>
                                    <?php echo _l('inventory_report'); ?>
                                </a>
                            </div>
                            <div class="col-md-3">
                                <a href="<?php echo admin_url('pos_inventory/reports/customers'); ?>" class="btn btn-block btn-outline btn-info">
                                    <i class="fa fa-users"></i><br>
                                    <?php echo _l('customer_report'); ?>
                                </a>
                            </div>
                            <div class="col-md-3">
                                <a href="<?php echo admin_url('pos_inventory/reports/staff'); ?>" class="btn btn-block btn-outline btn-warning">
                                    <i class="fa fa-user"></i><br>
                                    <?php echo _l('staff_report'); ?>
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Loading Overlay -->
<div id="loading-overlay" style="display: none; position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.5); z-index: 9999;">
    <div style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); text-align: center; color: white;">
        <i class="fa fa-spinner fa-spin fa-3x"></i>
        <p style="margin-top: 15px;"><?php echo _l('loading_reports'); ?>...</p>
    </div>
</div>

<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
var admin_url = '<?php echo admin_url(); ?>';
var currency_symbol = '<?php echo get_base_currency()->symbol; ?>';

// Initialize dashboard
$(document).ready(function() {
    Reports.init();
});
</script>

<?php init_tail(); ?>
