<?php defined('BASEPATH') or exit('No direct script access allowed'); ?>
<?php init_head(); ?>
<div id="wrapper">
    <div class="content">
        <div class="row">
            <div class="col-md-12">
                <?php echo form_open_multipart($this->uri->uri_string(), ['id' => 'product-form']); ?>
                <div class="panel_s">
                    <div class="panel-body">
                        <div class="row">
                            <div class="col-md-8">
                                <h4 class="no-margin">
                                    <?php echo $title; ?>
                                </h4>
                            </div>
                            <div class="col-md-4 text-right">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fa fa-save"></i> <?php echo _l('save'); ?>
                                </button>
                                <a href="<?php echo admin_url('pos_inventory/products'); ?>" class="btn btn-default">
                                    <?php echo _l('cancel'); ?>
                                </a>
                            </div>
                        </div>
                        <hr class="hr-panel-heading" />
                        
                        <!-- Product Information Tabs -->
                        <div class="row">
                            <div class="col-md-12">
                                <ul class="nav nav-tabs" role="tablist">
                                    <li role="presentation" class="active">
                                        <a href="#general" aria-controls="general" role="tab" data-toggle="tab">
                                            <?php echo _l('general'); ?>
                                        </a>
                                    </li>
                                    <li role="presentation">
                                        <a href="#pricing" aria-controls="pricing" role="tab" data-toggle="tab">
                                            <?php echo _l('pricing'); ?>
                                        </a>
                                    </li>
                                    <li role="presentation">
                                        <a href="#inventory" aria-controls="inventory" role="tab" data-toggle="tab">
                                            <?php echo _l('inventory'); ?>
                                        </a>
                                    </li>
                                    <li role="presentation">
                                        <a href="#variants" aria-controls="variants" role="tab" data-toggle="tab">
                                            <?php echo _l('variants'); ?>
                                        </a>
                                    </li>
                                    <li role="presentation">
                                        <a href="#images" aria-controls="images" role="tab" data-toggle="tab">
                                            <?php echo _l('images'); ?>
                                        </a>
                                    </li>
                                </ul>
                                
                                <div class="tab-content" style="padding-top: 20px;">
                                    <!-- General Tab -->
                                    <div role="tabpanel" class="tab-pane active" id="general">
                                        <div class="row">
                                            <div class="col-md-6">
                                                <div class="form-group">
                                                    <label for="name" class="control-label">
                                                        <?php echo _l('product_name'); ?> <span class="text-danger">*</span>
                                                    </label>
                                                    <input type="text" class="form-control" id="name" name="name" 
                                                           value="<?php echo isset($product) ? $product['name'] : ''; ?>" required>
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="form-group">
                                                    <label for="sku" class="control-label">
                                                        <?php echo _l('product_sku'); ?> <span class="text-danger">*</span>
                                                    </label>
                                                    <input type="text" class="form-control" id="sku" name="sku" 
                                                           value="<?php echo isset($product) ? $product['sku'] : ''; ?>" required>
                                                    <small class="text-muted"><?php echo _l('sku_must_be_unique'); ?></small>
                                                </div>
                                            </div>
                                        </div>
                                        
                                        <div class="row">
                                            <div class="col-md-6">
                                                <div class="form-group">
                                                    <label for="barcode" class="control-label"><?php echo _l('product_barcode'); ?></label>
                                                    <div class="input-group">
                                                        <input type="text" class="form-control" id="barcode" name="barcode" 
                                                               value="<?php echo isset($product) ? $product['barcode'] : ''; ?>">
                                                        <span class="input-group-btn">
                                                            <button type="button" class="btn btn-default" id="generate-barcode">
                                                                <?php echo _l('generate'); ?>
                                                            </button>
                                                        </span>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="form-group">
                                                    <label for="category_id" class="control-label"><?php echo _l('category'); ?></label>
                                                    <select class="form-control selectpicker" id="category_id" name="category_id" data-live-search="true">
                                                        <option value=""><?php echo _l('select_category'); ?></option>
                                                        <?php foreach ($categories as $category) { ?>
                                                            <option value="<?php echo $category['id']; ?>" 
                                                                    <?php echo (isset($product) && $product['category_id'] == $category['id']) ? 'selected' : ''; ?>>
                                                                <?php echo $category['name']; ?>
                                                            </option>
                                                        <?php } ?>
                                                    </select>
                                                </div>
                                            </div>
                                        </div>
                                        
                                        <div class="form-group">
                                            <label for="description" class="control-label"><?php echo _l('product_description'); ?></label>
                                            <textarea class="form-control" id="description" name="description" rows="4"><?php echo isset($product) ? $product['description'] : ''; ?></textarea>
                                        </div>
                                        
                                        <div class="row">
                                            <div class="col-md-4">
                                                <div class="form-group">
                                                    <label for="unit" class="control-label"><?php echo _l('product_unit'); ?></label>
                                                    <input type="text" class="form-control" id="unit" name="unit" 
                                                           value="<?php echo isset($product) ? $product['unit'] : ''; ?>" 
                                                           placeholder="<?php echo _l('e.g. pcs, kg, liter'); ?>">
                                                </div>
                                            </div>
                                            <div class="col-md-4">
                                                <div class="form-group">
                                                    <label for="weight" class="control-label"><?php echo _l('product_weight'); ?></label>
                                                    <input type="number" class="form-control" id="weight" name="weight" 
                                                           value="<?php echo isset($product) ? $product['weight'] : ''; ?>" 
                                                           step="0.01" placeholder="<?php echo _l('weight_in_kg'); ?>">
                                                </div>
                                            </div>
                                            <div class="col-md-4">
                                                <div class="form-group">
                                                    <label for="dimensions" class="control-label"><?php echo _l('product_dimensions'); ?></label>
                                                    <input type="text" class="form-control" id="dimensions" name="dimensions" 
                                                           value="<?php echo isset($product) ? $product['dimensions'] : ''; ?>" 
                                                           placeholder="<?php echo _l('e.g. 10x20x30 cm'); ?>">
                                                </div>
                                            </div>
                                        </div>
                                        
                                        <div class="row">
                                            <div class="col-md-6">
                                                <div class="form-group">
                                                    <div class="checkbox">
                                                        <label>
                                                            <input type="checkbox" name="status" value="1" 
                                                                   <?php echo (!isset($product) || $product['status'] == 1) ? 'checked' : ''; ?>>
                                                            <?php echo _l('active'); ?>
                                                        </label>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <!-- Pricing Tab -->
                                    <div role="tabpanel" class="tab-pane" id="pricing">
                                        <div class="row">
                                            <div class="col-md-6">
                                                <div class="form-group">
                                                    <label for="cost_price" class="control-label"><?php echo _l('product_cost'); ?></label>
                                                    <div class="input-group">
                                                        <span class="input-group-addon"><?php echo get_base_currency()->symbol; ?></span>
                                                        <input type="number" class="form-control" id="cost_price" name="cost_price" 
                                                               value="<?php echo isset($product) ? $product['cost_price'] : '0'; ?>" 
                                                               step="0.01" min="0">
                                                    </div>
                                                    <small class="text-muted"><?php echo _l('cost_price_help'); ?></small>
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="form-group">
                                                    <label for="sale_price" class="control-label">
                                                        <?php echo _l('product_price'); ?> <span class="text-danger">*</span>
                                                    </label>
                                                    <div class="input-group">
                                                        <span class="input-group-addon"><?php echo get_base_currency()->symbol; ?></span>
                                                        <input type="number" class="form-control" id="sale_price" name="sale_price" 
                                                               value="<?php echo isset($product) ? $product['sale_price'] : '0'; ?>" 
                                                               step="0.01" min="0" required>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        
                                        <div class="row">
                                            <div class="col-md-12">
                                                <div class="alert alert-info">
                                                    <strong><?php echo _l('profit_margin'); ?>:</strong>
                                                    <span id="profit-margin">0%</span> |
                                                    <strong><?php echo _l('profit_amount'); ?>:</strong>
                                                    <span id="profit-amount"><?php echo get_base_currency()->symbol; ?>0.00</span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <!-- Inventory Tab -->
                                    <div role="tabpanel" class="tab-pane" id="inventory">
                                        <div class="row">
                                            <div class="col-md-6">
                                                <div class="form-group">
                                                    <div class="checkbox">
                                                        <label>
                                                            <input type="checkbox" name="track_inventory" value="1" id="track_inventory"
                                                                   <?php echo (!isset($product) || $product['track_inventory'] == 1) ? 'checked' : ''; ?>>
                                                            <?php echo _l('track_inventory'); ?>
                                                        </label>
                                                    </div>
                                                    <small class="text-muted"><?php echo _l('track_inventory_help'); ?></small>
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="form-group">
                                                    <div class="checkbox">
                                                        <label>
                                                            <input type="checkbox" name="allow_backorders" value="1"
                                                                   <?php echo (isset($product) && $product['allow_backorders'] == 1) ? 'checked' : ''; ?>>
                                                            <?php echo _l('allow_backorders'); ?>
                                                        </label>
                                                    </div>
                                                    <small class="text-muted"><?php echo _l('allow_backorders_help'); ?></small>
                                                </div>
                                            </div>
                                        </div>
                                        
                                        <div class="inventory-fields">
                                            <div class="row">
                                                <div class="col-md-6">
                                                    <div class="form-group">
                                                        <label for="low_stock_threshold" class="control-label"><?php echo _l('low_stock_threshold'); ?></label>
                                                        <input type="number" class="form-control" id="low_stock_threshold" name="low_stock_threshold" 
                                                               value="<?php echo isset($product) ? $product['low_stock_threshold'] : '5'; ?>" 
                                                               min="0">
                                                        <small class="text-muted"><?php echo _l('low_stock_threshold_help'); ?></small>
                                                    </div>
                                                </div>
                                                <?php if (!isset($product)) { ?>
                                                <div class="col-md-6">
                                                    <div class="form-group">
                                                        <label for="initial_stock" class="control-label"><?php echo _l('initial_stock'); ?></label>
                                                        <input type="number" class="form-control" id="initial_stock" name="initial_stock" 
                                                               value="0" min="0">
                                                        <small class="text-muted"><?php echo _l('initial_stock_help'); ?></small>
                                                    </div>
                                                </div>
                                                <?php } ?>
                                            </div>
                                            
                                            <?php if (isset($product) && !empty($product['inventory'])) { ?>
                                            <div class="row">
                                                <div class="col-md-12">
                                                    <h5><?php echo _l('current_inventory'); ?></h5>
                                                    <div class="table-responsive">
                                                        <table class="table table-striped">
                                                            <thead>
                                                                <tr>
                                                                    <th><?php echo _l('location'); ?></th>
                                                                    <th><?php echo _l('quantity'); ?></th>
                                                                    <th><?php echo _l('reserved'); ?></th>
                                                                    <th><?php echo _l('available'); ?></th>
                                                                    <th><?php echo _l('actions'); ?></th>
                                                                </tr>
                                                            </thead>
                                                            <tbody>
                                                                <?php foreach ($product['inventory'] as $inv) { ?>
                                                                <tr>
                                                                    <td><?php echo $inv['location_name']; ?></td>
                                                                    <td><?php echo $inv['quantity']; ?></td>
                                                                    <td><?php echo $inv['reserved_quantity']; ?></td>
                                                                    <td><?php echo ($inv['quantity'] - $inv['reserved_quantity']); ?></td>
                                                                    <td>
                                                                        <a href="<?php echo admin_url('pos_inventory/stock_adjustment?product=' . $product['id'] . '&location=' . $inv['location_id']); ?>" 
                                                                           class="btn btn-sm btn-primary">
                                                                            <?php echo _l('adjust'); ?>
                                                                        </a>
                                                                    </td>
                                                                </tr>
                                                                <?php } ?>
                                                            </tbody>
                                                        </table>
                                                    </div>
                                                </div>
                                            </div>
                                            <?php } ?>
                                        </div>
                                    </div>
                                    
                                    <!-- Variants Tab -->
                                    <div role="tabpanel" class="tab-pane" id="variants">
                                        <div class="row">
                                            <div class="col-md-12">
                                                <div class="form-group">
                                                    <button type="button" class="btn btn-success" id="add-variant">
                                                        <i class="fa fa-plus"></i> <?php echo _l('add_variant'); ?>
                                                    </button>
                                                </div>
                                                
                                                <div id="variants-container">
                                                    <?php if (isset($product) && !empty($product['variants'])) { ?>
                                                        <?php foreach ($product['variants'] as $index => $variant) { ?>
                                                            <!-- Variant rows will be added here -->
                                                        <?php } ?>
                                                    <?php } ?>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <!-- Images Tab -->
                                    <div role="tabpanel" class="tab-pane" id="images">
                                        <div class="row">
                                            <div class="col-md-6">
                                                <div class="form-group">
                                                    <label for="product_image" class="control-label"><?php echo _l('product_image'); ?></label>
                                                    <input type="file" class="form-control" id="product_image" name="product_image" accept="image/*">
                                                    <small class="text-muted"><?php echo _l('image_upload_help'); ?></small>
                                                </div>
                                                
                                                <?php if (isset($product) && !empty($product['image'])) { ?>
                                                <div class="current-image">
                                                    <label><?php echo _l('current_image'); ?></label>
                                                    <div>
                                                        <img src="<?php echo base_url('uploads/pos_products/' . $product['image']); ?>" 
                                                             alt="<?php echo $product['name']; ?>" 
                                                             style="max-width: 200px; max-height: 200px;">
                                                        <div style="margin-top: 10px;">
                                                            <button type="button" class="btn btn-danger btn-sm" id="remove-image">
                                                                <i class="fa fa-trash"></i> <?php echo _l('remove_image'); ?>
                                                            </button>
                                                        </div>
                                                    </div>
                                                </div>
                                                <?php } ?>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="alert alert-info">
                                                    <h5><?php echo _l('image_guidelines'); ?></h5>
                                                    <ul>
                                                        <li><?php echo _l('recommended_size'); ?>: 800x800px</li>
                                                        <li><?php echo _l('max_file_size'); ?>: 2MB</li>
                                                        <li><?php echo _l('supported_formats'); ?>: JPG, PNG, GIF</li>
                                                        <li><?php echo _l('square_images_work_best'); ?></li>
                                                    </ul>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <?php echo form_close(); ?>
            </div>
        </div>
    </div>
</div>

<script>
// Ensure jQuery is available
if (typeof $ === 'undefined' && typeof jQuery !== 'undefined') {
    var $ = jQuery;
}

// Check if jQuery is loaded
if (typeof $ === 'undefined') {
    console.error('jQuery is not loaded on product page');
} else {
    console.log('jQuery is available on product page');
}

$(document).ready(function() {
    // Set currency symbol globally if not set
    if (typeof window.currency_symbol === 'undefined') {
        window.currency_symbol = '<?php echo get_base_currency()->symbol; ?>';
    }

    // Calculate profit margin
    function calculateProfit() {
        var costPrice = parseFloat($('#cost_price').val()) || 0;
        var salePrice = parseFloat($('#sale_price').val()) || 0;

        if (costPrice > 0 && salePrice > 0) {
            var profit = salePrice - costPrice;
            var margin = (profit / salePrice) * 100;

            $('#profit-amount').text(window.currency_symbol + profit.toFixed(2));
            $('#profit-margin').text(margin.toFixed(2) + '%');
        } else {
            $('#profit-amount').text(window.currency_symbol + '0.00');
            $('#profit-margin').text('0%');
        }
    }

    $('#cost_price, #sale_price').on('input', calculateProfit);
    calculateProfit(); // Initial calculation
    
    // Toggle inventory fields
    $('#track_inventory').change(function() {
        console.log('Track inventory changed:', $(this).is(':checked'));
        if ($(this).is(':checked')) {
            $('.inventory-fields').show();
            $('#initial_stock').prop('required', true);
        } else {
            $('.inventory-fields').hide();
            $('#initial_stock').prop('required', false);
        }
    });

    // Trigger initial state
    $('#track_inventory').trigger('change');

    // Fix checkbox interactions
    $('input[type="checkbox"]').on('change', function() {
        var $this = $(this);
        var name = $this.attr('name');
        var checked = $this.is(':checked');

        console.log('Checkbox changed:', name, checked);

        // Update visual state
        if (checked) {
            $this.closest('.checkbox').addClass('checked');
        } else {
            $this.closest('.checkbox').removeClass('checked');
        }

        // Trigger custom event for other scripts to listen to
        $this.trigger('checkbox:changed', [name, checked]);
    });

    // Initialize checkbox states
    $('input[type="checkbox"]').each(function() {
        var $this = $(this);
        if ($this.is(':checked')) {
            $this.closest('.checkbox').addClass('checked');
        }
    });
    
    // Generate barcode
    $('#generate-barcode').click(function() {
        var timestamp = Date.now();
        var random = Math.floor(Math.random() * 1000);
        var barcode = timestamp.toString() + random.toString().padStart(3, '0');
        $('#barcode').val(barcode.substr(-12)); // Take last 12 digits
    });
    
    // Add variant functionality
    var variantIndex = <?php echo isset($product) && !empty($product['variants']) ? count($product['variants']) : 0; ?>;
    
    $('#add-variant').click(function() {
        addVariantRow();
    });
    
    function addVariantRow(variant = null) {
        var html = `
            <div class="variant-row panel panel-default" data-index="${variantIndex}">
                <div class="panel-body">
                    <div class="row">
                        <div class="col-md-3">
                            <div class="form-group">
                                <label>Variant Name</label>
                                <input type="text" class="form-control" name="variants[${variantIndex}][name]" 
                                       value="${variant ? variant.name : ''}" required>
                            </div>
                        </div>
                        <div class="col-md-2">
                            <div class="form-group">
                                <label>SKU</label>
                                <input type="text" class="form-control" name="variants[${variantIndex}][sku]" 
                                       value="${variant ? variant.sku : ''}" required>
                            </div>
                        </div>
                        <div class="col-md-2">
                            <div class="form-group">
                                <label>Sale Price</label>
                                <input type="number" class="form-control" name="variants[${variantIndex}][sale_price]" 
                                       value="${variant ? variant.sale_price : ''}" step="0.01" required>
                            </div>
                        </div>
                        <div class="col-md-2">
                            <div class="form-group">
                                <label>Cost Price</label>
                                <input type="number" class="form-control" name="variants[${variantIndex}][cost_price]" 
                                       value="${variant ? variant.cost_price : '0'}" step="0.01">
                            </div>
                        </div>
                        <div class="col-md-2">
                            <div class="form-group">
                                <label>Barcode</label>
                                <input type="text" class="form-control" name="variants[${variantIndex}][barcode]" 
                                       value="${variant ? variant.barcode : ''}">
                            </div>
                        </div>
                        <div class="col-md-1">
                            <div class="form-group">
                                <label>&nbsp;</label>
                                <button type="button" class="btn btn-danger btn-block remove-variant">
                                    <i class="fa fa-trash"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
        
        $('#variants-container').append(html);
        variantIndex++;
    }
    
    // Remove variant
    $(document).on('click', '.remove-variant', function() {
        $(this).closest('.variant-row').remove();
    });
    
    // Load existing variants
    <?php if (isset($product) && !empty($product['variants'])) { ?>
        <?php foreach ($product['variants'] as $variant) { ?>
            addVariantRow(<?php echo json_encode($variant); ?>);
        <?php } ?>
    <?php } ?>
});
</script>

<?php init_tail(); ?>
