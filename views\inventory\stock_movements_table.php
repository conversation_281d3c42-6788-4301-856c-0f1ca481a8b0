<?php

defined('BASEPATH') or exit('No direct script access allowed');

// Get CodeIgniter instance
$CI = &get_instance();

$aColumns = [
    'sm.created_at',
    'p.name as product_name',
    'l.name as location_name',
    'sm.movement_type',
    'sm.quantity',
    'sm.unit_cost',
    'CONCAT(sm.reference_type, " #", sm.reference_id) as reference',
    'sm.notes',
    'CONCAT(s.firstname, " ", s.lastname) as created_by_name'
];

$sIndexColumn = 'sm.id';
$sTable = db_prefix() . 'pos_stock_movements sm';

$join = [
    'LEFT JOIN ' . db_prefix() . 'pos_products p ON p.id = sm.product_id',
    'LEFT JOIN ' . db_prefix() . 'pos_locations l ON l.id = sm.location_id',
    'LEFT JOIN ' . db_prefix() . 'staff s ON s.staffid = sm.created_by'
];

$where = [];

// Date filters
if ($CI->input->post('date_from')) {
    $date_from = to_sql_date($CI->input->post('date_from'));
    array_push($where, 'AND DATE(sm.created_at) >= "' . $date_from . '"');
}

if ($CI->input->post('date_to')) {
    $date_to = to_sql_date($CI->input->post('date_to'));
    array_push($where, 'AND DATE(sm.created_at) <= "' . $date_to . '"');
}

// Movement type filter
if ($CI->input->post('movement_type')) {
    $movement_type = $CI->input->post('movement_type');
    array_push($where, 'AND sm.movement_type = "' . $CI->db->escape_str($movement_type) . '"');
}

// Product filter
if ($CI->input->post('product')) {
    $product = $CI->input->post('product');
    array_push($where, 'AND p.name LIKE "%' . $CI->db->escape_like_str($product) . '%"');
}

// Location filter
if ($CI->input->post('location_id')) {
    $location_id = $CI->input->post('location_id');
    array_push($where, 'AND sm.location_id = ' . (int)$location_id);
}

$result = data_tables_init($aColumns, $sIndexColumn, $sTable, $join, $where, [
    'sm.id',
    'sm.product_id',
    'sm.location_id',
    'sm.movement_type',
    'sm.quantity',
    'sm.unit_cost',
    'sm.reference_type',
    'sm.reference_id',
    'sm.notes',
    'sm.created_at',
    'sm.created_by'
]);

$output = $result['output'];
$rResult = $result['rResult'];

foreach ($rResult as $aRow) {
    $row = [];
    
    // Date
    $row[] = _dt($aRow['created_at']);
    
    // Product name
    $row[] = $aRow['product_name'];
    
    // Location name
    $row[] = $aRow['location_name'];
    
    // Movement type with badge
    $movement_type = $aRow['movement_type'];
    $badge_class = '';
    switch ($movement_type) {
        case 'in':
            $badge_class = 'success';
            $movement_type = _l('stock_in');
            break;
        case 'out':
            $badge_class = 'danger';
            $movement_type = _l('stock_out');
            break;
        case 'transfer':
            $badge_class = 'info';
            $movement_type = _l('transfer');
            break;
        case 'adjustment':
            $badge_class = 'warning';
            $movement_type = _l('adjustment');
            break;
    }
    $row[] = '<span class="label label-' . $badge_class . '">' . $movement_type . '</span>';
    
    // Quantity with +/- indicator
    $quantity = $aRow['quantity'];
    $quantity_class = $aRow['movement_type'] == 'in' ? 'text-success' : 'text-danger';
    $quantity_sign = $aRow['movement_type'] == 'in' ? '+' : '-';
    $row[] = '<span class="' . $quantity_class . '">' . $quantity_sign . $quantity . '</span>';
    
    // Unit cost
    $unit_cost = $aRow['unit_cost'];
    $row[] = $unit_cost ? app_format_money($unit_cost, get_base_currency()) : '-';
    
    // Reference
    $reference = '';
    if ($aRow['reference_type'] && $aRow['reference_id']) {
        $reference = ucfirst($aRow['reference_type']) . ' #' . $aRow['reference_id'];
    }
    $row[] = $reference ?: '-';
    
    // Notes
    $notes = $aRow['notes'];
    $row[] = $notes ? '<span title="' . htmlspecialchars($notes) . '">' . 
             (strlen($notes) > 50 ? substr($notes, 0, 50) . '...' : $notes) . '</span>' : '-';
    
    // Created by
    $row[] = $aRow['created_by_name'];
    
    $output['aaData'][] = $row;
}
