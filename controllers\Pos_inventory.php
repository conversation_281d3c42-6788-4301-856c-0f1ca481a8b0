<?php

defined('BASEPATH') or exit('No direct script access allowed');

/**
 * Main POS Inventory Controller
 */
class Pos_inventory extends AdminController
{
    public function __construct()
    {
        parent::__construct();
        $this->load->model('pos_inventory_model');
        $this->load->model('pos_products_model');
        $this->load->model('pos_transactions_model');
        $this->load->model('pos_purchase_orders_model');
        $this->load->model('pos_integrations_model');
        
        // Check if user has permission to access this module
        if (!has_permission('pos_inventory', '', 'view')) {
            access_denied('pos_inventory');
        }
    }

    /**
     * Main dashboard
     */
    public function index()
    {
        if (!has_permission('pos_inventory', '', 'view')) {
            access_denied('pos_inventory');
        }

        $data['title'] = _l('pos_inventory_dashboard');
        
        // Get dashboard statistics
        $data['total_products'] = $this->pos_products_model->get_total_products();
        $data['total_stock_value'] = $this->pos_inventory_model->get_total_stock_value();
        $data['low_stock_products'] = $this->pos_inventory_model->get_low_stock_products_count();
        $data['today_sales'] = $this->pos_transactions_model->get_today_sales();
        $data['pending_purchase_orders'] = $this->pos_purchase_orders_model->get_pending_count();
        
        // Get recent transactions
        $data['recent_transactions'] = $this->pos_transactions_model->get_recent_transactions(10);
        
        // Get top selling products
        $data['top_selling_products'] = $this->pos_products_model->get_top_selling_products(10);
        
        // Get low stock alerts
        $data['low_stock_alerts'] = $this->pos_inventory_model->get_low_stock_products(10);

        $this->load->view('dashboard', $data);
    }

    /**
     * Dashboard (alias for index)
     */
    public function dashboard()
    {
        $this->index();
    }

    /**
     * POS Interface
     */
    public function pos()
    {
        if (!has_permission('pos_transactions', '', 'create')) {
            access_denied('pos_transactions');
        }

        // Get current location
        $current_location = $this->pos_inventory_model->get_default_location();
        if (!$current_location) {
            // If no default location, get first available location
            $locations = $this->pos_inventory_model->get_locations();
            $current_location = !empty($locations) ? $locations[0] : null;
        }

        if (!$current_location) {
            set_alert('danger', _l('no_locations_configured'));
            redirect(admin_url('pos_inventory/locations'));
            return;
        }

        // Get products with stock information for current location
        $this->db->select('p.*, c.name as category_name,
                          COALESCE(i.quantity, 0) as stock_quantity,
                          COALESCE(i.reserved_quantity, 0) as reserved_quantity');
        $this->db->from(db_prefix() . 'pos_products p');
        $this->db->join(db_prefix() . 'pos_categories c', 'c.id = p.category_id', 'left');
        $this->db->join(db_prefix() . 'pos_inventory i', 'i.product_id = p.id AND i.location_id = ' . $current_location['id'], 'left');
        $this->db->where('p.status', 1);
        $this->db->order_by('p.name', 'ASC');
        $products = $this->db->get()->result_array();

        // Get customers
        $this->load->model('clients_model');
        $customers = $this->clients_model->get();

        $data['title'] = _l('pos_interface');
        $data['current_location'] = $current_location;
        $data['products'] = $products;
        $data['categories'] = $this->pos_products_model->get_categories();
        $data['customers'] = $customers;

        $this->load->view('pos/interface', $data);
    }

    /**
     * Products management
     */
    public function products()
    {
        if (!has_permission('pos_products', '', 'view')) {
            access_denied('pos_products');
        }

        $data['title'] = _l('products_management');
        $this->load->view('products/manage', $data);
    }

    /**
     * Categories management
     */
    public function categories()
    {
        if (!has_permission('pos_categories', '', 'view')) {
            access_denied('pos_categories');
        }

        $data['title'] = _l('categories');
        $this->load->view('categories/manage', $data);
    }

    /**
     * Transactions management
     */
    public function transactions()
    {
        if (!has_permission('pos_transactions', '', 'view')) {
            access_denied('pos_transactions');
        }

        $data['title'] = _l('transactions');
        $this->load->view('transactions/manage', $data);
    }

    /**
     * Add/Edit product
     */
    public function product($id = '')
    {
        if (!has_permission('pos_products', '', 'view')) {
            access_denied('pos_products');
        }

        if ($this->input->post()) {
            $data = $this->input->post();
            
            if ($id == '') {
                if (!has_permission('pos_products', '', 'create')) {
                    access_denied('pos_products');
                }
                try {
                    $id = $this->pos_products_model->add($data);
                    if ($id) {
                        set_alert('success', _l('added_successfully', _l('product')));
                        redirect(admin_url('pos_inventory/product/' . $id));
                    } else {
                        set_alert('danger', _l('error_adding', _l('product')));
                    }
                } catch (Exception $e) {
                    log_message('error', 'Product creation failed: ' . $e->getMessage());
                    set_alert('danger', _l('error_adding', _l('product')) . ': ' . _l('invalid_category_selected'));
                }
            } else {
                if (!has_permission('pos_products', '', 'edit')) {
                    access_denied('pos_products');
                }
                try {
                    $success = $this->pos_products_model->update($data, $id);
                    if ($success) {
                        set_alert('success', _l('updated_successfully', _l('product')));
                    } else {
                        set_alert('danger', _l('error_updating', _l('product')));
                    }
                } catch (Exception $e) {
                    log_message('error', 'Product update failed: ' . $e->getMessage());
                    set_alert('danger', _l('error_updating', _l('product')) . ': ' . _l('invalid_category_selected'));
                }
                redirect(admin_url('pos_inventory/product/' . $id));
            }
        }

        if ($id == '') {
            $title = _l('add_new_product');
        } else {
            $data['product'] = $this->pos_products_model->get($id);
            $title = _l('edit_product');
            if (!$data['product']) {
                blank_page();
            }
        }

        $data['title'] = $title;

        // Ensure at least one category exists
        $this->pos_products_model->ensure_default_category();

        $data['categories'] = $this->pos_products_model->get_categories();
        $data['suppliers'] = $this->pos_purchase_orders_model->get_suppliers();
        $data['locations'] = $this->pos_inventory_model->get_locations();

        $this->load->view('products/product', $data);
    }

    /**
     * Inventory management
     */
    public function inventory()
    {
        if (!has_permission('pos_inventory', '', 'view')) {
            access_denied('pos_inventory');
        }

        $data['title'] = _l('inventory_management');
        $data['locations'] = $this->pos_inventory_model->get_locations();
        $this->load->view('inventory/manage', $data);
    }

    /**
     * Stock adjustment
     */
    public function stock_adjustment($id = '')
    {
        if (!has_permission('pos_inventory', '', 'edit')) {
            access_denied('pos_inventory');
        }

        if ($this->input->post()) {
            $data = $this->input->post();
            $success = $this->pos_inventory_model->adjust_stock($data);
            if ($success) {
                set_alert('success', _l('stock_adjusted_successfully'));
            } else {
                set_alert('danger', _l('error_adjusting_stock'));
            }
            redirect(admin_url('pos_inventory/inventory'));
        }

        $data['title'] = _l('stock_adjustment');
        $data['products'] = $this->pos_products_model->get_all(['p.track_inventory' => 1]);
        $data['locations'] = $this->pos_inventory_model->get_locations();
        $data['adjustment_types'] = [
            'increase' => _l('increase'),
            'decrease' => _l('decrease'),
            'set' => _l('set_quantity')
        ];

        $this->load->view('inventory/stock_adjustment', $data);
    }

    /**
     * Stock transfer
     */
    public function stock_transfer()
    {
        if (!has_permission('pos_inventory', '', 'edit')) {
            access_denied('pos_inventory');
        }

        if ($this->input->post()) {
            $data = $this->input->post();
            $success = $this->pos_inventory_model->transfer_stock($data);
            if ($success) {
                set_alert('success', _l('stock_transferred_successfully'));
            } else {
                set_alert('danger', _l('error_transferring_stock'));
            }
            redirect(admin_url('pos_inventory/inventory'));
        }

        $data['title'] = _l('stock_transfer');
        $data['products'] = $this->pos_products_model->get_all(['p.track_inventory' => 1]);
        $data['locations'] = $this->pos_inventory_model->get_locations();

        $this->load->view('inventory/stock_transfer', $data);
    }

    /**
     * Stock movements
     */
    public function stock_movements()
    {
        if (!has_permission('pos_inventory', '', 'view')) {
            access_denied('pos_inventory');
        }

        $data['title'] = _l('stock_movements');
        $this->load->view('inventory/stock_movements', $data);
    }

    /**
     * Purchase Orders
     */
    public function purchase_orders()
    {
        if (!has_permission('pos_purchase_orders', '', 'view')) {
            access_denied('pos_purchase_orders');
        }

        $data['title'] = _l('purchase_orders');
        $data['suppliers'] = $this->pos_purchase_orders_model->get_suppliers();
        $this->load->view('purchase_orders/manage', $data);
    }

    /**
     * Add/Edit purchase order
     */
    public function purchase_order($id = '')
    {
        if (!has_permission('pos_purchase_orders', '', 'view')) {
            access_denied('pos_purchase_orders');
        }

        if ($this->input->post()) {
            $data = $this->input->post();

            if ($id == '') {
                if (!has_permission('pos_purchase_orders', '', 'create')) {
                    access_denied('pos_purchase_orders');
                }
                $id = $this->pos_purchase_orders_model->add($data);
                if ($id) {
                    set_alert('success', _l('added_successfully', _l('purchase_order')));
                    redirect(admin_url('pos_inventory/purchase_order/' . $id));
                }
            } else {
                if (!has_permission('pos_purchase_orders', '', 'edit')) {
                    access_denied('pos_purchase_orders');
                }
                $success = $this->pos_purchase_orders_model->update($data, $id);
                if ($success) {
                    set_alert('success', _l('updated_successfully', _l('purchase_order')));
                }
                redirect(admin_url('pos_inventory/purchase_order/' . $id));
            }
        }

        if ($id == '') {
            $title = _l('add_new_purchase_order');
        } else {
            $data['purchase_order'] = $this->pos_purchase_orders_model->get($id);
            $title = _l('edit_purchase_order');
            if (!$data['purchase_order']) {
                blank_page();
            }

            // Get additional data for existing PO
            $data['documents'] = $this->pos_purchase_orders_model->get_documents($id);
            $data['backorders'] = $this->pos_purchase_orders_model->get_backorders($id);
        }

        $data['title'] = $title;
        $data['suppliers'] = $this->pos_purchase_orders_model->get_suppliers();
        $data['products'] = $this->pos_products_model->get_all();
        $data['locations'] = $this->pos_inventory_model->get_locations();

        $this->load->view('purchase_orders/purchase_order', $data);
    }

    /**
     * Receive stock for purchase order
     */
    public function receive_stock($po_id)
    {
        if (!has_permission('pos_purchase_orders', '', 'edit')) {
            access_denied('pos_purchase_orders');
        }

        $po = $this->pos_purchase_orders_model->get($po_id);
        if (!$po) {
            show_404();
        }

        if ($this->input->post()) {
            $received_items = $this->input->post('items');
            $delivery_date = $this->input->post('delivery_date');

            $success = $this->pos_purchase_orders_model->receive_stock($po_id, $received_items, $delivery_date);

            if ($success) {
                set_alert('success', _l('stock_received_successfully'));
            } else {
                set_alert('danger', _l('error_receiving_stock'));
            }

            redirect(admin_url('pos_inventory/purchase_order/' . $po_id));
        }

        $data['title'] = _l('receive_stock') . ' - ' . $po['po_number'];
        $data['purchase_order'] = $po;

        $this->load->view('purchase_orders/receive_stock', $data);
    }

    /**
     * Convert PO to invoice
     */
    public function convert_po_to_invoice($po_id)
    {
        if (!has_permission('pos_purchase_orders', '', 'edit')) {
            access_denied('pos_purchase_orders');
        }

        $invoice_id = $this->pos_purchase_orders_model->convert_to_invoice($po_id);

        if ($invoice_id) {
            set_alert('success', _l('po_converted_to_invoice_successfully'));
            redirect(admin_url('invoices/invoice/' . $invoice_id));
        } else {
            set_alert('danger', _l('error_converting_po_to_invoice'));
            redirect(admin_url('pos_inventory/purchase_order/' . $po_id));
        }
    }

    /**
     * Suppliers management
     */
    public function suppliers()
    {
        if (!has_permission('pos_suppliers', '', 'view')) {
            access_denied('pos_suppliers');
        }

        $data['title'] = _l('suppliers_management');
        $this->load->view('suppliers/manage', $data);
    }

    /**
     * Add/Edit supplier
     */
    public function supplier($id = '')
    {
        if (!has_permission('pos_suppliers', '', 'view')) {
            access_denied('pos_suppliers');
        }

        if ($this->input->post()) {
            $data = $this->input->post();

            if ($id == '') {
                if (!has_permission('pos_suppliers', '', 'create')) {
                    access_denied('pos_suppliers');
                }
                $id = $this->pos_purchase_orders_model->add_supplier($data);
                if ($id) {
                    set_alert('success', _l('added_successfully', _l('supplier')));
                    redirect(admin_url('pos_inventory/supplier/' . $id));
                }
            } else {
                if (!has_permission('pos_suppliers', '', 'edit')) {
                    access_denied('pos_suppliers');
                }
                $success = $this->pos_purchase_orders_model->update_supplier($data, $id);
                if ($success) {
                    set_alert('success', _l('updated_successfully', _l('supplier')));
                }
                redirect(admin_url('pos_inventory/supplier/' . $id));
            }
        }

        if ($id == '') {
            $title = _l('add_new_supplier');
        } else {
            $data['supplier'] = $this->pos_purchase_orders_model->get_supplier($id);
            $title = _l('edit_supplier');
            if (!$data['supplier']) {
                blank_page();
            }

            // Get supplier purchase history
            $data['purchase_history'] = $this->pos_purchase_orders_model->get_supplier_purchase_history($id, 10);
        }

        $data['title'] = $title;

        // Get Perfex CRM contacts for linking
        $this->load->model('clients_model');
        $data['perfex_contacts'] = $this->clients_model->get();

        // Get currencies for supplier form
        $data['currencies'] = $this->get_currencies_for_supplier();

        $this->load->view('suppliers/supplier', $data);
    }

    /**
     * Backorders management
     */
    public function backorders()
    {
        if (!has_permission('pos_purchase_orders', '', 'view')) {
            access_denied('pos_purchase_orders');
        }

        $data['title'] = _l('backorders_management');
        $this->load->view('purchase_orders/backorders', $data);
    }

    /**
     * Integrations
     */
    public function integrations()
    {
        if (!has_permission('pos_integrations', '', 'view')) {
            access_denied('pos_integrations');
        }

        $data['title'] = _l('integrations');
        $data['woocommerce_settings'] = $this->pos_integrations_model->get_woocommerce_settings();
        $data['shopify_settings'] = $this->pos_integrations_model->get_shopify_settings();
        $data['sync_logs'] = $this->pos_integrations_model->get_recent_sync_logs(20);
        $data['locations'] = $this->pos_inventory_model->get_locations();

        $this->load->view('integrations/manage', $data);
    }

    /**
     * Configure integration
     */
    public function configure_integration($platform)
    {
        if (!has_permission('pos_integrations', '', 'edit')) {
            access_denied('pos_integrations');
        }

        if (!in_array($platform, ['woocommerce', 'shopify'])) {
            show_404();
        }

        if ($this->input->post()) {
            $data = $this->input->post();
            $result = $this->pos_integrations_model->save_integration_settings($platform, $data);

            if ($result) {
                set_alert('success', _l('integration_settings_saved'));
            } else {
                set_alert('danger', _l('error_saving_integration_settings'));
            }

            redirect(admin_url('pos_inventory/integrations'));
        }

        $data['title'] = _l('configure_integration') . ' - ' . ucfirst($platform);
        $data['platform'] = $platform;
        $data['locations'] = $this->pos_inventory_model->get_locations();

        if ($platform == 'woocommerce') {
            $data['settings'] = $this->pos_integrations_model->get_woocommerce_settings();
        } else {
            $data['settings'] = $this->pos_integrations_model->get_shopify_settings();
        }

        $this->load->view('integrations/configure', $data);
    }

    /**
     * Product mappings
     */
    public function product_mappings($integration_id)
    {
        if (!has_permission('pos_integrations', '', 'view')) {
            access_denied('pos_integrations');
        }

        $data['title'] = _l('product_mappings');
        $data['integration_id'] = $integration_id;
        $data['categories'] = $this->pos_products_model->get_categories();
        $data['statistics'] = $this->pos_integrations_model->get_mapping_statistics($integration_id);

        $this->load->view('integrations/product_mappings', $data);
    }

    /**
     * Sync logs
     */
    public function sync_logs($integration_id = null)
    {
        if (!has_permission('pos_integrations', '', 'view')) {
            access_denied('pos_integrations');
        }

        $data['title'] = _l('sync_logs');
        $data['integration_id'] = $integration_id;
        $data['statistics'] = $this->pos_integrations_model->get_sync_log_statistics($integration_id);

        $this->load->view('integrations/sync_logs', $data);
    }

    /**
     * Reports
     */
    public function reports()
    {
        if (!has_permission('pos_inventory', '', 'view')) {
            access_denied('pos_inventory');
        }

        $data['title'] = _l('reports_analytics');
        $data['locations'] = $this->pos_inventory_model->get_locations();

        $this->load->view('reports/dashboard', $data);
    }

    /**
     * Settings
     */
    public function settings()
    {
        if (!is_admin()) {
            access_denied('settings');
        }

        if ($this->input->post()) {
            $data = $this->input->post();
            foreach ($data as $key => $value) {
                pos_inventory_update_option($key, $value);
            }
            set_alert('success', _l('settings_updated'));
            redirect(admin_url('pos_inventory/settings'));
        }

        $data['title'] = _l('pos_inventory_settings');
        $this->load->view('settings/manage', $data);
    }

    /**
     * Products table data for DataTables
     */
    public function products_table()
    {
        if (!has_permission('pos_products', '', 'view')) {
            ajax_access_denied();
        }

        $this->app->get_table_data(module_views_path(POS_INVENTORY_MODULE_NAME, 'products/table'));
    }

    /**
     * Get categories for AJAX
     */
    public function get_categories()
    {
        if (!has_permission('pos_products', '', 'view')) {
            ajax_access_denied();
        }

        $categories = $this->pos_products_model->get_categories();
        header('Content-Type: application/json');
        echo json_encode($categories);
    }

    /**
     * Get locations for AJAX
     */
    public function get_locations()
    {
        if (!has_permission('pos_inventory', '', 'view')) {
            ajax_access_denied();
        }

        $locations = $this->pos_inventory_model->get_locations();
        header('Content-Type: application/json');
        echo json_encode($locations);
    }

    /**
     * Search product by barcode
     */
    public function search_by_barcode()
    {
        if (!has_permission('pos_transactions', '', 'view')) {
            ajax_access_denied();
        }

        $barcode = $this->input->post('barcode');
        $product = $this->pos_products_model->get_by_barcode($barcode);

        if ($product) {
            // Get stock information
            $this->load->model('pos_inventory_model');
            $stock = $this->pos_inventory_model->get_product_stock($product['id']);
            $product['stock'] = $stock;

            echo json_encode(['success' => true, 'product' => $product]);
        } else {
            echo json_encode(['success' => false, 'message' => _l('product_not_found')]);
        }
    }

    /**
     * Complete POS sale
     */
    public function complete_sale()
    {
        if (!has_permission('pos_transactions', '', 'create')) {
            ajax_access_denied();
        }

        $data = $this->input->post();

        // Validate cart data
        if (empty($data['cart']) || !is_array($data['cart'])) {
            echo json_encode([
                'success' => false,
                'message' => _l('invalid_cart_data')
            ]);
            return;
        }

        // Start database transaction
        $this->db->trans_start();

        try {
            // Calculate totals
            $subtotal = 0;
            $total_items = 0;
            $items = [];

            foreach ($data['cart'] as $item) {
                $item_total = $item['price'] * $item['quantity'];
                $subtotal += $item_total;
                $total_items += $item['quantity'];

                $items[] = [
                    'product_id' => $item['id'],
                    'quantity' => $item['quantity'],
                    'unit_price' => $item['price'],
                    'total_price' => $item_total,
                    'cost_price' => $item['cost'] ?? 0
                ];

                // Validate stock availability
                if (isset($item['trackInventory']) && $item['trackInventory']) {
                    $current_stock = $this->pos_inventory_model->get_product_stock(
                        $item['id'],
                        $data['location_id'] ?? $this->pos_inventory_model->get_default_location()['id']
                    );

                    if ($current_stock < $item['quantity'] && !($item['allowBackorders'] ?? false)) {
                        throw new Exception(_l('insufficient_stock_for_product') . ': ' . $item['name']);
                    }
                }
            }

            // Apply discount
            $discount_amount = 0;
            if (isset($data['discount_value']) && $data['discount_value'] > 0) {
                if (($data['discount_type'] ?? 'percentage') === 'percentage') {
                    $discount_amount = ($subtotal * $data['discount_value']) / 100;
                } else {
                    $discount_amount = min($data['discount_value'], $subtotal);
                }
            }

            // Calculate tax
            $tax_rate = isset($data['tax_rate']) ? $data['tax_rate'] : (get_option('default_tax_rate') ?: 0);
            $taxable_amount = $subtotal - $discount_amount;
            $tax_amount = ($taxable_amount * $tax_rate) / 100;
            $total_amount = $taxable_amount + $tax_amount;

            // Validate payment amount for cash transactions
            if (($data['payment_method'] ?? 'cash') === 'cash') {
                $amount_received = $data['amount_received'] ?? 0;
                if ($amount_received < $total_amount) {
                    throw new Exception(_l('insufficient_payment_amount'));
                }
            }

            // Create transaction record
            $transaction_data = [
                'transaction_number' => $this->generate_transaction_number(),
                'customer_id' => !empty($data['customer']) ? $data['customer'] : null,
                'location_id' => $data['location_id'] ?? $this->pos_inventory_model->get_default_location()['id'],
                'staff_id' => get_staff_user_id(),
                'transaction_date' => date('Y-m-d H:i:s'),
                'subtotal' => $subtotal,
                'discount_type' => $data['discount_type'] ?? 'percentage',
                'discount_value' => $data['discount_value'] ?? 0,
                'discount_amount' => $discount_amount,
                'discount_reason' => $data['discount_reason'] ?? '',
                'tax_rate' => $tax_rate,
                'tax_amount' => $tax_amount,
                'total_amount' => $total_amount,
                'payment_method' => $data['payment_method'] ?? 'cash',
                'amount_received' => $data['amount_received'] ?? $total_amount,
                'change_amount' => max(0, ($data['amount_received'] ?? $total_amount) - $total_amount),
                'status' => 'completed',
                'notes' => $data['notes'] ?? '',
                'created_at' => date('Y-m-d H:i:s')
            ];

            $this->db->insert(db_prefix() . 'pos_transactions', $transaction_data);
            $transaction_id = $this->db->insert_id();

            if (!$transaction_id) {
                throw new Exception('Failed to create transaction');
            }

            // Process cart items and update inventory
            foreach ($items as $item) {
                $item['transaction_id'] = $transaction_id;
                $this->db->insert(db_prefix() . 'pos_transaction_items', $item);

                // Update inventory if tracking is enabled
                $product_data = null;
                foreach ($data['cart'] as $cart_item) {
                    if ($cart_item['id'] == $item['product_id']) {
                        $product_data = $cart_item;
                        break;
                    }
                }

                if ($product_data && isset($product_data['trackInventory']) && $product_data['trackInventory']) {
                    $this->pos_inventory_model->reduce_stock(
                        $item['product_id'],
                        $transaction_data['location_id'],
                        $item['quantity']
                    );
                }
            }

            $this->db->trans_complete();

            if ($this->db->trans_status() === FALSE) {
                throw new Exception('Transaction failed');
            }

            // Generate receipt data
            $receipt_data = $this->generate_receipt_data($transaction_id);

            echo json_encode([
                'success' => true,
                'message' => _l('sale_completed_successfully'),
                'transaction_id' => $transaction_id,
                'transaction_number' => $transaction_data['transaction_number'],
                'receipt_data' => $receipt_data,
                'receipt_url' => admin_url('pos_inventory/receipt/' . $transaction_id),
                'print_url' => admin_url('pos_inventory/print_receipt/' . $transaction_id)
            ]);

        } catch (Exception $e) {
            $this->db->trans_rollback();
            echo json_encode([
                'success' => false,
                'message' => _l('error_processing_sale') . ': ' . $e->getMessage()
            ]);
        }
    }

    /**
     * Hold order
     */
    public function hold_order()
    {
        if (!has_permission('pos_transactions', '', 'create')) {
            ajax_access_denied();
        }

        $data = $this->input->post();

        if (empty($data['cart']) || !is_array($data['cart'])) {
            echo json_encode([
                'success' => false,
                'message' => _l('invalid_cart_data')
            ]);
            return;
        }

        // Calculate order summary for held order
        $subtotal = 0;
        $total_items = 0;

        foreach ($data['cart'] as $item) {
            $subtotal += $item['price'] * $item['quantity'];
            $total_items += $item['quantity'];
        }

        $hold_data = [
            'order_number' => $this->generate_hold_order_number(),
            'customer_id' => !empty($data['customer']) ? $data['customer'] : null,
            'location_id' => $data['location_id'] ?? $this->pos_inventory_model->get_default_location()['id'],
            'staff_id' => get_staff_user_id(),
            'cart_data' => json_encode($data['cart']),
            'discount_data' => json_encode([
                'type' => $data['discount_type'] ?? 'percentage',
                'value' => $data['discount_value'] ?? 0,
                'reason' => $data['discount_reason'] ?? ''
            ]),
            'subtotal' => $subtotal,
            'items_count' => $total_items,
            'notes' => $data['notes'] ?? '',
            'created_at' => date('Y-m-d H:i:s')
        ];

        $this->db->insert(db_prefix() . 'pos_held_orders', $hold_data);
        $order_id = $this->db->insert_id();

        if ($order_id) {
            echo json_encode([
                'success' => true,
                'message' => _l('order_held_successfully'),
                'order_id' => $order_id,
                'order_number' => $hold_data['order_number']
            ]);
        } else {
            echo json_encode([
                'success' => false,
                'message' => _l('error_holding_order')
            ]);
        }
    }

    /**
     * Delete product
     */
    public function delete_product($id)
    {
        if (!has_permission('pos_products', '', 'delete')) {
            access_denied('pos_products');
        }

        if (!$id) {
            redirect(admin_url('pos_inventory/products'));
        }

        $response = $this->pos_products_model->delete($id);
        if ($response) {
            set_alert('success', _l('deleted', _l('product')));
        } else {
            set_alert('warning', _l('problem_deleting', _l('product')));
        }

        redirect(admin_url('pos_inventory/products'));
    }

    /**
     * Categories table data for DataTables
     */
    public function categories_table()
    {
        if (!has_permission('pos_categories', '', 'view')) {
            ajax_access_denied();
        }

        // Manual DataTables processing since Perfex might not have the datatables library
        $draw = $this->input->post('draw');
        $start = $this->input->post('start') ?: 0;
        $length = $this->input->post('length') ?: 10;
        $search = $this->input->post('search')['value'] ?? '';

        // Get total records
        $this->db->select('COUNT(*) as total');
        $this->db->from(db_prefix() . 'pos_categories');
        $total_records = $this->db->get()->row()->total;

        // Build query
        $this->db->select('c.id, c.name, c.description, c.status, c.created_at, COUNT(p.id) as products_count');
        $this->db->from(db_prefix() . 'pos_categories c');
        $this->db->join(db_prefix() . 'pos_products p', 'p.category_id = c.id', 'left');
        $this->db->group_by('c.id');

        // Search
        if (!empty($search)) {
            $this->db->group_start();
            $this->db->like('c.name', $search);
            $this->db->or_like('c.description', $search);
            $this->db->group_end();
        }

        // For simplicity, use total records as filtered records if search is empty
        $filtered_records = $total_records;
        if (!empty($search)) {
            // Count filtered records separately
            $this->db->select('COUNT(DISTINCT c.id) as count');
            $this->db->from(db_prefix() . 'pos_categories c');
            $this->db->group_start();
            $this->db->like('c.name', $search);
            $this->db->or_like('c.description', $search);
            $this->db->group_end();
            $filtered_records = $this->db->get()->row()->count;

            // Rebuild main query
            $this->db->select('c.id, c.name, c.description, c.status, c.created_at, COUNT(p.id) as products_count');
            $this->db->from(db_prefix() . 'pos_categories c');
            $this->db->join(db_prefix() . 'pos_products p', 'p.category_id = c.id', 'left');
            $this->db->group_by('c.id');
            $this->db->group_start();
            $this->db->like('c.name', $search);
            $this->db->or_like('c.description', $search);
            $this->db->group_end();
        }

        // Apply limit and offset
        $this->db->limit($length, $start);
        $this->db->order_by('c.name', 'ASC');

        $categories = $this->db->get()->result_array();

        // Format data
        $data = [];
        foreach ($categories as $category) {
            $status = $category['status'] == 1 ?
                '<span class="label label-success">' . _l('active') . '</span>' :
                '<span class="label label-default">' . _l('inactive') . '</span>';

            $options = '';
            if (has_permission('pos_categories', '', 'edit')) {
                $options .= '<a href="#" onclick="editCategory(' . $category['id'] . ', \'' .
                           addslashes($category['name']) . '\', \'' . addslashes($category['description']) . '\', ' .
                           $category['status'] . ')" class="btn btn-default btn-icon btn-xs" data-toggle="tooltip" title="' .
                           _l('edit') . '"><i class="fa fa-pencil-square-o"></i></a> ';
            }
            if (has_permission('pos_categories', '', 'delete')) {
                $options .= '<a href="#" onclick="deleteCategory(' . $category['id'] . ')" class="btn btn-danger btn-icon btn-xs" data-toggle="tooltip" title="' .
                           _l('delete') . '"><i class="fa fa-remove"></i></a>';
            }

            $data[] = [
                'name' => $category['name'],
                'description' => $category['description'] ?: '-',
                'products_count' => $category['products_count'],
                'status' => $status,
                'options' => $options
            ];
        }

        $response = [
            'draw' => intval($draw),
            'recordsTotal' => $total_records,
            'recordsFiltered' => $filtered_records,
            'data' => $data
        ];

        echo json_encode($response);
    }

    /**
     * Delete category
     */
    public function delete_category()
    {
        if (!has_permission('pos_categories', '', 'delete')) {
            ajax_access_denied();
        }

        $id = $this->input->post('id');
        if (!$id) {
            echo json_encode(['success' => false, 'message' => _l('category_not_found')]);
            return;
        }

        // Check if category has products
        $this->db->where('category_id', $id);
        $products_count = $this->db->count_all_results(db_prefix() . 'pos_products');

        if ($products_count > 0) {
            echo json_encode(['success' => false, 'message' => _l('category_has_products_cannot_delete')]);
            return;
        }

        $success = $this->pos_products_model->delete_category($id);
        $message = $success ? _l('deleted_successfully', _l('category')) : _l('error_deleting', _l('category'));

        echo json_encode(['success' => $success, 'message' => $message]);
    }

    /**
     * Manage categories
     */
    public function manage_categories()
    {
        if (!has_permission('pos_products', '', 'view')) {
            ajax_access_denied();
        }

        if ($this->input->post()) {
            $data = $this->input->post();

            if (isset($data['category_id']) && !empty($data['category_id'])) {
                // Update category
                $success = $this->pos_products_model->update_category($data, $data['category_id']);
                $message = $success ? _l('updated_successfully', _l('category')) : _l('error_updating', _l('category'));
            } else {
                // Add new category
                $category_id = $this->pos_products_model->add_category($data);
                $success = $category_id ? true : false;
                $message = $success ? _l('added_successfully', _l('category')) : _l('error_adding', _l('category'));
            }

            echo json_encode(['success' => $success, 'message' => $message]);
            return;
        }

        // Get categories for display
        $categories = $this->pos_products_model->get_categories_with_count();
        echo json_encode($categories);
    }

    /**
     * Inventory table data for DataTables
     */
    public function inventory_table()
    {
        if (!has_permission('pos_inventory', '', 'view')) {
            ajax_access_denied();
        }

        $this->app->get_table_data(module_views_path(POS_INVENTORY_MODULE_NAME, 'inventory/table'));
    }

    /**
     * Stock movements table data for DataTables
     */
    public function stock_movements_table()
    {
        if (!has_permission('pos_inventory', '', 'view')) {
            ajax_access_denied();
        }

        $this->app->get_table_data(module_views_path(POS_INVENTORY_MODULE_NAME, 'inventory/stock_movements_table'));
    }

    /**
     * Get inventory summary
     */
    public function inventory_summary()
    {
        if (!has_permission('pos_inventory', '', 'view')) {
            ajax_access_denied();
        }

        $summary = [
            'total_products' => $this->pos_products_model->get_total_products(),
            'total_stock_value' => app_format_money($this->pos_inventory_model->get_total_stock_value(), get_base_currency()),
            'low_stock_count' => $this->pos_inventory_model->get_low_stock_products_count(),
            'out_of_stock_count' => $this->get_out_of_stock_count()
        ];

        header('Content-Type: application/json');
        echo json_encode($summary);
    }

    /**
     * Get product stock for AJAX
     */
    public function get_product_stock()
    {
        if (!has_permission('pos_inventory', '', 'view')) {
            ajax_access_denied();
        }

        $product_id = $this->input->post('product_id');
        $location_id = $this->input->post('location_id');
        $variant_id = $this->input->post('variant_id');

        if (!$product_id || !$location_id) {
            echo json_encode(['success' => false, 'message' => 'Missing parameters']);
            return;
        }

        $stock = $this->pos_inventory_model->get_product_stock($product_id, $location_id, $variant_id);

        // Get product cost price for value calculation
        $product = $this->pos_products_model->get($product_id);
        $cost_price = $product ? $product['cost_price'] : 0;
        $stock['value'] = app_format_money($stock['quantity'] * $cost_price, get_base_currency());

        echo json_encode(['success' => true, 'stock' => $stock]);
    }

    /**
     * Get out of stock count
     */
    private function get_out_of_stock_count()
    {
        $this->db->select('COUNT(*) as count');
        $this->db->from(db_prefix() . 'pos_products p');
        $this->db->join(db_prefix() . 'pos_inventory i', 'i.product_id = p.id', 'left');
        $this->db->where('p.status', 1);
        $this->db->where('p.track_inventory', 1);
        $this->db->group_start();
        $this->db->where('i.quantity', 0);
        $this->db->or_where('i.quantity IS NULL');
        $this->db->group_end();
        $this->db->group_by('p.id');

        $result = $this->db->get();
        return $result->num_rows();
    }

    /**
     * Manage locations
     */
    public function locations()
    {
        if (!has_permission('pos_inventory', '', 'view')) {
            access_denied('pos_inventory');
        }

        // Get locations with statistics
        $this->db->select('l.*,
            COUNT(DISTINCT i.product_id) as products_count,
            SUM(i.quantity * p.cost_price) as stock_value');
        $this->db->from(db_prefix() . 'pos_locations l');
        $this->db->join(db_prefix() . 'pos_inventory i', 'i.location_id = l.id', 'left');
        $this->db->join(db_prefix() . 'pos_products p', 'p.id = i.product_id', 'left');
        $this->db->group_by('l.id');
        $this->db->order_by('l.is_default', 'DESC');
        $this->db->order_by('l.name', 'ASC');

        $data['locations'] = $this->db->get()->result_array();
        $data['title'] = _l('manage_locations');

        $this->load->view('inventory/locations', $data);
    }

    /**
     * Save location
     */
    public function save_location()
    {
        if (!has_permission('pos_inventory', '', 'edit')) {
            ajax_access_denied();
        }

        $data = $this->input->post();
        $location_id = isset($data['location_id']) ? $data['location_id'] : null;

        // If setting as default, remove default from other locations
        if (isset($data['is_default']) && $data['is_default'] == 1) {
            $this->db->update(db_prefix() . 'pos_locations', ['is_default' => 0]);
        }

        $location_data = [
            'name' => $data['name'],
            'address' => isset($data['address']) ? $data['address'] : '',
            'phone' => isset($data['phone']) ? $data['phone'] : '',
            'email' => isset($data['email']) ? $data['email'] : '',
            'is_default' => isset($data['is_default']) ? 1 : 0,
            'status' => isset($data['status']) ? 1 : 0
        ];

        if ($location_id) {
            // Update existing location
            $this->db->where('id', $location_id);
            $success = $this->db->update(db_prefix() . 'pos_locations', $location_data);
            $message = $success ? _l('location_updated_successfully') : _l('error_updating_location');
        } else {
            // Create new location
            $this->db->insert(db_prefix() . 'pos_locations', $location_data);
            $success = $this->db->insert_id() ? true : false;
            $message = $success ? _l('location_added_successfully') : _l('error_adding_location');
        }

        echo json_encode(['success' => $success, 'message' => $message]);
    }

    /**
     * Set default location
     */
    public function set_default_location($id)
    {
        if (!has_permission('pos_inventory', '', 'edit')) {
            access_denied('pos_inventory');
        }

        // Remove default from all locations
        $this->db->update(db_prefix() . 'pos_locations', ['is_default' => 0]);

        // Set new default
        $this->db->where('id', $id);
        $success = $this->db->update(db_prefix() . 'pos_locations', ['is_default' => 1]);

        if ($success) {
            set_alert('success', _l('default_location_updated'));
        } else {
            set_alert('danger', _l('error_updating_default_location'));
        }

        redirect(admin_url('pos_inventory/locations'));
    }

    /**
     * Delete location
     */
    public function delete_location($id)
    {
        if (!has_permission('pos_inventory', '', 'delete')) {
            access_denied('pos_inventory');
        }

        // Check if location has inventory
        $this->db->where('location_id', $id);
        $inventory_count = $this->db->count_all_results(db_prefix() . 'pos_inventory');

        if ($inventory_count > 0) {
            set_alert('warning', _l('cannot_delete_location_with_inventory'));
            redirect(admin_url('pos_inventory/locations'));
            return;
        }

        // Check if it's default location
        $this->db->where('id', $id);
        $this->db->where('is_default', 1);
        $is_default = $this->db->count_all_results(db_prefix() . 'pos_locations');

        if ($is_default > 0) {
            set_alert('warning', _l('cannot_delete_default_location'));
            redirect(admin_url('pos_inventory/locations'));
            return;
        }

        $this->db->where('id', $id);
        $success = $this->db->delete(db_prefix() . 'pos_locations');

        if ($success) {
            set_alert('success', _l('location_deleted_successfully'));
        } else {
            set_alert('danger', _l('error_deleting_location'));
        }

        redirect(admin_url('pos_inventory/locations'));
    }

    /**
     * Add customer from POS
     */
    public function add_customer()
    {
        if (!has_permission('customers', '', 'create')) {
            ajax_access_denied();
        }

        $data = $this->input->post();

        $customer_data = [
            'company' => $data['company'],
            'email' => isset($data['email']) ? $data['email'] : '',
            'phonenumber' => isset($data['phonenumber']) ? $data['phonenumber'] : '',
            'address' => isset($data['address']) ? $data['address'] : '',
            'active' => 1,
            'datecreated' => date('Y-m-d H:i:s')
        ];

        $this->load->model('clients_model');
        $customer_id = $this->clients_model->add($customer_data);

        if ($customer_id) {
            echo json_encode([
                'success' => true,
                'customer_id' => $customer_id,
                'message' => _l('customer_added_successfully')
            ]);
        } else {
            echo json_encode([
                'success' => false,
                'message' => _l('error_adding_customer')
            ]);
        }
    }

    /**
     * Get held orders
     */
    public function get_held_orders()
    {
        if (!has_permission('pos_transactions', '', 'view')) {
            ajax_access_denied();
        }

        $orders = $this->pos_transactions_model->get_held_orders();

        echo json_encode([
            'success' => true,
            'orders' => $orders
        ]);
    }

    /**
     * Get held orders count
     */
    public function get_held_orders_count()
    {
        if (!has_permission('pos_transactions', '', 'view')) {
            ajax_access_denied();
        }

        $count = $this->pos_transactions_model->get_held_orders_count();

        echo json_encode(['count' => $count]);
    }

    /**
     * Retrieve held order
     */
    public function retrieve_held_order()
    {
        if (!has_permission('pos_transactions', '', 'edit')) {
            ajax_access_denied();
        }

        $order_id = $this->input->post('order_id');
        $order = $this->pos_transactions_model->get_held_order($order_id);

        if ($order) {
            // Delete the held order
            $this->pos_transactions_model->delete_held_order($order_id);

            echo json_encode([
                'success' => true,
                'cart' => json_decode($order['cart_data'], true),
                'customer' => $order['customer_id'],
                'message' => _l('order_retrieved_successfully')
            ]);
        } else {
            echo json_encode([
                'success' => false,
                'message' => _l('error_retrieving_order')
            ]);
        }
    }

    // ===== AJAX ENDPOINTS FOR NEW FEATURES =====

    /**
     * Test integration connection
     */
    public function test_integration_connection()
    {
        if (!has_permission('pos_integrations', '', 'edit')) {
            ajax_access_denied();
        }

        $platform = $this->input->post('platform');
        $result = $this->pos_integrations_model->test_connection($platform);

        header('Content-Type: application/json');
        echo json_encode($result);
    }

    /**
     * Start sync process
     */
    public function start_sync()
    {
        if (!has_permission('pos_integrations', '', 'edit')) {
            ajax_access_denied();
        }

        $platform = $this->input->post('platform');
        $sync_type = $this->input->post('sync_type');

        $result = $this->pos_integrations_model->start_sync($platform, $sync_type);

        header('Content-Type: application/json');
        echo json_encode($result);
    }

    /**
     * Get sync statistics
     */
    public function get_sync_statistics()
    {
        if (!has_permission('pos_integrations', '', 'view')) {
            ajax_access_denied();
        }

        $integration_id = $this->input->post('integration_id');
        $days = $this->input->post('days') ?: 30;

        $stats = $this->pos_integrations_model->get_sync_statistics($integration_id, $days);

        header('Content-Type: application/json');
        echo json_encode(['success' => true, 'statistics' => $stats]);
    }

    /**
     * Product mappings table data
     */
    public function product_mappings_table($integration_id)
    {
        if (!has_permission('pos_integrations', '', 'view')) {
            ajax_access_denied();
        }

        // Manual DataTables processing
        $draw = $this->input->post('draw');
        $start = $this->input->post('start') ?: 0;
        $length = $this->input->post('length') ?: 10;
        $search = $this->input->post('search')['value'] ?? '';

        // Get filters
        $status_filter = $this->input->post('status_filter');
        $category_filter = $this->input->post('category_filter');
        $sync_status_filter = $this->input->post('sync_status_filter');

        // Build query
        $this->db->select('p.id, p.name, p.sku, p.category_id, c.name as category_name,
                          pm.platform_product_id, pm.platform_product_name, pm.sync_status, pm.last_sync');
        $this->db->from(db_prefix() . 'pos_products p');
        $this->db->join(db_prefix() . 'pos_categories c', 'c.id = p.category_id', 'left');
        $this->db->join(db_prefix() . 'pos_product_mappings pm', 'pm.pos_product_id = p.id AND pm.integration_id = ' . (int)$integration_id, 'left');
        $this->db->where('p.status', 1);

        // Apply filters
        if ($status_filter) {
            if ($status_filter == 'mapped') {
                $this->db->where('pm.id IS NOT NULL');
            } elseif ($status_filter == 'unmapped') {
                $this->db->where('pm.id IS NULL');
            }
        }

        if ($category_filter) {
            $this->db->where('p.category_id', $category_filter);
        }

        if ($sync_status_filter) {
            $this->db->where('pm.sync_status', $sync_status_filter);
        }

        // Search
        if (!empty($search)) {
            $this->db->group_start();
            $this->db->like('p.name', $search);
            $this->db->or_like('p.sku', $search);
            $this->db->or_like('pm.platform_product_name', $search);
            $this->db->group_end();
        }

        // Get total count
        $total_query = clone $this->db;
        $total_records = $total_query->count_all_results('', false);

        // Apply pagination
        $this->db->limit($length, $start);
        $this->db->order_by('p.name', 'ASC');

        $products = $this->db->get()->result_array();

        // Format data
        $data = [];
        foreach ($products as $product) {
            $is_mapped = !empty($product['platform_product_id']);

            $mapping_status = $is_mapped ?
                '<span class="label label-success">' . _l('mapped') . '</span>' :
                '<span class="label label-warning">' . _l('unmapped') . '</span>';

            $sync_status = '';
            if ($is_mapped) {
                switch ($product['sync_status']) {
                    case 'synced':
                        $sync_status = '<span class="label label-success">' . _l('synced') . '</span>';
                        break;
                    case 'pending':
                        $sync_status = '<span class="label label-info">' . _l('pending') . '</span>';
                        break;
                    case 'failed':
                        $sync_status = '<span class="label label-danger">' . _l('failed') . '</span>';
                        break;
                    default:
                        $sync_status = '<span class="label label-default">' . _l('unknown') . '</span>';
                }
            }

            $actions = '<input type="checkbox" class="product-checkbox" value="' . $product['id'] . '">';
            if ($is_mapped) {
                $actions .= ' <button class="btn btn-xs btn-warning" onclick="unmapProduct(' . $product['id'] . ')" title="' . _l('unmap') . '">
                            <i class="fa fa-unlink"></i></button>';
                $actions .= ' <button class="btn btn-xs btn-info" onclick="syncProduct(' . $product['id'] . ')" title="' . _l('sync') . '">
                            <i class="fa fa-refresh"></i></button>';
            } else {
                $actions .= ' <button class="btn btn-xs btn-primary" onclick="mapProduct(' . $product['id'] . ')" title="' . _l('map') . '">
                            <i class="fa fa-link"></i></button>';
            }

            $data[] = [
                'checkbox' => '<input type="checkbox" class="product-checkbox" value="' . $product['id'] . '">',
                'pos_product' => '<strong>' . $product['name'] . '</strong><br><small>' . $product['sku'] . '</small>',
                'platform_product' => $is_mapped ?
                    '<strong>' . $product['platform_product_name'] . '</strong><br><small>ID: ' . $product['platform_product_id'] . '</small>' :
                    '<span class="text-muted">' . _l('not_mapped') . '</span>',
                'mapping_status' => $mapping_status,
                'sync_status' => $sync_status,
                'last_sync' => $product['last_sync'] ? _dt($product['last_sync']) : _l('never'),
                'actions' => $actions
            ];
        }

        $response = [
            'draw' => intval($draw),
            'recordsTotal' => $total_records,
            'recordsFiltered' => $total_records,
            'data' => $data
        ];

        echo json_encode($response);
    }

    /**
     * Get integration statistics
     */
    public function get_integration_statistics()
    {
        if (!has_permission('pos_integrations', '', 'view')) {
            ajax_access_denied();
        }

        $stats = $this->pos_integrations_model->get_integration_statistics();
        echo json_encode(['success' => true, 'statistics' => $stats]);
    }



    /**
     * Sync logs table data
     */
    public function sync_logs_table()
    {
        if (!has_permission('pos_integrations', '', 'view')) {
            ajax_access_denied();
        }

        // Manual DataTables processing
        $draw = $this->input->post('draw');
        $start = $this->input->post('start') ?: 0;
        $length = $this->input->post('length') ?: 10;
        $search = $this->input->post('search')['value'] ?? '';

        // Get filters
        $platform_filter = $this->input->post('platform_filter');
        $sync_type_filter = $this->input->post('sync_type_filter');
        $status_filter = $this->input->post('status_filter');
        $date_from = $this->input->post('date_from');
        $date_to = $this->input->post('date_to');
        $integration_id = $this->input->post('integration_id');

        // Build query
        $this->db->select('sl.*, i.name as integration_name, i.platform');
        $this->db->from(db_prefix() . 'pos_sync_logs sl');
        $this->db->join(db_prefix() . 'pos_integrations i', 'i.id = sl.integration_id');

        // Apply filters
        if ($integration_id) {
            $this->db->where('sl.integration_id', $integration_id);
        }

        if ($platform_filter) {
            $this->db->where('i.platform', $platform_filter);
        }

        if ($sync_type_filter) {
            $this->db->where('sl.sync_type', $sync_type_filter);
        }

        if ($status_filter) {
            $this->db->where('sl.status', $status_filter);
        }

        if ($date_from) {
            $this->db->where('DATE(sl.started_at) >=', $date_from);
        }

        if ($date_to) {
            $this->db->where('DATE(sl.started_at) <=', $date_to);
        }

        // Search
        if (!empty($search)) {
            $this->db->group_start();
            $this->db->like('i.name', $search);
            $this->db->or_like('sl.sync_type', $search);
            $this->db->or_like('sl.message', $search);
            $this->db->group_end();
        }

        // Get total count
        $total_query = clone $this->db;
        $total_records = $total_query->count_all_results('', false);

        // Apply pagination
        $this->db->limit($length, $start);
        $this->db->order_by('sl.id', 'DESC');

        $logs = $this->db->get()->result_array();

        // Format data
        $data = [];
        foreach ($logs as $log) {
            $status_label = '';
            switch ($log['status']) {
                case 'success':
                    $status_label = '<span class="label label-success">' . _l('success') . '</span>';
                    break;
                case 'failed':
                    $status_label = '<span class="label label-danger">' . _l('failed') . '</span>';
                    break;
                case 'running':
                    $status_label = '<span class="label label-info">' . _l('running') . '</span>';
                    break;
                case 'cancelled':
                    $status_label = '<span class="label label-warning">' . _l('cancelled') . '</span>';
                    break;
                default:
                    $status_label = '<span class="label label-default">' . $log['status'] . '</span>';
            }

            $platform_icon = $log['platform'] == 'woocommerce' ? 'wordpress' : 'shopping-bag';
            $platform_display = '<i class="fa fa-' . $platform_icon . '"></i> ' . ucfirst($log['platform']);

            $duration = '';
            if ($log['completed_at']) {
                $start = new DateTime($log['started_at']);
                $end = new DateTime($log['completed_at']);
                $diff = $end->diff($start);
                $duration = $diff->format('%H:%I:%S');
            } elseif ($log['status'] == 'running') {
                $duration = '<i class="fa fa-spinner fa-spin"></i> ' . _l('running');
            }

            $success_rate = '';
            if ($log['total_records'] > 0) {
                $rate = round(($log['successful_records'] / $log['total_records']) * 100, 1);
                $success_rate = $rate . '%';
            }

            $actions = '<button class="btn btn-xs btn-info" onclick="viewSyncDetails(' . $log['id'] . ')" title="' . _l('view_details') . '">
                       <i class="fa fa-eye"></i></button>';

            if ($log['status'] == 'running') {
                $actions .= ' <button class="btn btn-xs btn-warning" onclick="cancelSync(' . $log['id'] . ')" title="' . _l('cancel') . '">
                            <i class="fa fa-stop"></i></button>';
            } elseif ($log['status'] == 'failed') {
                $actions .= ' <button class="btn btn-xs btn-success" onclick="retrySyncLog(' . $log['id'] . ')" title="' . _l('retry') . '">
                            <i class="fa fa-refresh"></i></button>';
            }

            $actions .= ' <button class="btn btn-xs btn-danger" onclick="deleteSyncLog(' . $log['id'] . ')" title="' . _l('delete') . '">
                        <i class="fa fa-remove"></i></button>';

            $data[] = [
                'id' => $log['id'],
                'platform' => $platform_display,
                'sync_type' => ucfirst($log['sync_type']),
                'status' => $status_label,
                'records' => $log['total_records'] . ' / ' . $log['successful_records'],
                'success_rate' => $success_rate,
                'started_at' => _dt($log['started_at']),
                'duration' => $duration,
                'actions' => $actions
            ];
        }

        $response = [
            'draw' => intval($draw),
            'recordsTotal' => $total_records,
            'recordsFiltered' => $total_records,
            'data' => $data
        ];

        echo json_encode($response);
    }

    /**
     * Get sync log details
     */
    public function get_sync_log_details()
    {
        if (!has_permission('pos_integrations', '', 'view')) {
            ajax_access_denied();
        }

        $log_id = $this->input->post('log_id');
        $log = $this->pos_integrations_model->get_sync_log($log_id);

        if (!$log) {
            echo json_encode(['success' => false, 'message' => _l('log_not_found')]);
            return;
        }

        // Get integration details
        $integration = $this->pos_integrations_model->get_integration($log['integration_id']);

        // Format log details
        $html = '<div class="row">';
        $html .= '<div class="col-md-6">';
        $html .= '<h5>' . _l('sync_details') . '</h5>';
        $html .= '<table class="table table-striped">';
        $html .= '<tr><td><strong>' . _l('id') . ':</strong></td><td>' . $log['id'] . '</td></tr>';
        $html .= '<tr><td><strong>' . _l('integration') . ':</strong></td><td>' . $integration['name'] . '</td></tr>';
        $html .= '<tr><td><strong>' . _l('platform') . ':</strong></td><td>' . ucfirst($integration['platform']) . '</td></tr>';
        $html .= '<tr><td><strong>' . _l('sync_type') . ':</strong></td><td>' . ucfirst($log['sync_type']) . '</td></tr>';
        $html .= '<tr><td><strong>' . _l('status') . ':</strong></td><td>' . ucfirst($log['status']) . '</td></tr>';
        $html .= '<tr><td><strong>' . _l('started_at') . ':</strong></td><td>' . _dt($log['started_at']) . '</td></tr>';

        if ($log['completed_at']) {
            $html .= '<tr><td><strong>' . _l('completed_at') . ':</strong></td><td>' . _dt($log['completed_at']) . '</td></tr>';

            // Calculate duration
            $start = new DateTime($log['started_at']);
            $end = new DateTime($log['completed_at']);
            $diff = $end->diff($start);
            $html .= '<tr><td><strong>' . _l('duration') . ':</strong></td><td>' . $diff->format('%H:%I:%S') . '</td></tr>';
        }

        $html .= '<tr><td><strong>' . _l('total_records') . ':</strong></td><td>' . $log['total_records'] . '</td></tr>';
        $html .= '<tr><td><strong>' . _l('successful_records') . ':</strong></td><td>' . $log['successful_records'] . '</td></tr>';
        $html .= '<tr><td><strong>' . _l('failed_records') . ':</strong></td><td>' . ($log['total_records'] - $log['successful_records']) . '</td></tr>';

        if ($log['total_records'] > 0) {
            $success_rate = round(($log['successful_records'] / $log['total_records']) * 100, 1);
            $html .= '<tr><td><strong>' . _l('success_rate') . ':</strong></td><td>' . $success_rate . '%</td></tr>';
        }

        $html .= '</table>';
        $html .= '</div>';

        $html .= '<div class="col-md-6">';
        $html .= '<h5>' . _l('log_message') . '</h5>';
        $html .= '<div class="well" style="max-height: 300px; overflow-y: auto;">';
        $html .= nl2br($log['message'] ?: _l('no_message'));
        $html .= '</div>';

        if (!empty($log['error'])) {
            $html .= '<h5>' . _l('error_details') . '</h5>';
            $html .= '<div class="well well-danger" style="max-height: 300px; overflow-y: auto;">';
            $html .= nl2br($log['error']);
            $html .= '</div>';
        }

        $html .= '</div>';
        $html .= '</div>';

        echo json_encode(['success' => true, 'html' => $html, 'log' => $log]);
    }

    /**
     * Retry sync log
     */
    public function retry_sync_log()
    {
        if (!has_permission('pos_integrations', '', 'edit')) {
            ajax_access_denied();
        }

        $log_id = $this->input->post('log_id');
        $result = $this->pos_integrations_model->retry_sync($log_id);

        echo json_encode($result);
    }

    /**
     * Cancel sync
     */
    public function cancel_sync()
    {
        if (!has_permission('pos_integrations', '', 'edit')) {
            ajax_access_denied();
        }

        $log_id = $this->input->post('log_id');
        $result = $this->pos_integrations_model->cancel_sync($log_id);

        echo json_encode($result);
    }

    /**
     * Delete sync log
     */
    public function delete_sync_log()
    {
        if (!has_permission('pos_integrations', '', 'delete')) {
            ajax_access_denied();
        }

        $log_id = $this->input->post('log_id');
        $result = $this->pos_integrations_model->delete_sync_log($log_id);

        echo json_encode($result);
    }

    /**
     * Clear old sync logs
     */
    public function clear_old_sync_logs()
    {
        if (!has_permission('pos_integrations', '', 'delete')) {
            ajax_access_denied();
        }

        $days = $this->input->post('days') ?: 30;
        $result = $this->pos_integrations_model->clear_old_sync_logs($days);

        echo json_encode($result);
    }

    /**
     * Export sync logs
     */
    public function export_sync_logs()
    {
        if (!has_permission('pos_integrations', '', 'view')) {
            access_denied('pos_integrations');
        }

        // Get filters
        $platform_filter = $this->input->get('platform');
        $sync_type_filter = $this->input->get('sync_type');
        $status_filter = $this->input->get('status');
        $date_from = $this->input->get('date_from');
        $date_to = $this->input->get('date_to');
        $integration_id = $this->input->get('integration_id');

        $logs = $this->pos_integrations_model->get_sync_logs_for_export(
            $integration_id,
            $platform_filter,
            $sync_type_filter,
            $status_filter,
            $date_from,
            $date_to
        );

        // Create CSV
        $this->load->library('csv');

        $header = [
            _l('id'),
            _l('integration'),
            _l('platform'),
            _l('sync_type'),
            _l('status'),
            _l('total_records'),
            _l('successful_records'),
            _l('failed_records'),
            _l('success_rate'),
            _l('started_at'),
            _l('completed_at'),
            _l('duration'),
            _l('message'),
            _l('error')
        ];

        $this->csv->set_header($header);

        foreach ($logs as $log) {
            $duration = '';
            if ($log['completed_at']) {
                $start = new DateTime($log['started_at']);
                $end = new DateTime($log['completed_at']);
                $diff = $end->diff($start);
                $duration = $diff->format('%H:%I:%S');
            }

            $success_rate = '';
            if ($log['total_records'] > 0) {
                $success_rate = round(($log['successful_records'] / $log['total_records']) * 100, 1) . '%';
            }

            $row = [
                $log['id'],
                $log['integration_name'],
                ucfirst($log['platform']),
                ucfirst($log['sync_type']),
                ucfirst($log['status']),
                $log['total_records'],
                $log['successful_records'],
                $log['total_records'] - $log['successful_records'],
                $success_rate,
                _dt($log['started_at']),
                $log['completed_at'] ? _dt($log['completed_at']) : '',
                $duration,
                $log['message'],
                $log['error']
            ];

            $this->csv->add_row($row);
        }

        $this->csv->download('sync_logs_' . date('Y-m-d'));
    }

    /**
     * Get POS product info
     */
    public function get_pos_product_info()
    {
        if (!has_permission('pos_integrations', '', 'view')) {
            ajax_access_denied();
        }

        $product_id = $this->input->post('product_id');
        $product = $this->pos_products_model->get_product($product_id);

        if (!$product) {
            echo json_encode(['success' => false, 'message' => _l('product_not_found')]);
            return;
        }

        // Get category
        $category = $this->pos_products_model->get_category($product['category_id']);
        $category_name = $category ? $category['name'] : '';

        // Format product info
        $html = '<div class="product-info">';
        $html .= '<p><strong>' . _l('name') . ':</strong> ' . $product['name'] . '</p>';
        $html .= '<p><strong>' . _l('sku') . ':</strong> ' . $product['sku'] . '</p>';
        $html .= '<p><strong>' . _l('category') . ':</strong> ' . $category_name . '</p>';
        $html .= '<p><strong>' . _l('price') . ':</strong> ' . app_format_money($product['price'], get_base_currency()) . '</p>';
        $html .= '<p><strong>' . _l('cost_price') . ':</strong> ' . app_format_money($product['cost_price'], get_base_currency()) . '</p>';

        // Get inventory
        $inventory = $this->pos_inventory_model->get_product_inventory($product_id);
        $total_stock = 0;
        foreach ($inventory as $item) {
            $total_stock += $item['quantity'];
        }

        $html .= '<p><strong>' . _l('total_stock') . ':</strong> ' . $total_stock . '</p>';
        $html .= '</div>';

        echo json_encode(['success' => true, 'html' => $html]);
    }

    /**
     * Search platform products
     */
    public function search_platform_products()
    {
        if (!has_permission('pos_integrations', '', 'view')) {
            ajax_access_denied();
        }

        $integration_id = $this->input->post('integration_id');
        $query = $this->input->post('query');

        $products = $this->pos_integrations_model->search_platform_products($integration_id, $query);

        echo json_encode(['success' => true, 'products' => $products]);
    }

    /**
     * Save product mapping
     */
    public function save_product_mapping()
    {
        if (!has_permission('pos_integrations', '', 'edit')) {
            ajax_access_denied();
        }

        $pos_product_id = $this->input->post('pos_product_id');
        $platform_product_id = $this->input->post('platform_product_id');
        $integration_id = $this->input->post('integration_id');
        $sync_options = [
            'sync_price' => $this->input->post('sync_price') ? 1 : 0,
            'sync_inventory' => $this->input->post('sync_inventory') ? 1 : 0,
            'sync_description' => $this->input->post('sync_description') ? 1 : 0,
            'sync_images' => $this->input->post('sync_images') ? 1 : 0
        ];

        $result = $this->pos_integrations_model->save_product_mapping(
            $integration_id,
            $pos_product_id,
            $platform_product_id,
            $sync_options
        );

        echo json_encode($result);
    }

    /**
     * Unmap product
     */
    public function unmap_product()
    {
        if (!has_permission('pos_integrations', '', 'edit')) {
            ajax_access_denied();
        }

        $mapping_id = $this->input->post('mapping_id');
        $result = $this->pos_integrations_model->unmap_product($mapping_id);

        echo json_encode($result);
    }

    /**
     * Sync single product
     */
    public function sync_single_product()
    {
        if (!has_permission('pos_integrations', '', 'edit')) {
            ajax_access_denied();
        }

        $mapping_id = $this->input->post('mapping_id');
        $result = $this->pos_integrations_model->sync_single_product($mapping_id);

        echo json_encode($result);
    }

    /**
     * Auto map products
     */
    public function auto_map_products()
    {
        if (!has_permission('pos_integrations', '', 'edit')) {
            ajax_access_denied();
        }

        $integration_id = $this->input->post('integration_id');
        $result = $this->pos_integrations_model->auto_map_products($integration_id);

        echo json_encode($result);
    }

    /**
     * Sync mapped products
     */
    public function sync_mapped_products()
    {
        if (!has_permission('pos_integrations', '', 'edit')) {
            ajax_access_denied();
        }

        $integration_id = $this->input->post('integration_id');
        $result = $this->pos_integrations_model->sync_mapped_products($integration_id);

        echo json_encode($result);
    }

    /**
     * Bulk unmap products
     */
    public function bulk_unmap_products()
    {
        if (!has_permission('pos_integrations', '', 'edit')) {
            ajax_access_denied();
        }

        $product_ids = $this->input->post('product_ids');
        $result = $this->pos_integrations_model->bulk_unmap_products($product_ids);

        echo json_encode($result);
    }

    /**
     * Bulk sync products
     */
    public function bulk_sync_products()
    {
        if (!has_permission('pos_integrations', '', 'edit')) {
            ajax_access_denied();
        }

        $product_ids = $this->input->post('product_ids');
        $result = $this->pos_integrations_model->bulk_sync_products($product_ids);

        echo json_encode($result);
    }

    /**
     * Upload purchase order document
     */
    public function upload_po_document()
    {
        if (!has_permission('pos_purchase_orders', '', 'edit')) {
            ajax_access_denied();
        }

        $po_id = $this->input->post('po_id');

        if (!$po_id) {
            echo json_encode(['success' => false, 'message' => 'Purchase order ID required']);
            return;
        }

        $config['upload_path'] = './uploads/pos_documents/';
        $config['allowed_types'] = 'pdf|doc|docx|jpg|jpeg|png|gif';
        $config['max_size'] = 10240; // 10MB
        $config['encrypt_name'] = true;

        // Create directory if it doesn't exist
        if (!is_dir($config['upload_path'])) {
            mkdir($config['upload_path'], 0755, true);
        }

        $this->load->library('upload', $config);

        if ($this->upload->do_upload('document')) {
            $upload_data = $this->upload->data();

            $document_data = [
                'document_name' => $this->input->post('document_name') ?: $upload_data['orig_name'],
                'original_filename' => $upload_data['orig_name'],
                'file_path' => $upload_data['full_path'],
                'file_size' => $upload_data['file_size'],
                'mime_type' => $upload_data['file_type'],
                'document_type' => $this->input->post('document_type') ?: 'other',
                'description' => $this->input->post('description') ?: ''
            ];

            $document_id = $this->pos_purchase_orders_model->add_document($po_id, $document_data);

            if ($document_id) {
                echo json_encode([
                    'success' => true,
                    'message' => _l('document_uploaded_successfully'),
                    'document_id' => $document_id
                ]);
            } else {
                echo json_encode(['success' => false, 'message' => _l('error_uploading_document')]);
            }
        } else {
            echo json_encode(['success' => false, 'message' => $this->upload->display_errors('', '')]);
        }
    }

    /**
     * Delete purchase order document
     */
    public function delete_po_document()
    {
        if (!has_permission('pos_purchase_orders', '', 'delete')) {
            ajax_access_denied();
        }

        $document_id = $this->input->post('document_id');

        // Get document info before deletion
        $this->db->where('id', $document_id);
        $document = $this->db->get(db_prefix() . 'pos_purchase_order_documents')->row_array();

        if ($document) {
            $success = $this->pos_purchase_orders_model->delete_document($document_id);

            if ($success) {
                // Delete physical file
                if (file_exists($document['file_path'])) {
                    unlink($document['file_path']);
                }

                echo json_encode(['success' => true, 'message' => _l('document_deleted_successfully')]);
            } else {
                echo json_encode(['success' => false, 'message' => _l('error_deleting_document')]);
            }
        } else {
            echo json_encode(['success' => false, 'message' => _l('document_not_found')]);
        }
    }

    /**
     * Update tracking number
     */
    public function update_tracking()
    {
        if (!has_permission('pos_purchase_orders', '', 'edit')) {
            ajax_access_denied();
        }

        $po_id = $this->input->post('po_id');
        $tracking_number = $this->input->post('tracking_number');
        $shipping_method = $this->input->post('shipping_method');

        $success = $this->pos_purchase_orders_model->update_tracking($po_id, $tracking_number, $shipping_method);

        if ($success) {
            echo json_encode(['success' => true, 'message' => _l('tracking_updated_successfully')]);
        } else {
            echo json_encode(['success' => false, 'message' => _l('error_updating_tracking')]);
        }
    }

    /**
     * Delete held order
     */
    public function delete_held_order()
    {
        if (!has_permission('pos_transactions', '', 'delete')) {
            ajax_access_denied();
        }

        $order_id = $this->input->post('order_id');
        $success = $this->pos_transactions_model->delete_held_order($order_id);

        echo json_encode([
            'success' => $success,
            'message' => $success ? _l('held_order_deleted') : _l('error_deleting_held_order')
        ]);
    }

    /**
     * Quick stock adjustment from POS
     */
    public function quick_stock_adjustment()
    {
        if (!has_permission('pos_inventory', '', 'edit')) {
            ajax_access_denied();
        }

        $data = $this->input->post();
        $success = $this->pos_inventory_model->adjust_stock($data);

        echo json_encode([
            'success' => $success,
            'message' => $success ? _l('stock_adjusted_successfully') : _l('error_adjusting_stock')
        ]);
    }

    /**
     * Bulk stock adjustment
     */
    public function bulk_stock_adjustment()
    {
        if (!has_permission('pos_inventory', '', 'edit')) {
            ajax_access_denied();
        }

        $data = $this->input->post();
        $product_ids = explode(',', $data['product_ids']);

        $success_count = 0;
        foreach ($product_ids as $product_id) {
            $adjustment_data = [
                'product_id' => $product_id,
                'location_id' => $data['location_id'],
                'adjustment_type' => $data['adjustment_type'],
                'quantity' => $data['quantity'],
                'reason' => $data['reason'],
                'notes' => $data['notes']
            ];

            if ($this->pos_inventory_model->adjust_stock($adjustment_data)) {
                $success_count++;
            }
        }

        $total_products = count($product_ids);
        $message = "$success_count of $total_products products adjusted successfully";

        echo json_encode([
            'success' => $success_count > 0,
            'message' => $message
        ]);
    }

    /**
     * Low stock table for DataTables
     */
    public function low_stock_table()
    {
        if (!has_permission('pos_inventory', '', 'view')) {
            ajax_access_denied();
        }

        // Get low stock products
        $this->db->select('p.id, p.name as product_name, p.sku, p.low_stock_threshold,
                          l.name as location_name, i.quantity as current_stock');
        $this->db->from(db_prefix() . 'pos_products p');
        $this->db->join(db_prefix() . 'pos_inventory i', 'i.product_id = p.id');
        $this->db->join(db_prefix() . 'pos_locations l', 'l.id = i.location_id');
        $this->db->where('p.status', 1);
        $this->db->where('p.track_inventory', 1);
        $this->db->where('i.quantity <=', 'p.low_stock_threshold', false);
        $this->db->order_by('i.quantity', 'ASC');

        $products = $this->db->get()->result_array();

        $output = [
            'draw' => intval($this->input->post('draw')),
            'recordsTotal' => count($products),
            'recordsFiltered' => count($products),
            'data' => []
        ];

        foreach ($products as $product) {
            $actions = '';
            if (has_permission('pos_inventory', '', 'edit')) {
                $actions = '<a href="' . admin_url('pos_inventory/stock_adjustment?product=' . $product['id']) . '"
                               class="btn btn-sm btn-primary">' . _l('adjust_stock') . '</a>';
            }

            $output['data'][] = [
                'product_name' => $product['product_name'],
                'location_name' => $product['location_name'],
                'current_stock' => '<span class="label label-warning">' . $product['current_stock'] . '</span>',
                'threshold' => $product['low_stock_threshold'],
                'actions' => $actions
            ];
        }

        echo json_encode($output);
    }





    /**
     * Generate transaction number
     */
    private function generate_transaction_number()
    {
        $prefix = get_option('pos_transaction_prefix', 'TXN');
        $date = date('Ymd');

        // Get next sequence number for today
        $this->db->select('COUNT(*) + 1 as next_number');
        $this->db->from(db_prefix() . 'pos_transactions');
        $this->db->where('DATE(transaction_date)', date('Y-m-d'));
        $result = $this->db->get()->row();

        $sequence = str_pad($result->next_number, 4, '0', STR_PAD_LEFT);

        return $prefix . '-' . $date . '-' . $sequence;
    }

    /**
     * Generate hold order number
     */
    private function generate_hold_order_number()
    {
        $prefix = get_option('pos_hold_order_prefix', 'HOLD');
        $date = date('Ymd');

        // Get next sequence number for today
        $this->db->select('COUNT(*) + 1 as next_number');
        $this->db->from(db_prefix() . 'pos_held_orders');
        $this->db->where('DATE(created_at)', date('Y-m-d'));
        $result = $this->db->get()->row();

        $sequence = str_pad($result->next_number, 4, '0', STR_PAD_LEFT);

        return $prefix . '-' . $date . '-' . $sequence;
    }

    /**
     * Generate receipt data
     */
    private function generate_receipt_data($transaction_id)
    {
        // Get transaction details
        $this->db->select('t.*, COALESCE(c.company, CONCAT(co.firstname, " ", co.lastname), "Walk-in Customer") as customer_name,
                          COALESCE(c.email, co.email) as customer_email,
                          l.name as location_name, l.address as location_address,
                          s.firstname, s.lastname');
        $this->db->from(db_prefix() . 'pos_transactions t');
        $this->db->join(db_prefix() . 'clients c', 'c.userid = t.customer_id', 'left');
        $this->db->join(db_prefix() . 'contacts co', 'co.userid = c.userid AND co.is_primary = 1', 'left');
        $this->db->join(db_prefix() . 'pos_locations l', 'l.id = t.location_id', 'left');
        $this->db->join(db_prefix() . 'staff s', 's.staffid = t.staff_id', 'left');
        $this->db->where('t.id', $transaction_id);
        $transaction = $this->db->get()->row_array();

        if (!$transaction) {
            return null;
        }

        // Get transaction items
        $this->db->select('ti.*, p.name as product_name, p.sku');
        $this->db->from(db_prefix() . 'pos_transaction_items ti');
        $this->db->join(db_prefix() . 'pos_products p', 'p.id = ti.product_id');
        $this->db->where('ti.transaction_id', $transaction_id);
        $items = $this->db->get()->result_array();

        return [
            'transaction' => $transaction,
            'items' => $items,
            'company' => [
                'name' => get_option('companyname'),
                'address' => get_option('company_address'),
                'phone' => get_option('company_phonenumber'),
                'email' => get_option('company_email'),
                'website' => get_option('company_website')
            ]
        ];
    }

    /**
     * Display receipt
     */
    public function receipt($transaction_id)
    {
        if (!has_permission('pos_transactions', '', 'view')) {
            access_denied('pos_transactions');
        }

        $data['receipt_data'] = $this->generate_receipt_data($transaction_id);

        if (!$data['receipt_data']) {
            show_404();
            return;
        }

        $data['title'] = _l('receipt') . ' - ' . $data['receipt_data']['transaction']['transaction_number'];

        $this->load->view('pos/receipt', $data);
    }

    /**
     * Print receipt
     */
    public function print_receipt($transaction_id)
    {
        if (!has_permission('pos_transactions', '', 'view')) {
            access_denied('pos_transactions');
        }

        $data['receipt_data'] = $this->generate_receipt_data($transaction_id);

        if (!$data['receipt_data']) {
            show_404();
            return;
        }

        $data['title'] = _l('receipt') . ' - ' . $data['receipt_data']['transaction']['transaction_number'];

        $this->load->view('pos/print_receipt', $data);
    }

    /**
     * Email receipt
     */
    public function email_receipt()
    {
        if (!has_permission('pos_transactions', '', 'view')) {
            ajax_access_denied();
        }

        $transaction_id = $this->input->post('transaction_id');
        $email = $this->input->post('email');

        if (!$transaction_id || !$email) {
            echo json_encode([
                'success' => false,
                'message' => _l('missing_required_fields')
            ]);
            return;
        }

        $receipt_data = $this->generate_receipt_data($transaction_id);

        if (!$receipt_data) {
            echo json_encode([
                'success' => false,
                'message' => _l('transaction_not_found')
            ]);
            return;
        }

        // Generate receipt HTML
        $this->load->view('pos/email_receipt_template', ['receipt_data' => $receipt_data]);
        $receipt_html = $this->output->get_output();

        // Send email
        $this->load->library('email');
        $this->email->from(get_option('smtp_email'), get_option('companyname'));
        $this->email->to($email);
        $this->email->subject(_l('receipt') . ' - ' . $receipt_data['transaction']['transaction_number']);
        $this->email->message($receipt_html);

        if ($this->email->send()) {
            echo json_encode([
                'success' => true,
                'message' => _l('receipt_sent_successfully')
            ]);
        } else {
            echo json_encode([
                'success' => false,
                'message' => _l('error_sending_receipt')
            ]);
        }
    }



    /**
     * Get dashboard KPIs
     */
    public function get_dashboard_kpis()
    {
        if (!has_permission('pos_reports', '', 'view')) {
            ajax_access_denied();
        }

        $date_from = $this->input->get('date_from') ?: date('Y-m-01');
        $date_to = $this->input->get('date_to') ?: date('Y-m-d');
        $location_id = $this->input->get('location_id');

        // Build base query
        $this->db->select('
            COUNT(*) as total_transactions,
            SUM(total_amount) as total_sales,
            AVG(total_amount) as average_sale,
            SUM((SELECT SUM(quantity) FROM ' . db_prefix() . 'pos_transaction_items ti WHERE ti.transaction_id = t.id)) as items_sold
        ');
        $this->db->from(db_prefix() . 'pos_transactions t');
        $this->db->where('t.status', 'completed');
        $this->db->where('DATE(t.transaction_date) >=', $date_from);
        $this->db->where('DATE(t.transaction_date) <=', $date_to);

        if ($location_id) {
            $this->db->where('t.location_id', $location_id);
        }

        $current_period = $this->db->get()->row_array();

        // Get previous period for comparison
        $days_diff = (strtotime($date_to) - strtotime($date_from)) / (60 * 60 * 24);
        $prev_date_from = date('Y-m-d', strtotime($date_from . ' -' . ($days_diff + 1) . ' days'));
        $prev_date_to = date('Y-m-d', strtotime($date_from . ' -1 day'));

        $this->db->select('
            COUNT(*) as total_transactions,
            SUM(total_amount) as total_sales,
            AVG(total_amount) as average_sale,
            SUM((SELECT SUM(quantity) FROM ' . db_prefix() . 'pos_transaction_items ti WHERE ti.transaction_id = t.id)) as items_sold
        ');
        $this->db->from(db_prefix() . 'pos_transactions t');
        $this->db->where('t.status', 'completed');
        $this->db->where('DATE(t.transaction_date) >=', $prev_date_from);
        $this->db->where('DATE(t.transaction_date) <=', $prev_date_to);

        if ($location_id) {
            $this->db->where('t.location_id', $location_id);
        }

        $previous_period = $this->db->get()->row_array();

        // Calculate changes
        $kpis = [
            'total_sales' => [
                'value' => $current_period['total_sales'] ?: 0,
                'change' => $this->calculate_percentage_change(
                    $previous_period['total_sales'] ?: 0,
                    $current_period['total_sales'] ?: 0
                )
            ],
            'total_transactions' => [
                'value' => $current_period['total_transactions'] ?: 0,
                'change' => $this->calculate_percentage_change(
                    $previous_period['total_transactions'] ?: 0,
                    $current_period['total_transactions'] ?: 0
                )
            ],
            'average_sale' => [
                'value' => $current_period['average_sale'] ?: 0,
                'change' => $this->calculate_percentage_change(
                    $previous_period['average_sale'] ?: 0,
                    $current_period['average_sale'] ?: 0
                )
            ],
            'items_sold' => [
                'value' => $current_period['items_sold'] ?: 0,
                'change' => $this->calculate_percentage_change(
                    $previous_period['items_sold'] ?: 0,
                    $current_period['items_sold'] ?: 0
                )
            ]
        ];

        echo json_encode(['success' => true, 'kpis' => $kpis]);
    }

    /**
     * Get sales trend data
     */
    public function get_sales_trend()
    {
        if (!has_permission('pos_reports', '', 'view')) {
            ajax_access_denied();
        }

        $period = $this->input->get('period') ?: 90;
        $location_id = $this->input->get('location_id');
        $date_from = date('Y-m-d', strtotime('-' . $period . ' days'));
        $date_to = date('Y-m-d');

        $this->db->select('DATE(transaction_date) as date, SUM(total_amount) as sales, COUNT(*) as transactions');
        $this->db->from(db_prefix() . 'pos_transactions');
        $this->db->where('status', 'completed');
        $this->db->where('DATE(transaction_date) >=', $date_from);
        $this->db->where('DATE(transaction_date) <=', $date_to);

        if ($location_id) {
            $this->db->where('location_id', $location_id);
        }

        $this->db->group_by('DATE(transaction_date)');
        $this->db->order_by('DATE(transaction_date)', 'ASC');

        $results = $this->db->get()->result_array();

        // Fill missing dates with zero values
        $data = [];
        $current_date = strtotime($date_from);
        $end_date = strtotime($date_to);

        while ($current_date <= $end_date) {
            $date_str = date('Y-m-d', $current_date);
            $found = false;

            foreach ($results as $result) {
                if ($result['date'] == $date_str) {
                    $data[] = [
                        'date' => $date_str,
                        'sales' => floatval($result['sales']),
                        'transactions' => intval($result['transactions'])
                    ];
                    $found = true;
                    break;
                }
            }

            if (!$found) {
                $data[] = [
                    'date' => $date_str,
                    'sales' => 0,
                    'transactions' => 0
                ];
            }

            $current_date = strtotime('+1 day', $current_date);
        }

        echo json_encode(['success' => true, 'data' => $data]);
    }

    /**
     * Get payment methods data
     */
    public function get_payment_methods_data()
    {
        if (!has_permission('pos_reports', '', 'view')) {
            ajax_access_denied();
        }

        $date_from = $this->input->get('date_from') ?: date('Y-m-01');
        $date_to = $this->input->get('date_to') ?: date('Y-m-d');
        $location_id = $this->input->get('location_id');

        $this->db->select('payment_method, COUNT(*) as count, SUM(total_amount) as total');
        $this->db->from(db_prefix() . 'pos_transactions');
        $this->db->where('status', 'completed');
        $this->db->where('DATE(transaction_date) >=', $date_from);
        $this->db->where('DATE(transaction_date) <=', $date_to);

        if ($location_id) {
            $this->db->where('location_id', $location_id);
        }

        $this->db->group_by('payment_method');
        $this->db->order_by('total', 'DESC');

        $results = $this->db->get()->result_array();

        echo json_encode(['success' => true, 'data' => $results]);
    }

    /**
     * Get top products data
     */
    public function get_top_products_data()
    {
        if (!has_permission('pos_reports', '', 'view')) {
            ajax_access_denied();
        }

        $date_from = $this->input->get('date_from') ?: date('Y-m-01');
        $date_to = $this->input->get('date_to') ?: date('Y-m-d');
        $location_id = $this->input->get('location_id');

        $this->db->select('p.name as product_name, SUM(ti.quantity) as quantity_sold, SUM(ti.total_price) as total_sales');
        $this->db->from(db_prefix() . 'pos_transaction_items ti');
        $this->db->join(db_prefix() . 'pos_transactions t', 't.id = ti.transaction_id');
        $this->db->join(db_prefix() . 'pos_products p', 'p.id = ti.product_id');
        $this->db->where('t.status', 'completed');
        $this->db->where('DATE(t.transaction_date) >=', $date_from);
        $this->db->where('DATE(t.transaction_date) <=', $date_to);

        if ($location_id) {
            $this->db->where('t.location_id', $location_id);
        }

        $this->db->group_by('ti.product_id');
        $this->db->order_by('quantity_sold', 'DESC');
        $this->db->limit(10);

        $results = $this->db->get()->result_array();

        echo json_encode(['success' => true, 'data' => $results]);
    }

    /**
     * Get hourly sales data
     */
    public function get_hourly_sales_data()
    {
        if (!has_permission('pos_reports', '', 'view')) {
            ajax_access_denied();
        }

        $date_from = $this->input->get('date_from') ?: date('Y-m-01');
        $date_to = $this->input->get('date_to') ?: date('Y-m-d');
        $location_id = $this->input->get('location_id');

        $this->db->select('HOUR(transaction_date) as hour, SUM(total_amount) as sales, COUNT(*) as transactions');
        $this->db->from(db_prefix() . 'pos_transactions');
        $this->db->where('status', 'completed');
        $this->db->where('DATE(transaction_date) >=', $date_from);
        $this->db->where('DATE(transaction_date) <=', $date_to);

        if ($location_id) {
            $this->db->where('location_id', $location_id);
        }

        $this->db->group_by('HOUR(transaction_date)');
        $this->db->order_by('hour', 'ASC');

        $results = $this->db->get()->result_array();

        // Fill missing hours with zero values
        $data = [];
        for ($hour = 0; $hour < 24; $hour++) {
            $found = false;
            foreach ($results as $result) {
                if ($result['hour'] == $hour) {
                    $data[] = [
                        'hour' => $hour,
                        'sales' => floatval($result['sales']),
                        'transactions' => intval($result['transactions'])
                    ];
                    $found = true;
                    break;
                }
            }

            if (!$found) {
                $data[] = [
                    'hour' => $hour,
                    'sales' => 0,
                    'transactions' => 0
                ];
            }
        }

        echo json_encode(['success' => true, 'data' => $data]);
    }

    /**
     * Export dashboard report
     */
    public function export_dashboard_report()
    {
        if (!has_permission('pos_reports', '', 'view')) {
            access_denied('pos_reports');
        }

        $format = $this->input->get('format') ?: 'pdf';
        $date_from = $this->input->get('date_from') ?: date('Y-m-01');
        $date_to = $this->input->get('date_to') ?: date('Y-m-d');
        $location_id = $this->input->get('location_id');

        // Get report data
        $report_data = $this->generate_dashboard_report_data($date_from, $date_to, $location_id);

        switch ($format) {
            case 'pdf':
                $this->export_pdf_report($report_data);
                break;
            case 'excel':
                $this->export_excel_report($report_data);
                break;
            case 'csv':
                $this->export_csv_report($report_data);
                break;
            default:
                show_404();
        }
    }

    /**
     * Generate dashboard report data
     */
    private function generate_dashboard_report_data($date_from, $date_to, $location_id = null)
    {
        // Get KPIs
        $this->db->select('
            COUNT(*) as total_transactions,
            SUM(total_amount) as total_sales,
            AVG(total_amount) as average_sale,
            SUM((SELECT SUM(quantity) FROM ' . db_prefix() . 'pos_transaction_items ti WHERE ti.transaction_id = t.id)) as items_sold
        ');
        $this->db->from(db_prefix() . 'pos_transactions t');
        $this->db->where('t.status', 'completed');
        $this->db->where('DATE(t.transaction_date) >=', $date_from);
        $this->db->where('DATE(t.transaction_date) <=', $date_to);

        if ($location_id) {
            $this->db->where('t.location_id', $location_id);
        }

        $kpis = $this->db->get()->row_array();

        // Get top products
        $this->db->select('p.name as product_name, SUM(ti.quantity) as quantity_sold, SUM(ti.total_price) as total_sales');
        $this->db->from(db_prefix() . 'pos_transaction_items ti');
        $this->db->join(db_prefix() . 'pos_transactions t', 't.id = ti.transaction_id');
        $this->db->join(db_prefix() . 'pos_products p', 'p.id = ti.product_id');
        $this->db->where('t.status', 'completed');
        $this->db->where('DATE(t.transaction_date) >=', $date_from);
        $this->db->where('DATE(t.transaction_date) <=', $date_to);

        if ($location_id) {
            $this->db->where('t.location_id', $location_id);
        }

        $this->db->group_by('ti.product_id');
        $this->db->order_by('quantity_sold', 'DESC');
        $this->db->limit(10);

        $top_products = $this->db->get()->result_array();

        // Get payment methods
        $this->db->select('payment_method, COUNT(*) as count, SUM(total_amount) as total');
        $this->db->from(db_prefix() . 'pos_transactions');
        $this->db->where('status', 'completed');
        $this->db->where('DATE(transaction_date) >=', $date_from);
        $this->db->where('DATE(transaction_date) <=', $date_to);

        if ($location_id) {
            $this->db->where('location_id', $location_id);
        }

        $this->db->group_by('payment_method');
        $this->db->order_by('total', 'DESC');

        $payment_methods = $this->db->get()->result_array();

        return [
            'period' => ['from' => $date_from, 'to' => $date_to],
            'location_id' => $location_id,
            'kpis' => $kpis,
            'top_products' => $top_products,
            'payment_methods' => $payment_methods
        ];
    }

    /**
     * Export PDF report
     */
    private function export_pdf_report($data)
    {
        // Implementation for PDF export
        // This would use a PDF library like TCPDF or mPDF
        $this->load->view('reports/pdf_export', ['data' => $data]);
    }

    /**
     * Export Excel report
     */
    private function export_excel_report($data)
    {
        // Implementation for Excel export
        // This would use a library like PhpSpreadsheet
        header('Content-Type: application/vnd.ms-excel');
        header('Content-Disposition: attachment;filename="pos_report_' . date('Y-m-d') . '.xls"');

        $this->load->view('reports/excel_export', ['data' => $data]);
    }

    /**
     * Export CSV report
     */
    private function export_csv_report($data)
    {
        header('Content-Type: text/csv');
        header('Content-Disposition: attachment;filename="pos_report_' . date('Y-m-d') . '.csv"');

        $this->load->view('reports/csv_export', ['data' => $data]);
    }

    /**
     * Calculate percentage change
     */
    private function calculate_percentage_change($old_value, $new_value)
    {
        if ($old_value == 0) {
            return $new_value > 0 ? 100 : 0;
        }

        return round((($new_value - $old_value) / $old_value) * 100, 2);
    }

    // ===== DATA TABLE ENDPOINTS =====

    /**
     * Purchase orders table data
     */
    public function purchase_orders_table()
    {
        if (!has_permission('pos_purchase_orders', '', 'view')) {
            ajax_access_denied();
        }

        $this->app->get_table_data(module_views_path(POS_INVENTORY_MODULE_NAME, 'tables/purchase_orders'));
    }

    /**
     * Suppliers table data
     */
    public function suppliers_table()
    {
        if (!has_permission('pos_suppliers', '', 'view')) {
            ajax_access_denied();
        }

        $this->app->get_table_data(module_views_path(POS_INVENTORY_MODULE_NAME, 'tables/suppliers'));
    }

    /**
     * Backorders table data
     */
    public function backorders_table()
    {
        if (!has_permission('pos_purchase_orders', '', 'view')) {
            ajax_access_denied();
        }

        $this->app->get_table_data(module_views_path(POS_INVENTORY_MODULE_NAME, 'tables/backorders'));
    }





    // ===== UTILITY METHODS =====

    /**
     * Get purchase order statistics
     */
    public function get_po_statistics()
    {
        if (!has_permission('pos_purchase_orders', '', 'view')) {
            ajax_access_denied();
        }

        $date_from = $this->input->post('date_from');
        $date_to = $this->input->post('date_to');

        $stats = $this->pos_purchase_orders_model->get_statistics($date_from, $date_to);

        header('Content-Type: application/json');
        echo json_encode(['success' => true, 'statistics' => $stats]);
    }

    /**
     * Get supplier details for AJAX
     */
    public function get_supplier_details()
    {
        if (!has_permission('pos_suppliers', '', 'view')) {
            ajax_access_denied();
        }

        $supplier_id = $this->input->post('supplier_id');
        $supplier = $this->pos_purchase_orders_model->get_supplier($supplier_id);

        if ($supplier) {
            echo json_encode(['success' => true, 'supplier' => $supplier]);
        } else {
            echo json_encode(['success' => false, 'message' => _l('supplier_not_found')]);
        }
    }

    /**
     * Get supplier statistics
     */
    public function get_supplier_statistics()
    {
        if (!has_permission('pos_suppliers', '', 'view')) {
            ajax_access_denied();
        }

        $statistics = $this->pos_purchase_orders_model->get_supplier_statistics();
        echo json_encode(['success' => true, 'statistics' => $statistics]);
    }

    /**
     * Get detailed supplier statistics
     */
    public function get_supplier_statistics_detail()
    {
        if (!has_permission('pos_suppliers', '', 'view')) {
            ajax_access_denied();
        }

        $supplier_id = $this->input->post('supplier_id');
        $statistics = $this->pos_purchase_orders_model->get_supplier_statistics_detail($supplier_id);
        echo json_encode(['success' => true, 'statistics' => $statistics]);
    }

    /**
     * Delete supplier
     */
    public function delete_supplier()
    {
        if (!has_permission('pos_suppliers', '', 'delete')) {
            ajax_access_denied();
        }

        $id = $this->input->post('id');

        // Check if supplier has any purchase orders
        $this->db->where('supplier_id', $id);
        $orders_count = $this->db->count_all_results(db_prefix() . 'pos_purchase_orders');

        if ($orders_count > 0) {
            echo json_encode([
                'success' => false,
                'message' => _l('supplier_has_orders_cannot_delete')
            ]);
            return;
        }

        $success = $this->pos_purchase_orders_model->delete_supplier($id);

        if ($success) {
            echo json_encode([
                'success' => true,
                'message' => _l('supplier_deleted_successfully')
            ]);
        } else {
            echo json_encode([
                'success' => false,
                'message' => _l('error_deleting_supplier')
            ]);
        }
    }

    /**
     * Toggle supplier status
     */
    public function toggle_supplier_status()
    {
        if (!has_permission('pos_suppliers', '', 'edit')) {
            ajax_access_denied();
        }

        $id = $this->input->post('id');
        $status = $this->input->post('status');

        $success = $this->pos_purchase_orders_model->update_supplier(['status' => $status], $id);

        if ($success) {
            $message = $status ? _l('supplier_activated_successfully') : _l('supplier_deactivated_successfully');
            echo json_encode(['success' => true, 'message' => $message]);
        } else {
            echo json_encode(['success' => false, 'message' => _l('error_updating_supplier')]);
        }
    }

    /**
     * Bulk supplier actions
     */
    public function bulk_supplier_action()
    {
        if (!has_permission('pos_suppliers', '', 'edit')) {
            ajax_access_denied();
        }

        $action = $this->input->post('action');
        $ids = $this->input->post('ids');

        if (empty($ids) || !is_array($ids)) {
            echo json_encode(['success' => false, 'message' => _l('no_suppliers_selected')]);
            return;
        }

        $success_count = 0;
        $total_count = count($ids);

        switch ($action) {
            case 'activate':
                foreach ($ids as $id) {
                    if ($this->pos_purchase_orders_model->update_supplier(['status' => 1], $id)) {
                        $success_count++;
                    }
                }
                $message = sprintf(_l('suppliers_activated_successfully'), $success_count, $total_count);
                break;

            case 'deactivate':
                foreach ($ids as $id) {
                    if ($this->pos_purchase_orders_model->update_supplier(['status' => 0], $id)) {
                        $success_count++;
                    }
                }
                $message = sprintf(_l('suppliers_deactivated_successfully'), $success_count, $total_count);
                break;

            case 'delete':
                if (!has_permission('pos_suppliers', '', 'delete')) {
                    ajax_access_denied();
                }
                foreach ($ids as $id) {
                    // Check if supplier has orders
                    $this->db->where('supplier_id', $id);
                    $orders_count = $this->db->count_all_results(db_prefix() . 'pos_purchase_orders');

                    if ($orders_count == 0 && $this->pos_purchase_orders_model->delete_supplier($id)) {
                        $success_count++;
                    }
                }
                $message = sprintf(_l('suppliers_deleted_successfully'), $success_count, $total_count);
                break;

            case 'link_perfex':
                // This would open a modal to link suppliers to Perfex contacts
                $message = _l('perfex_linking_feature_coming_soon');
                break;

            default:
                echo json_encode(['success' => false, 'message' => _l('invalid_action')]);
                return;
        }

        echo json_encode([
            'success' => $success_count > 0,
            'message' => $message,
            'processed' => $success_count,
            'total' => $total_count
        ]);
    }

    /**
     * Export suppliers
     */
    public function export_suppliers()
    {
        if (!has_permission('pos_suppliers', '', 'view')) {
            access_denied('pos_suppliers');
        }

        // Get filters
        $status = $this->input->get('status');
        $perfex_linked = $this->input->get('perfex_linked');
        $country = $this->input->get('country');
        $search = $this->input->get('search');

        $suppliers = $this->pos_purchase_orders_model->get_suppliers_for_export(
            $status, $perfex_linked, $country, $search
        );

        // Create CSV
        $this->load->library('csv');

        $header = [
            _l('name'),
            _l('company'),
            _l('email'),
            _l('phone'),
            _l('website'),
            _l('address'),
            _l('city'),
            _l('state'),
            _l('zip_code'),
            _l('country'),
            _l('tax_number'),
            _l('payment_terms'),
            _l('credit_limit'),
            _l('currency'),
            _l('discount_percentage'),
            _l('perfex_contact_id'),
            _l('status'),
            _l('created_at')
        ];

        $this->csv->set_header($header);

        foreach ($suppliers as $supplier) {
            $row = [
                $supplier['name'],
                $supplier['company'],
                $supplier['email'],
                $supplier['phone'],
                $supplier['website'],
                $supplier['address'],
                $supplier['city'],
                $supplier['state'],
                $supplier['zip'],
                $supplier['country_name'],
                $supplier['tax_number'],
                $supplier['payment_terms'],
                $supplier['credit_limit'],
                $supplier['currency_name'],
                $supplier['discount_percentage'],
                $supplier['perfex_contact_id'],
                $supplier['status'] ? _l('active') : _l('inactive'),
                _dt($supplier['created_at'])
            ];

            $this->csv->add_row($row);
        }

        $this->csv->download('suppliers_' . date('Y-m-d'));
    }

    /**
     * Import suppliers
     */
    public function import_suppliers()
    {
        if (!has_permission('pos_suppliers', '', 'create')) {
            ajax_access_denied();
        }

        if (!isset($_FILES['import_file']) || $_FILES['import_file']['error'] !== UPLOAD_ERR_OK) {
            echo json_encode(['success' => false, 'message' => _l('no_file_uploaded')]);
            return;
        }

        $update_existing = $this->input->post('update_existing');
        $auto_link_perfex = $this->input->post('auto_link_perfex');

        $result = $this->pos_purchase_orders_model->import_suppliers_from_csv(
            $_FILES['import_file']['tmp_name'],
            $update_existing,
            $auto_link_perfex
        );

        echo json_encode($result);
    }

    /**
     * Download supplier template
     */
    public function download_supplier_template()
    {
        if (!has_permission('pos_suppliers', '', 'view')) {
            access_denied('pos_suppliers');
        }

        $this->load->library('csv');

        $header = [
            'name',
            'company',
            'email',
            'phone',
            'website',
            'address',
            'city',
            'state',
            'zip',
            'country_id',
            'tax_number',
            'payment_terms',
            'credit_limit',
            'currency_id',
            'discount_percentage',
            'description',
            'status'
        ];

        $this->csv->set_header($header);

        // Add sample data
        $sample_row = [
            'Sample Supplier',
            'Sample Company Ltd',
            '<EMAIL>',
            '+1234567890',
            'https://example.com',
            '123 Main Street',
            'New York',
            'NY',
            '10001',
            '233', // US country ID
            'TAX123456',
            '30',
            '10000.00',
            '1', // Default currency ID
            '5.00',
            'Sample supplier description',
            '1'
        ];

        $this->csv->add_row($sample_row);
        $this->csv->download('supplier_import_template');
    }

    /**
     * Link supplier to Perfex contact
     */
    public function link_supplier_to_perfex($supplier_id)
    {
        if (!has_permission('pos_suppliers', '', 'edit')) {
            access_denied('pos_suppliers');
        }

        $data['supplier'] = $this->pos_purchase_orders_model->get_supplier($supplier_id);

        if (!$data['supplier']) {
            show_404();
        }

        $this->load->model('clients_model');
        $data['perfex_contacts'] = $this->clients_model->get();
        $data['title'] = _l('link_supplier_to_perfex');

        $this->load->view('suppliers/link_perfex', $data);
    }

    /**
     * Save supplier Perfex link
     */
    public function save_supplier_perfex_link()
    {
        if (!has_permission('pos_suppliers', '', 'edit')) {
            ajax_access_denied();
        }

        $supplier_id = $this->input->post('supplier_id');
        $contact_id = $this->input->post('contact_id');

        $data = ['perfex_contact_id' => $contact_id];
        $success = $this->pos_purchase_orders_model->update_supplier($data, $supplier_id);

        if ($success) {
            $message = $contact_id ? _l('contact_linked_successfully') : _l('contact_unlinked_successfully');
            echo json_encode(['success' => true, 'message' => $message]);
        } else {
            echo json_encode(['success' => false, 'message' => _l('error_updating_supplier')]);
        }
    }

    /**
     * Get suggested Perfex matches
     */
    public function get_suggested_perfex_matches()
    {
        if (!has_permission('pos_suppliers', '', 'view')) {
            ajax_access_denied();
        }

        $supplier_id = $this->input->post('supplier_id');
        $supplier = $this->pos_purchase_orders_model->get_supplier($supplier_id);

        if (!$supplier) {
            echo json_encode(['success' => false, 'message' => _l('supplier_not_found')]);
            return;
        }

        $this->load->model('clients_model');
        $contacts = $this->clients_model->get();

        $matches = [];

        foreach ($contacts as $contact) {
            $score = 0;
            $reasons = [];

            // Check company name similarity
            if (!empty($supplier['company']) && !empty($contact['company'])) {
                $similarity = 0;
                similar_text(strtolower($supplier['company']), strtolower($contact['company']), $similarity);
                if ($similarity > 70) {
                    $score += $similarity * 0.4;
                    $reasons[] = _l('company_name_match') . ' (' . round($similarity) . '%)';
                }
            }

            // Check email match
            if (!empty($supplier['email']) && !empty($contact['email'])) {
                if (strtolower($supplier['email']) === strtolower($contact['email'])) {
                    $score += 40;
                    $reasons[] = _l('email_exact_match');
                }
            }

            // Check phone match
            if (!empty($supplier['phone']) && !empty($contact['phonenumber'])) {
                $supplier_phone = preg_replace('/[^0-9]/', '', $supplier['phone']);
                $contact_phone = preg_replace('/[^0-9]/', '', $contact['phonenumber']);
                if ($supplier_phone === $contact_phone) {
                    $score += 30;
                    $reasons[] = _l('phone_exact_match');
                }
            }

            // Check address similarity
            if (!empty($supplier['address']) && !empty($contact['address'])) {
                $similarity = 0;
                similar_text(strtolower($supplier['address']), strtolower($contact['address']), $similarity);
                if ($similarity > 60) {
                    $score += $similarity * 0.2;
                    $reasons[] = _l('address_similarity') . ' (' . round($similarity) . '%)';
                }
            }

            if ($score >= 50) {
                $matches[] = [
                    'userid' => $contact['userid'],
                    'company' => $contact['company'],
                    'email' => $contact['email'],
                    'score' => round($score),
                    'reason' => implode(', ', $reasons)
                ];
            }
        }

        // Sort by score descending
        usort($matches, function($a, $b) {
            return $b['score'] - $a['score'];
        });

        // Limit to top 5 matches
        $matches = array_slice($matches, 0, 5);

        echo json_encode(['success' => true, 'matches' => $matches]);
    }

    /**
     * Search products for PO
     */
    public function search_products_for_po()
    {
        if (!has_permission('pos_purchase_orders', '', 'view')) {
            ajax_access_denied();
        }

        $search = $this->input->post('search');
        $limit = $this->input->post('limit') ?: 20;

        $this->db->select('id, name, sku, cost_price, sale_price');
        $this->db->from(db_prefix() . 'pos_products');
        $this->db->where('status', 1);

        if ($search) {
            $this->db->group_start();
            $this->db->like('name', $search);
            $this->db->or_like('sku', $search);
            $this->db->group_end();
        }

        $this->db->limit($limit);
        $products = $this->db->get()->result_array();

        echo json_encode(['success' => true, 'products' => $products]);
    }

    /**
     * Get overdue purchase orders
     */
    public function get_overdue_pos()
    {
        if (!has_permission('pos_purchase_orders', '', 'view')) {
            ajax_access_denied();
        }

        $overdue_pos = $this->pos_purchase_orders_model->get_overdue();

        echo json_encode(['success' => true, 'purchase_orders' => $overdue_pos]);
    }

    /**
     * Get currencies for supplier form
     * Tries to use Perfex CRM core currencies, falls back to default currencies
     */
    private function get_currencies_for_supplier()
    {
        // Try to load Perfex CRM currencies model
        if (file_exists(APPPATH . 'models/Currencies_model.php')) {
            $this->load->model('currencies_model');
            if (method_exists($this->currencies_model, 'get')) {
                $currencies = $this->currencies_model->get();
                if (!empty($currencies)) {
                    return $currencies;
                }
            }
        }

        // Fallback to default currencies if Perfex CRM currencies not available
        return $this->get_default_currencies();
    }

    /**
     * Get default currencies as fallback
     */
    private function get_default_currencies()
    {
        return [
            [
                'id' => 1,
                'name' => 'US Dollar',
                'symbol' => '$',
                'code' => 'USD',
                'isdefault' => 1
            ],
            [
                'id' => 2,
                'name' => 'Euro',
                'symbol' => '€',
                'code' => 'EUR',
                'isdefault' => 0
            ],
            [
                'id' => 3,
                'name' => 'British Pound',
                'symbol' => '£',
                'code' => 'GBP',
                'isdefault' => 0
            ],
            [
                'id' => 4,
                'name' => 'Canadian Dollar',
                'symbol' => 'C$',
                'code' => 'CAD',
                'isdefault' => 0
            ],
            [
                'id' => 5,
                'name' => 'Australian Dollar',
                'symbol' => 'A$',
                'code' => 'AUD',
                'isdefault' => 0
            ]
        ];
    }
}
