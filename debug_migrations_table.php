<?php
/**
 * Debug script to check migrations table structure
 * This helps understand what columns are available in different Perfex CRM versions
 */

defined('BASEPATH') or exit('No direct script access allowed');

// Load CodeIgniter
if (!defined('BASEPATH')) {
    require_once(dirname(__FILE__) . '/../../application/config/database.php');
    require_once(dirname(__FILE__) . '/../../system/core/CodeIgniter.php');
}

$CI = &get_instance();

echo "<h2>POS Inventory - Migrations Table Debug</h2>\n";

try {
    // Check if migrations table exists
    if (!$CI->db->table_exists(db_prefix() . 'migrations')) {
        echo "❌ Migrations table does not exist<br>\n";
        echo "This Perfex CRM installation might not use migrations<br>\n";
        exit;
    }

    echo "✅ Migrations table exists<br>\n";

    // Get table structure
    echo "<h3>Table Structure:</h3>\n";
    $fields = $CI->db->field_data(db_prefix() . 'migrations');
    
    echo "<table border='1' style='border-collapse: collapse;'>\n";
    echo "<tr><th>Column Name</th><th>Type</th><th>Max Length</th><th>Default</th></tr>\n";
    
    $available_columns = [];
    foreach ($fields as $field) {
        $available_columns[] = $field->name;
        echo "<tr>";
        echo "<td>{$field->name}</td>";
        echo "<td>{$field->type}</td>";
        echo "<td>" . (isset($field->max_length) ? $field->max_length : 'N/A') . "</td>";
        echo "<td>" . (isset($field->default) ? $field->default : 'NULL') . "</td>";
        echo "</tr>\n";
    }
    echo "</table>\n";

    echo "<h3>Available Columns:</h3>\n";
    echo implode(', ', $available_columns) . "<br>\n";

    // Check existing migration records
    echo "<h3>Existing Migration Records:</h3>\n";
    $migrations = $CI->db->get(db_prefix() . 'migrations')->result_array();
    
    if (empty($migrations)) {
        echo "No migration records found<br>\n";
    } else {
        echo "<table border='1' style='border-collapse: collapse;'>\n";
        echo "<tr>";
        foreach ($available_columns as $col) {
            echo "<th>$col</th>";
        }
        echo "</tr>\n";
        
        foreach ($migrations as $migration) {
            echo "<tr>";
            foreach ($available_columns as $col) {
                $value = isset($migration[$col]) ? $migration[$col] : 'NULL';
                echo "<td>$value</td>";
            }
            echo "</tr>\n";
        }
        echo "</table>\n";
    }

    // Check for POS Inventory migration records
    echo "<h3>POS Inventory Migration Records:</h3>\n";
    
    // Try different ways to find our migration records
    $pos_migrations = [];
    
    // Method 1: By group (if column exists)
    if (in_array('group', $available_columns)) {
        $CI->db->where('`group`', 'pos_inventory');
        $pos_migrations = $CI->db->get(db_prefix() . 'migrations')->result_array();
        echo "Found by group: " . count($pos_migrations) . " records<br>\n";
    }
    
    // Method 2: By version numbers
    if (empty($pos_migrations)) {
        $CI->db->where_in('version', [100, 101]);
        $pos_migrations = $CI->db->get(db_prefix() . 'migrations')->result_array();
        echo "Found by version: " . count($pos_migrations) . " records<br>\n";
    }
    
    if (!empty($pos_migrations)) {
        echo "<table border='1' style='border-collapse: collapse;'>\n";
        echo "<tr>";
        foreach ($available_columns as $col) {
            echo "<th>$col</th>";
        }
        echo "</tr>\n";
        
        foreach ($pos_migrations as $migration) {
            echo "<tr>";
            foreach ($available_columns as $col) {
                $value = isset($migration[$col]) ? $migration[$col] : 'NULL';
                echo "<td>$value</td>";
            }
            echo "</tr>\n";
        }
        echo "</table>\n";
    } else {
        echo "No POS Inventory migration records found<br>\n";
    }

    // Suggest optimal migration data structure
    echo "<h3>Recommended Migration Data Structure:</h3>\n";
    echo "<pre>\n";
    echo "Based on your table structure, use this data format:\n\n";
    echo '$migration_data = [' . "\n";
    
    if (in_array('version', $available_columns)) {
        echo "    'version' => 100,\n";
    }
    
    if (in_array('time', $available_columns)) {
        echo "    'time' => time(),\n";
    } elseif (in_array('timestamp', $available_columns)) {
        echo "    'timestamp' => time(),\n";
    }
    
    if (in_array('batch', $available_columns)) {
        echo "    'batch' => 1,\n";
    }
    
    if (in_array('class', $available_columns)) {
        echo "    'class' => 'Migration_Version_100',\n";
    }
    
    if (in_array('namespace', $available_columns)) {
        echo "    'namespace' => '',\n";
    }
    
    if (in_array('group', $available_columns)) {
        echo "    '`group`' => 'pos_inventory',\n";
    }
    
    echo "];\n";
    echo "</pre>\n";

} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "<br>\n";
}

echo "<br><a href='javascript:history.back()'>← Go Back</a>\n";
?>
