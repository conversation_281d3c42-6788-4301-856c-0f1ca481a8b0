<?php defined('BASEPATH') or exit('No direct script access allowed'); ?>
<?php init_head(); ?>
<div id="wrapper">
    <div class="content">
        <div class="row">
            <div class="col-md-12">
                <div class="panel_s">
                    <div class="panel-body">
                        <div class="row">
                            <div class="col-md-8">
                                <h4 class="no-margin"><?php echo $title; ?></h4>
                            </div>
                            <div class="col-md-4 text-right">
                                <a href="<?php echo admin_url('pos_inventory/purchase_order/' . $purchase_order['id']); ?>" class="btn btn-default">
                                    <i class="fa fa-arrow-left"></i> <?php echo _l('back_to_po'); ?>
                                </a>
                            </div>
                        </div>
                        <hr class="hr-panel-heading" />

                        <!-- Purchase Order Info -->
                        <div class="row">
                            <div class="col-md-12">
                                <div class="panel panel-info">
                                    <div class="panel-heading">
                                        <h4 class="panel-title"><?php echo _l('purchase_order_information'); ?></h4>
                                    </div>
                                    <div class="panel-body">
                                        <div class="row">
                                            <div class="col-md-3">
                                                <strong><?php echo _l('po_number'); ?>:</strong><br>
                                                <?php echo $purchase_order['po_number']; ?>
                                            </div>
                                            <div class="col-md-3">
                                                <strong><?php echo _l('supplier'); ?>:</strong><br>
                                                <?php echo $purchase_order['supplier_name']; ?>
                                            </div>
                                            <div class="col-md-3">
                                                <strong><?php echo _l('order_date'); ?>:</strong><br>
                                                <?php echo _d($purchase_order['order_date']); ?>
                                            </div>
                                            <div class="col-md-3">
                                                <strong><?php echo _l('expected_delivery'); ?>:</strong><br>
                                                <?php echo $purchase_order['expected_delivery'] ? _d($purchase_order['expected_delivery']) : '-'; ?>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <?php echo form_open(admin_url('pos_inventory/receive_stock/' . $purchase_order['id']), ['id' => 'receive-stock-form']); ?>
                        
                        <!-- Delivery Information -->
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="delivery_date" class="control-label"><?php echo _l('delivery_date'); ?> <span class="text-danger">*</span></label>
                                    <input type="date" name="delivery_date" id="delivery_date" class="form-control" 
                                           value="<?php echo date('Y-m-d'); ?>" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="delivery_notes" class="control-label"><?php echo _l('delivery_notes'); ?></label>
                                    <input type="text" name="delivery_notes" id="delivery_notes" class="form-control" 
                                           placeholder="<?php echo _l('delivery_notes_placeholder'); ?>">
                                </div>
                            </div>
                        </div>

                        <!-- Items to Receive -->
                        <div class="row">
                            <div class="col-md-12">
                                <h4><?php echo _l('items_to_receive'); ?></h4>
                                <div class="table-responsive">
                                    <table class="table table-bordered" id="receive-items-table">
                                        <thead>
                                            <tr>
                                                <th width="25%"><?php echo _l('product'); ?></th>
                                                <th width="15%"><?php echo _l('ordered_qty'); ?></th>
                                                <th width="15%"><?php echo _l('received_qty'); ?></th>
                                                <th width="15%"><?php echo _l('receiving_qty'); ?></th>
                                                <th width="15%"><?php echo _l('batch_number'); ?></th>
                                                <th width="15%"><?php echo _l('expected_date'); ?></th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php foreach ($purchase_order['items'] as $item): ?>
                                                <?php 
                                                $remaining_qty = $item['quantity'] - $item['received_quantity'];
                                                if ($remaining_qty > 0): 
                                                ?>
                                                <tr>
                                                    <td>
                                                        <strong><?php echo $item['product_name']; ?></strong>
                                                        <?php if ($item['variant_name']): ?>
                                                            <br><small class="text-muted"><?php echo $item['variant_name']; ?></small>
                                                        <?php endif; ?>
                                                        <br><small class="text-muted">SKU: <?php echo $item['sku']; ?></small>
                                                    </td>
                                                    <td>
                                                        <span class="badge badge-info"><?php echo $item['quantity']; ?></span>
                                                    </td>
                                                    <td>
                                                        <span class="badge badge-success"><?php echo $item['received_quantity']; ?></span>
                                                    </td>
                                                    <td>
                                                        <input type="number" 
                                                               name="items[<?php echo $item['id']; ?>][received_quantity]" 
                                                               class="form-control receiving-qty" 
                                                               value="<?php echo $remaining_qty; ?>" 
                                                               min="0" 
                                                               max="<?php echo $remaining_qty; ?>" 
                                                               step="1"
                                                               data-item-id="<?php echo $item['id']; ?>"
                                                               data-remaining="<?php echo $remaining_qty; ?>">
                                                    </td>
                                                    <td>
                                                        <input type="text" 
                                                               name="items[<?php echo $item['id']; ?>][batch_number]" 
                                                               class="form-control" 
                                                               value="<?php echo $item['batch_number']; ?>"
                                                               placeholder="<?php echo _l('batch_number_placeholder'); ?>">
                                                    </td>
                                                    <td>
                                                        <input type="date" 
                                                               name="items[<?php echo $item['id']; ?>][expected_date]" 
                                                               class="form-control expected-date" 
                                                               placeholder="<?php echo _l('backorder_expected_date'); ?>">
                                                        <small class="text-muted"><?php echo _l('for_backorders_only'); ?></small>
                                                    </td>
                                                </tr>
                                                <?php endif; ?>
                                            <?php endforeach; ?>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>

                        <!-- Quick Actions -->
                        <div class="row">
                            <div class="col-md-12">
                                <div class="panel panel-default">
                                    <div class="panel-heading">
                                        <h4 class="panel-title"><?php echo _l('quick_actions'); ?></h4>
                                    </div>
                                    <div class="panel-body">
                                        <button type="button" class="btn btn-success" onclick="receiveAll()">
                                            <i class="fa fa-check"></i> <?php echo _l('receive_all_items'); ?>
                                        </button>
                                        <button type="button" class="btn btn-warning" onclick="receivePartial()">
                                            <i class="fa fa-exclamation-triangle"></i> <?php echo _l('receive_partial'); ?>
                                        </button>
                                        <button type="button" class="btn btn-info" onclick="clearAll()">
                                            <i class="fa fa-refresh"></i> <?php echo _l('clear_all'); ?>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Summary -->
                        <div class="row">
                            <div class="col-md-6 col-md-offset-6">
                                <div class="panel panel-info">
                                    <div class="panel-heading">
                                        <h4 class="panel-title"><?php echo _l('receiving_summary'); ?></h4>
                                    </div>
                                    <div class="panel-body">
                                        <table class="table table-condensed">
                                            <tr>
                                                <td><strong><?php echo _l('total_items'); ?>:</strong></td>
                                                <td class="text-right" id="total-items">0</td>
                                            </tr>
                                            <tr>
                                                <td><strong><?php echo _l('items_receiving'); ?>:</strong></td>
                                                <td class="text-right" id="items-receiving">0</td>
                                            </tr>
                                            <tr>
                                                <td><strong><?php echo _l('items_backorder'); ?>:</strong></td>
                                                <td class="text-right" id="items-backorder">0</td>
                                            </tr>
                                            <tr class="info">
                                                <td><strong><?php echo _l('completion_percentage'); ?>:</strong></td>
                                                <td class="text-right"><strong><span id="completion-percentage">0</span>%</strong></td>
                                            </tr>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="btn-bottom-toolbar text-right">
                            <button type="submit" class="btn btn-primary">
                                <i class="fa fa-check"></i> <?php echo _l('receive_stock'); ?>
                            </button>
                            <a href="<?php echo admin_url('pos_inventory/purchase_order/' . $purchase_order['id']); ?>" class="btn btn-default">
                                <?php echo _l('cancel'); ?>
                            </a>
                        </div>

                        <?php echo form_close(); ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
$(document).ready(function() {
    // Initialize form
    initializeReceiveForm();
    
    // Calculate initial summary
    calculateSummary();
});

function initializeReceiveForm() {
    // Monitor quantity changes
    $('.receiving-qty').on('input', function() {
        var itemId = $(this).data('item-id');
        var receivingQty = parseInt($(this).val()) || 0;
        var remainingQty = $(this).data('remaining');
        
        // Show/hide expected date field based on whether there will be backorders
        var expectedDateField = $('input[name="items[' + itemId + '][expected_date]"]');
        if (receivingQty < remainingQty) {
            expectedDateField.closest('td').show();
        } else {
            expectedDateField.closest('td').hide();
            expectedDateField.val('');
        }
        
        calculateSummary();
    });
    
    // Form validation
    $('#receive-stock-form').on('submit', function(e) {
        if (!validateReceiveForm()) {
            e.preventDefault();
        }
    });
}

function receiveAll() {
    $('.receiving-qty').each(function() {
        var remainingQty = $(this).data('remaining');
        $(this).val(remainingQty);
        $(this).trigger('input');
    });
}

function receivePartial() {
    var percentage = prompt('<?php echo _l('enter_percentage_to_receive'); ?>', '50');
    if (percentage && !isNaN(percentage)) {
        percentage = Math.max(0, Math.min(100, parseInt(percentage)));
        
        $('.receiving-qty').each(function() {
            var remainingQty = $(this).data('remaining');
            var receiveQty = Math.floor(remainingQty * percentage / 100);
            $(this).val(receiveQty);
            $(this).trigger('input');
        });
    }
}

function clearAll() {
    $('.receiving-qty').val(0);
    $('.expected-date').val('');
    $('.expected-date').closest('td').hide();
    calculateSummary();
}

function calculateSummary() {
    var totalItems = $('.receiving-qty').length;
    var itemsReceiving = 0;
    var itemsBackorder = 0;
    var totalReceiving = 0;
    var totalRemaining = 0;
    
    $('.receiving-qty').each(function() {
        var receivingQty = parseInt($(this).val()) || 0;
        var remainingQty = $(this).data('remaining');
        
        totalReceiving += receivingQty;
        totalRemaining += remainingQty;
        
        if (receivingQty > 0) {
            itemsReceiving++;
        }
        
        if (receivingQty < remainingQty) {
            itemsBackorder++;
        }
    });
    
    var completionPercentage = totalRemaining > 0 ? Math.round((totalReceiving / totalRemaining) * 100) : 0;
    
    $('#total-items').text(totalItems);
    $('#items-receiving').text(itemsReceiving);
    $('#items-backorder').text(itemsBackorder);
    $('#completion-percentage').text(completionPercentage);
}

function validateReceiveForm() {
    var isValid = true;
    var errors = [];
    
    // Check if at least one item is being received
    var hasReceiving = false;
    $('.receiving-qty').each(function() {
        if (parseInt($(this).val()) > 0) {
            hasReceiving = true;
            return false;
        }
    });
    
    if (!hasReceiving) {
        errors.push('<?php echo _l('must_receive_at_least_one_item'); ?>');
        isValid = false;
    }
    
    // Check delivery date
    if (!$('#delivery_date').val()) {
        errors.push('<?php echo _l('delivery_date_required'); ?>');
        isValid = false;
    }
    
    // Validate receiving quantities
    $('.receiving-qty').each(function() {
        var receivingQty = parseInt($(this).val()) || 0;
        var remainingQty = $(this).data('remaining');
        
        if (receivingQty > remainingQty) {
            errors.push('<?php echo _l('cannot_receive_more_than_ordered'); ?>');
            isValid = false;
            return false;
        }
    });
    
    if (!isValid) {
        alert_float('danger', errors.join('<br>'));
    }
    
    return isValid;
}
</script>

<?php init_tail(); ?>
