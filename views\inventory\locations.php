<?php defined('BASEPATH') or exit('No direct script access allowed'); ?>
<?php init_head(); ?>
<div id="wrapper">
    <div class="content">
        <div class="row">
            <div class="col-md-12">
                <div class="panel_s">
                    <div class="panel-body">
                        <div class="row">
                            <div class="col-md-8">
                                <h4 class="no-margin">
                                    <i class="fa fa-map-marker"></i> <?php echo _l('manage_locations'); ?>
                                </h4>
                            </div>
                            <div class="col-md-4 text-right">
                                <?php if (has_permission('pos_inventory', '', 'create')) { ?>
                                    <button type="button" class="btn btn-primary" data-toggle="modal" data-target="#locationModal">
                                        <i class="fa fa-plus"></i> <?php echo _l('add_location'); ?>
                                    </button>
                                <?php } ?>
                            </div>
                        </div>
                        <hr class="hr-panel-heading" />
                        
                        <!-- Locations Table -->
                        <div class="table-responsive">
                            <table class="table table-striped" id="locations-table">
                                <thead>
                                    <tr>
                                        <th><?php echo _l('location_name'); ?></th>
                                        <th><?php echo _l('address'); ?></th>
                                        <th><?php echo _l('contact_info'); ?></th>
                                        <th><?php echo _l('products_count'); ?></th>
                                        <th><?php echo _l('total_stock_value'); ?></th>
                                        <th><?php echo _l('default'); ?></th>
                                        <th><?php echo _l('status'); ?></th>
                                        <th><?php echo _l('actions'); ?></th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($locations as $location) { ?>
                                    <tr>
                                        <td>
                                            <strong><?php echo $location['name']; ?></strong>
                                        </td>
                                        <td>
                                            <?php echo $location['address'] ?: '<span class="text-muted">' . _l('no_address') . '</span>'; ?>
                                        </td>
                                        <td>
                                            <?php if ($location['phone'] || $location['email']) { ?>
                                                <?php if ($location['phone']) { ?>
                                                    <i class="fa fa-phone"></i> <?php echo $location['phone']; ?><br>
                                                <?php } ?>
                                                <?php if ($location['email']) { ?>
                                                    <i class="fa fa-envelope"></i> <?php echo $location['email']; ?>
                                                <?php } ?>
                                            <?php } else { ?>
                                                <span class="text-muted"><?php echo _l('no_contact_info'); ?></span>
                                            <?php } ?>
                                        </td>
                                        <td>
                                            <span class="label label-info"><?php echo $location['products_count'] ?: 0; ?></span>
                                        </td>
                                        <td>
                                            <strong><?php echo app_format_money($location['stock_value'] ?: 0, get_base_currency()); ?></strong>
                                        </td>
                                        <td>
                                            <?php if ($location['is_default']) { ?>
                                                <span class="label label-success"><?php echo _l('yes'); ?></span>
                                            <?php } else { ?>
                                                <span class="text-muted"><?php echo _l('no'); ?></span>
                                            <?php } ?>
                                        </td>
                                        <td>
                                            <?php if ($location['status']) { ?>
                                                <span class="label label-success"><?php echo _l('active'); ?></span>
                                            <?php } else { ?>
                                                <span class="label label-default"><?php echo _l('inactive'); ?></span>
                                            <?php } ?>
                                        </td>
                                        <td>
                                            <?php if (has_permission('pos_inventory', '', 'view')) { ?>
                                                <a href="<?php echo admin_url('pos_inventory/inventory?location=' . $location['id']); ?>" 
                                                   class="btn btn-default btn-icon" data-toggle="tooltip" title="<?php echo _l('view_inventory'); ?>">
                                                    <i class="fa fa-cubes"></i>
                                                </a>
                                            <?php } ?>
                                            
                                            <?php if (has_permission('pos_inventory', '', 'edit')) { ?>
                                                <button type="button" class="btn btn-default btn-icon edit-location" 
                                                        data-id="<?php echo $location['id']; ?>"
                                                        data-name="<?php echo htmlspecialchars($location['name']); ?>"
                                                        data-address="<?php echo htmlspecialchars($location['address']); ?>"
                                                        data-phone="<?php echo htmlspecialchars($location['phone']); ?>"
                                                        data-email="<?php echo htmlspecialchars($location['email']); ?>"
                                                        data-is-default="<?php echo $location['is_default']; ?>"
                                                        data-status="<?php echo $location['status']; ?>"
                                                        data-toggle="tooltip" title="<?php echo _l('edit'); ?>">
                                                    <i class="fa fa-pencil-square-o"></i>
                                                </button>
                                                
                                                <?php if (!$location['is_default']) { ?>
                                                    <a href="<?php echo admin_url('pos_inventory/set_default_location/' . $location['id']); ?>" 
                                                       class="btn btn-success btn-icon" data-toggle="tooltip" title="<?php echo _l('set_as_default'); ?>">
                                                        <i class="fa fa-star"></i>
                                                    </a>
                                                <?php } ?>
                                            <?php } ?>
                                            
                                            <?php if (has_permission('pos_inventory', '', 'delete') && !$location['is_default'] && $location['products_count'] == 0) { ?>
                                                <a href="<?php echo admin_url('pos_inventory/delete_location/' . $location['id']); ?>" 
                                                   class="btn btn-danger btn-icon _delete" data-toggle="tooltip" title="<?php echo _l('delete'); ?>">
                                                    <i class="fa fa-remove"></i>
                                                </a>
                                            <?php } ?>
                                        </td>
                                    </tr>
                                    <?php } ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Location Modal -->
<div class="modal fade" id="locationModal" tabindex="-1" role="dialog">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
                <h4 class="modal-title" id="locationModalTitle"><?php echo _l('add_location'); ?></h4>
            </div>
            <form id="location-form">
                <input type="hidden" id="location_id" name="location_id">
                <div class="modal-body">
                    <div class="form-group">
                        <label for="location_name" class="control-label">
                            <?php echo _l('location_name'); ?> <span class="text-danger">*</span>
                        </label>
                        <input type="text" class="form-control" id="location_name" name="name" required>
                    </div>
                    
                    <div class="form-group">
                        <label for="location_address" class="control-label"><?php echo _l('address'); ?></label>
                        <textarea class="form-control" id="location_address" name="address" rows="3"></textarea>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="location_phone" class="control-label"><?php echo _l('phone'); ?></label>
                                <input type="text" class="form-control" id="location_phone" name="phone">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="location_email" class="control-label"><?php echo _l('email'); ?></label>
                                <input type="email" class="form-control" id="location_email" name="email">
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <div class="checkbox">
                                    <label>
                                        <input type="checkbox" id="location_is_default" name="is_default" value="1">
                                        <?php echo _l('set_as_default_location'); ?>
                                    </label>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <div class="checkbox">
                                    <label>
                                        <input type="checkbox" id="location_status" name="status" value="1" checked>
                                        <?php echo _l('active'); ?>
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal"><?php echo _l('close'); ?></button>
                    <button type="submit" class="btn btn-primary" id="save-location-btn"><?php echo _l('save'); ?></button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
$(document).ready(function() {
    // Initialize DataTable
    $('#locations-table').DataTable({
        responsive: true,
        order: [[0, 'asc']],
        columnDefs: [
            { orderable: false, targets: -1 }
        ]
    });
    
    // Edit location
    $('.edit-location').click(function() {
        var data = $(this).data();
        
        $('#location_id').val(data.id);
        $('#location_name').val(data.name);
        $('#location_address').val(data.address);
        $('#location_phone').val(data.phone);
        $('#location_email').val(data.email);
        $('#location_is_default').prop('checked', data.isDefault == 1);
        $('#location_status').prop('checked', data.status == 1);
        
        $('#locationModalTitle').text('<?php echo _l('edit_location'); ?>');
        $('#locationModal').modal('show');
    });
    
    // Reset form when modal is hidden
    $('#locationModal').on('hidden.bs.modal', function() {
        $('#location-form')[0].reset();
        $('#location_id').val('');
        $('#locationModalTitle').text('<?php echo _l('add_location'); ?>');
    });
    
    // Submit location form
    $('#location-form').submit(function(e) {
        e.preventDefault();
        
        var formData = $(this).serialize();
        var isEdit = $('#location_id').val() !== '';
        
        $.post(admin_url + 'pos_inventory/save_location', formData, function(response) {
            if (response.success) {
                alert_float('success', response.message);
                $('#locationModal').modal('hide');
                location.reload();
            } else {
                alert_float('danger', response.message);
            }
        }, 'json');
    });
});
</script>

<?php init_tail(); ?>
