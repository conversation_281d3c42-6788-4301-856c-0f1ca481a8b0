/**
 * POS Inventory Module - Common JavaScript Functions
 * This file contains shared functionality used across the module
 */

// Ensure jQuery is available
if (typeof $ === 'undefined' && typeof jQuery !== 'undefined') {
    var $ = jQuery;
}

// Common POS Inventory namespace
var POSInventory = POSInventory || {};

/**
 * Common utility functions
 */
POSInventory.Utils = {
    
    /**
     * Format currency for display
     */
    formatCurrency: function(amount, symbol) {
        symbol = symbol || '$';
        if (typeof amount === 'string') {
            amount = parseFloat(amount);
        }
        return symbol + (amount || 0).toFixed(2);
    },
    
    /**
     * Format number with thousands separator
     */
    formatNumber: function(num) {
        if (typeof num === 'string') {
            num = parseFloat(num);
        }
        return (num || 0).toLocaleString();
    },
    
    /**
     * Show loading spinner
     */
    showLoading: function(element) {
        if (element) {
            $(element).html('<i class="fa fa-spinner fa-spin"></i> Loading...');
        }
    },
    
    /**
     * Hide loading spinner
     */
    hideLoading: function() {
        $('.fa-spinner').remove();
    },
    
    /**
     * Validate form fields
     */
    validateRequired: function(form) {
        var isValid = true;
        $(form).find('[required]').each(function() {
            if (!$(this).val()) {
                $(this).addClass('has-error');
                isValid = false;
            } else {
                $(this).removeClass('has-error');
            }
        });
        return isValid;
    },
    
    /**
     * Initialize tooltips
     */
    initTooltips: function() {
        if (typeof $().tooltip === 'function') {
            $('[data-toggle="tooltip"]').tooltip();
        }
    },
    
    /**
     * Initialize select2 dropdowns
     */
    initSelect2: function() {
        if (typeof $.fn.select2 === 'function') {
            $('.select2').select2();
        }
    },
    
    /**
     * Initialize selectpicker dropdowns
     */
    initSelectPicker: function() {
        if (typeof $.fn.selectpicker === 'function') {
            $('.selectpicker').selectpicker('refresh');
        }
    }
};

/**
 * Common form handlers
 */
POSInventory.Forms = {
    
    /**
     * Handle radio button changes
     */
    initRadioButtons: function() {
        $(document).on('change', 'input[type="radio"]', function() {
            var name = $(this).attr('name');
            var value = $(this).val();
            
            // Remove active class from all radio buttons with same name
            $('input[name="' + name + '"]').closest('.radio').removeClass('active');
            
            // Add active class to selected radio button
            $(this).closest('.radio').addClass('active');
            
            // Trigger custom event
            $(this).trigger('radio:changed', [name, value]);
        });
    },
    
    /**
     * Handle checkbox changes
     */
    initCheckboxes: function() {
        $(document).on('change', 'input[type="checkbox"]', function() {
            var name = $(this).attr('name');
            var value = $(this).val();
            var checked = $(this).is(':checked');
            
            // Trigger custom event
            $(this).trigger('checkbox:changed', [name, value, checked]);
        });
    },
    
    /**
     * Initialize form validation
     */
    initValidation: function() {
        $(document).on('submit', 'form', function(e) {
            if (!POSInventory.Utils.validateRequired(this)) {
                e.preventDefault();
                alert('Please fill in all required fields.');
                return false;
            }
        });
    }
};

/**
 * Initialize common functionality when document is ready
 */
$(document).ready(function() {
    // Check if jQuery is available
    if (typeof $ === 'undefined') {
        console.error('POS Inventory: jQuery is not loaded');
        return;
    }
    
    // Initialize common components
    POSInventory.Utils.initTooltips();
    POSInventory.Utils.initSelect2();
    POSInventory.Utils.initSelectPicker();
    POSInventory.Forms.initRadioButtons();
    POSInventory.Forms.initCheckboxes();
    POSInventory.Forms.initValidation();
    
    // Global error handler for AJAX requests
    $(document).ajaxError(function(event, xhr, settings, thrownError) {
        if (xhr.status === 404) {
            console.error('POS Inventory: AJAX 404 error for URL: ' + settings.url);
        } else if (xhr.status === 500) {
            console.error('POS Inventory: Server error for URL: ' + settings.url);
        }
    });
    
    console.log('POS Inventory common.js loaded successfully');
});

// Export for use in other scripts
window.POSInventory = POSInventory;
