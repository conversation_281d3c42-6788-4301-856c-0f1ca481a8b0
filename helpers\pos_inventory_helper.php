<?php

defined('BASEPATH') or exit('No direct script access allowed');

/**
 * Sanitize input for POS operations
 */
function pos_sanitize_input($input, $type = 'string')
{
    $CI = &get_instance();

    switch ($type) {
        case 'int':
            return (int) $input;
        case 'float':
            return (float) $input;
        case 'email':
            return filter_var($input, FILTER_SANITIZE_EMAIL);
        case 'url':
            return filter_var($input, FILTER_SANITIZE_URL);
        default:
            return $CI->security->xss_clean(trim($input));
    }
}

/**
 * Advanced POS & Central Inventory Helper Functions
 * 
 * This file contains helper functions for the POS Inventory module
 * that can be used throughout the application.
 */

if (!function_exists('pos_inventory_option')) {
    /**
     * Get or set POS inventory module option
     * 
     * @param string $name Option name
     * @param mixed $value Option value (if setting)
     * @param bool $auto Whether this is an auto option
     * @return mixed Option value or boolean for set operations
     */
    function pos_inventory_option($name, $value = null, $auto = false)
    {
        $CI = &get_instance();
        
        if ($value !== null) {
            // Setting option
            $existing = $CI->db->get_where(db_prefix() . 'options', ['name' => $name])->row();
            
            if ($existing) {
                $CI->db->where('name', $name);
                return $CI->db->update(db_prefix() . 'options', [
                    'value' => $value,
                    'autoload' => $auto ? 1 : 0
                ]);
            } else {
                return $CI->db->insert(db_prefix() . 'options', [
                    'name' => $name,
                    'value' => $value,
                    'autoload' => $auto ? 1 : 0
                ]);
            }
        } else {
            // Getting option
            $option = $CI->db->get_where(db_prefix() . 'options', ['name' => $name])->row();
            return $option ? $option->value : null;
        }
    }
}

if (!function_exists('pos_inventory_option_exists')) {
    /**
     * Check if POS inventory option exists
     * 
     * @param string $name Option name
     * @return bool True if exists, false otherwise
     */
    function pos_inventory_option_exists($name)
    {
        $CI = &get_instance();
        return $CI->db->where('name', $name)->count_all_results(db_prefix() . 'options') > 0;
    }
}

if (!function_exists('pos_inventory_delete_option')) {
    /**
     * Delete POS inventory option
     * 
     * @param string $name Option name
     * @return bool True if successful
     */
    function pos_inventory_delete_option($name)
    {
        $CI = &get_instance();
        $CI->db->where('name', $name);
        return $CI->db->delete(db_prefix() . 'options');
    }
}

if (!function_exists('pos_inventory_format_currency')) {
    /**
     * Format currency for POS display
     * 
     * @param float $amount Amount to format
     * @param string $currency Currency code
     * @return string Formatted currency
     */
    function pos_inventory_format_currency($amount, $currency = null)
    {
        if ($currency === null) {
            $currency = pos_inventory_option('pos_default_currency', 'USD');
        }
        
        return app_format_money($amount, $currency);
    }
}

if (!function_exists('pos_inventory_generate_sku')) {
    /**
     * Generate unique SKU for product
     * 
     * @param string $prefix SKU prefix
     * @return string Generated SKU
     */
    function pos_inventory_generate_sku($prefix = 'POS')
    {
        $CI = &get_instance();
        
        do {
            $timestamp = date('ymd');
            $random = str_pad(mt_rand(1, 9999), 4, '0', STR_PAD_LEFT);
            $sku = $prefix . '-' . $timestamp . '-' . $random;
            
            // Check if SKU already exists
            $exists = $CI->db->get_where(db_prefix() . 'pos_products', ['sku' => $sku])->row();
        } while ($exists);
        
        return $sku;
    }
}

if (!function_exists('pos_inventory_generate_barcode')) {
    /**
     * Generate unique barcode for product
     * 
     * @param int $length Barcode length (default 12)
     * @return string Generated barcode
     */
    function pos_inventory_generate_barcode($length = 12)
    {
        $CI = &get_instance();
        
        do {
            $barcode = '';
            for ($i = 0; $i < $length; $i++) {
                $barcode .= mt_rand(0, 9);
            }
            
            // Check if barcode already exists
            $exists = $CI->db->get_where(db_prefix() . 'pos_products', ['barcode' => $barcode])->row();
        } while ($exists);
        
        return $barcode;
    }
}

if (!function_exists('pos_inventory_log_activity')) {
    /**
     * Log POS inventory activity
     * 
     * @param string $action Action performed
     * @param string $description Activity description
     * @param array $data Additional data
     * @return bool True if successful
     */
    function pos_inventory_log_activity($action, $description, $data = [])
    {
        if (!pos_inventory_config('enable_audit_log', true)) {
            return true;
        }
        
        $CI = &get_instance();
        
        return $CI->db->insert(db_prefix() . 'pos_activity_log', [
            'staff_id' => get_staff_user_id(),
            'action' => $action,
            'description' => $description,
            'data' => json_encode($data),
            'ip_address' => $CI->input->ip_address(),
            'user_agent' => $CI->input->user_agent(),
            'created_at' => date('Y-m-d H:i:s')
        ]);
    }
}

if (!function_exists('pos_inventory_calculate_tax')) {
    /**
     * Calculate tax amount
     * 
     * @param float $amount Base amount
     * @param float $tax_rate Tax rate (percentage)
     * @param bool $inclusive Whether tax is inclusive
     * @return array Tax calculation details
     */
    function pos_inventory_calculate_tax($amount, $tax_rate, $inclusive = false)
    {
        if ($inclusive) {
            $tax_amount = $amount - ($amount / (1 + ($tax_rate / 100)));
            $net_amount = $amount - $tax_amount;
        } else {
            $tax_amount = $amount * ($tax_rate / 100);
            $net_amount = $amount;
        }
        
        return [
            'net_amount' => round($net_amount, 2),
            'tax_amount' => round($tax_amount, 2),
            'total_amount' => round($net_amount + $tax_amount, 2),
            'tax_rate' => $tax_rate,
            'inclusive' => $inclusive
        ];
    }
}

if (!function_exists('pos_inventory_format_date')) {
    /**
     * Format date for POS display
     * 
     * @param string $date Date string
     * @param string $format Date format
     * @return string Formatted date
     */
    function pos_inventory_format_date($date, $format = null)
    {
        if ($format === null) {
            $format = get_option('dateformat');
        }
        
        return _d($date, $format);
    }
}

if (!function_exists('pos_inventory_format_datetime')) {
    /**
     * Format datetime for POS display
     * 
     * @param string $datetime Datetime string
     * @param string $format Datetime format
     * @return string Formatted datetime
     */
    function pos_inventory_format_datetime($datetime, $format = null)
    {
        if ($format === null) {
            $format = get_option('dateformat') . ' ' . get_option('timeformat');
        }
        
        return _dt($datetime, $format);
    }
}

if (!function_exists('pos_inventory_get_staff_name')) {
    /**
     * Get staff member name
     * 
     * @param int $staff_id Staff ID
     * @return string Staff name
     */
    function pos_inventory_get_staff_name($staff_id)
    {
        $CI = &get_instance();
        $staff = $CI->db->get_where(db_prefix() . 'staff', ['staffid' => $staff_id])->row();
        
        if ($staff) {
            return $staff->firstname . ' ' . $staff->lastname;
        }
        
        return 'Unknown';
    }
}

if (!function_exists('pos_inventory_sanitize_filename')) {
    /**
     * Sanitize filename for uploads
     * 
     * @param string $filename Original filename
     * @return string Sanitized filename
     */
    function pos_inventory_sanitize_filename($filename)
    {
        // Remove special characters and spaces
        $filename = preg_replace('/[^a-zA-Z0-9._-]/', '_', $filename);
        
        // Remove multiple underscores
        $filename = preg_replace('/_+/', '_', $filename);
        
        // Trim underscores from start and end
        $filename = trim($filename, '_');
        
        return $filename;
    }
}

if (!function_exists('pos_inventory_validate_image')) {
    /**
     * Validate uploaded image
     * 
     * @param array $file File data from $_FILES
     * @return array Validation result
     */
    function pos_inventory_validate_image($file)
    {
        $allowed_types = explode('|', pos_inventory_config('allowed_image_types', 'jpg|jpeg|png|gif'));
        $max_size = pos_inventory_config('max_file_size', 5 * 1024 * 1024);
        
        $errors = [];
        
        // Check file size
        if ($file['size'] > $max_size) {
            $errors[] = 'File size exceeds maximum allowed size of ' . ($max_size / 1024 / 1024) . 'MB';
        }
        
        // Check file type
        $file_ext = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));
        if (!in_array($file_ext, $allowed_types)) {
            $errors[] = 'File type not allowed. Allowed types: ' . implode(', ', $allowed_types);
        }
        
        // Check if file is actually an image
        if (!empty($file['tmp_name']) && !getimagesize($file['tmp_name'])) {
            $errors[] = 'File is not a valid image';
        }
        
        return [
            'valid' => empty($errors),
            'errors' => $errors
        ];
    }
}

if (!function_exists('pos_inventory_generate_receipt_number')) {
    /**
     * Generate unique receipt number
     * 
     * @param string $prefix Receipt prefix
     * @return string Generated receipt number
     */
    function pos_inventory_generate_receipt_number($prefix = 'RCP')
    {
        $CI = &get_instance();
        
        // Get next sequence number for today
        $CI->db->select('COUNT(*) + 1 as next_number');
        $CI->db->from(db_prefix() . 'pos_transactions');
        $CI->db->where('DATE(created_at)', date('Y-m-d'));
        $result = $CI->db->get()->row();
        
        $date = date('ymd');
        $sequence = str_pad($result->next_number, 4, '0', STR_PAD_LEFT);
        
        return $prefix . '-' . $date . '-' . $sequence;
    }
}

if (!function_exists('get_currencies')) {
    /**
     * Get currencies for POS Inventory module
     * This function provides compatibility with Perfex CRM core currency system
     *
     * @return array Array of currencies
     */
    function get_currencies()
    {
        $CI = &get_instance();

        // Try to load Perfex CRM currencies model
        if (file_exists(APPPATH . 'models/Currencies_model.php')) {
            $CI->load->model('currencies_model');
            if (method_exists($CI->currencies_model, 'get')) {
                $currencies = $CI->currencies_model->get();
                if (!empty($currencies)) {
                    return $currencies;
                }
            }
        }

        // Fallback to default currencies if Perfex CRM currencies not available
        return [
            [
                'id' => 1,
                'name' => 'US Dollar',
                'symbol' => '$',
                'code' => 'USD',
                'isdefault' => 1
            ],
            [
                'id' => 2,
                'name' => 'Euro',
                'symbol' => '€',
                'code' => 'EUR',
                'isdefault' => 0
            ],
            [
                'id' => 3,
                'name' => 'British Pound',
                'symbol' => '£',
                'code' => 'GBP',
                'isdefault' => 0
            ],
            [
                'id' => 4,
                'name' => 'Canadian Dollar',
                'symbol' => 'C$',
                'code' => 'CAD',
                'isdefault' => 0
            ],
            [
                'id' => 5,
                'name' => 'Australian Dollar',
                'symbol' => 'A$',
                'code' => 'AUD',
                'isdefault' => 0
            ]
        ];
    }
}
