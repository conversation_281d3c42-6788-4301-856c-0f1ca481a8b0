<?php defined('BASEPATH') or exit('No direct script access allowed'); ?>
<?php init_head(); ?>
<div id="wrapper">
    <div class="content">
        <!-- Page Header -->
        <div class="row">
            <div class="col-md-12">
                <div class="panel_s">
                    <div class="panel-body">
                        <div class="row">
                            <div class="col-md-8">
                                <h4 class="no-margin">
                                    <i class="fa fa-line-chart"></i> <?php echo _l('sales_report'); ?>
                                </h4>
                                <p class="text-muted"><?php echo _l('detailed_sales_analysis'); ?></p>
                            </div>
                            <div class="col-md-4 text-right">
                                <a href="<?php echo admin_url('pos_inventory/reports'); ?>" class="btn btn-default">
                                    <i class="fa fa-arrow-left"></i> <?php echo _l('back_to_dashboard'); ?>
                                </a>
                                <div class="btn-group">
                                    <button type="button" class="btn btn-primary dropdown-toggle" data-toggle="dropdown">
                                        <i class="fa fa-download"></i> <?php echo _l('export'); ?> <span class="caret"></span>
                                    </button>
                                    <ul class="dropdown-menu dropdown-menu-right">
                                        <li><a href="#" id="export-sales-pdf"><i class="fa fa-file-pdf-o"></i> <?php echo _l('export_pdf'); ?></a></li>
                                        <li><a href="#" id="export-sales-excel"><i class="fa fa-file-excel-o"></i> <?php echo _l('export_excel'); ?></a></li>
                                        <li><a href="#" id="export-sales-csv"><i class="fa fa-file-text-o"></i> <?php echo _l('export_csv'); ?></a></li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Advanced Filters -->
        <div class="row">
            <div class="col-md-12">
                <div class="panel_s">
                    <div class="panel-body">
                        <form id="sales-filter-form" class="form-inline">
                            <div class="row">
                                <div class="col-md-3">
                                    <div class="form-group">
                                        <label for="sales_date_from"><?php echo _l('date_from'); ?>:</label>
                                        <input type="date" class="form-control" id="sales_date_from" name="date_from" 
                                               value="<?php echo date('Y-m-01'); ?>">
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="form-group">
                                        <label for="sales_date_to"><?php echo _l('date_to'); ?>:</label>
                                        <input type="date" class="form-control" id="sales_date_to" name="date_to" 
                                               value="<?php echo date('Y-m-d'); ?>">
                                    </div>
                                </div>
                                <div class="col-md-2">
                                    <div class="form-group">
                                        <label for="sales_location"><?php echo _l('location'); ?>:</label>
                                        <select class="form-control selectpicker" id="sales_location" name="location_id">
                                            <option value=""><?php echo _l('all_locations'); ?></option>
                                            <?php foreach ($locations as $location) { ?>
                                                <option value="<?php echo $location['id']; ?>"><?php echo $location['name']; ?></option>
                                            <?php } ?>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-2">
                                    <div class="form-group">
                                        <label for="sales_staff"><?php echo _l('staff'); ?>:</label>
                                        <select class="form-control selectpicker" id="sales_staff" name="staff_id">
                                            <option value=""><?php echo _l('all_staff'); ?></option>
                                            <?php foreach ($staff as $member) { ?>
                                                <option value="<?php echo $member['staffid']; ?>"><?php echo $member['firstname'] . ' ' . $member['lastname']; ?></option>
                                            <?php } ?>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-2">
                                    <div class="form-group">
                                        <label for="sales_payment_method"><?php echo _l('payment_method'); ?>:</label>
                                        <select class="form-control selectpicker" id="sales_payment_method" name="payment_method">
                                            <option value=""><?php echo _l('all_methods'); ?></option>
                                            <option value="cash"><?php echo _l('cash'); ?></option>
                                            <option value="card"><?php echo _l('card'); ?></option>
                                            <option value="bank_transfer"><?php echo _l('bank_transfer'); ?></option>
                                            <option value="mobile_payment"><?php echo _l('mobile_payment'); ?></option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                            <div class="row" style="margin-top: 15px;">
                                <div class="col-md-12">
                                    <button type="submit" class="btn btn-info">
                                        <i class="fa fa-filter"></i> <?php echo _l('apply_filter'); ?>
                                    </button>
                                    <button type="button" class="btn btn-default" id="reset-sales-filters">
                                        <i class="fa fa-times"></i> <?php echo _l('reset'); ?>
                                    </button>
                                    <div class="pull-right">
                                        <div class="btn-group" data-toggle="buttons">
                                            <label class="btn btn-default active">
                                                <input type="radio" name="view_type" value="summary" checked> <?php echo _l('summary'); ?>
                                            </label>
                                            <label class="btn btn-default">
                                                <input type="radio" name="view_type" value="detailed"> <?php echo _l('detailed'); ?>
                                            </label>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>

        <!-- Summary Cards -->
        <div class="row" id="sales-summary-cards">
            <div class="col-md-2">
                <div class="panel_s">
                    <div class="panel-body text-center">
                        <h4 class="no-margin text-success" id="summary-total-sales">$0.00</h4>
                        <p class="text-muted"><?php echo _l('total_sales'); ?></p>
                    </div>
                </div>
            </div>
            <div class="col-md-2">
                <div class="panel_s">
                    <div class="panel-body text-center">
                        <h4 class="no-margin text-primary" id="summary-transactions">0</h4>
                        <p class="text-muted"><?php echo _l('transactions'); ?></p>
                    </div>
                </div>
            </div>
            <div class="col-md-2">
                <div class="panel_s">
                    <div class="panel-body text-center">
                        <h4 class="no-margin text-warning" id="summary-avg-sale">$0.00</h4>
                        <p class="text-muted"><?php echo _l('avg_sale'); ?></p>
                    </div>
                </div>
            </div>
            <div class="col-md-2">
                <div class="panel_s">
                    <div class="panel-body text-center">
                        <h4 class="no-margin text-info" id="summary-items">0</h4>
                        <p class="text-muted"><?php echo _l('items_sold'); ?></p>
                    </div>
                </div>
            </div>
            <div class="col-md-2">
                <div class="panel_s">
                    <div class="panel-body text-center">
                        <h4 class="no-margin text-success" id="summary-tax">$0.00</h4>
                        <p class="text-muted"><?php echo _l('total_tax'); ?></p>
                    </div>
                </div>
            </div>
            <div class="col-md-2">
                <div class="panel_s">
                    <div class="panel-body text-center">
                        <h4 class="no-margin text-danger" id="summary-discounts">$0.00</h4>
                        <p class="text-muted"><?php echo _l('total_discounts'); ?></p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Sales Table -->
        <div class="row">
            <div class="col-md-12">
                <div class="panel_s">
                    <div class="panel-body">
                        <div class="table-responsive">
                            <table class="table table-striped" id="sales-table">
                                <thead>
                                    <tr>
                                        <th><?php echo _l('transaction_number'); ?></th>
                                        <th><?php echo _l('date_time'); ?></th>
                                        <th><?php echo _l('customer'); ?></th>
                                        <th><?php echo _l('staff'); ?></th>
                                        <th><?php echo _l('location'); ?></th>
                                        <th><?php echo _l('items'); ?></th>
                                        <th><?php echo _l('subtotal'); ?></th>
                                        <th><?php echo _l('discount'); ?></th>
                                        <th><?php echo _l('tax'); ?></th>
                                        <th><?php echo _l('total'); ?></th>
                                        <th><?php echo _l('payment_method'); ?></th>
                                        <th><?php echo _l('actions'); ?></th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <!-- Data will be loaded via AJAX -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Charts Section -->
        <div class="row">
            <div class="col-md-6">
                <div class="panel_s">
                    <div class="panel-body">
                        <h5 class="panel-title">
                            <i class="fa fa-calendar"></i> <?php echo _l('daily_sales_trend'); ?>
                        </h5>
                        <div style="height: 300px;">
                            <canvas id="daily-sales-chart"></canvas>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="panel_s">
                    <div class="panel-body">
                        <h5 class="panel-title">
                            <i class="fa fa-user"></i> <?php echo _l('sales_by_staff'); ?>
                        </h5>
                        <div style="height: 300px;">
                            <canvas id="staff-sales-chart"></canvas>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Product Performance -->
        <div class="row">
            <div class="col-md-12">
                <div class="panel_s">
                    <div class="panel-body">
                        <h5 class="panel-title">
                            <i class="fa fa-star"></i> <?php echo _l('product_performance'); ?>
                        </h5>
                        <div class="table-responsive">
                            <table class="table table-striped" id="product-performance-table">
                                <thead>
                                    <tr>
                                        <th><?php echo _l('product'); ?></th>
                                        <th><?php echo _l('sku'); ?></th>
                                        <th><?php echo _l('quantity_sold'); ?></th>
                                        <th><?php echo _l('revenue'); ?></th>
                                        <th><?php echo _l('avg_price'); ?></th>
                                        <th><?php echo _l('profit_margin'); ?></th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <!-- Data will be loaded via AJAX -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
var admin_url = '<?php echo admin_url(); ?>';
var currency_symbol = '<?php echo get_base_currency()->symbol; ?>';

$(document).ready(function() {
    SalesReport.init();
});

var SalesReport = {
    salesTable: null,
    productTable: null,
    charts: {},
    
    init: function() {
        this.bindEvents();
        this.initDataTables();
        this.loadSalesData();
    },
    
    bindEvents: function() {
        var self = this;
        
        $('#sales-filter-form').submit(function(e) {
            e.preventDefault();
            self.loadSalesData();
        });
        
        $('#reset-sales-filters').click(function() {
            self.resetFilters();
        });
        
        $('input[name="view_type"]').change(function() {
            self.toggleView($(this).val());
        });
    },
    
    initDataTables: function() {
        this.salesTable = $('#sales-table').DataTable({
            processing: true,
            serverSide: true,
            ajax: {
                url: admin_url + 'pos_inventory/get_sales_data',
                type: 'POST',
                data: function(d) {
                    return $.extend({}, d, $('#sales-filter-form').serializeObject());
                }
            },
            columns: [
                { data: 'transaction_number' },
                { data: 'transaction_date' },
                { data: 'customer_name' },
                { data: 'staff_name' },
                { data: 'location_name' },
                { data: 'items_count' },
                { data: 'subtotal' },
                { data: 'discount_amount' },
                { data: 'tax_amount' },
                { data: 'total_amount' },
                { data: 'payment_method' },
                { data: 'actions', orderable: false, searchable: false }
            ],
            order: [[1, 'desc']],
            responsive: true
        });
        
        this.productTable = $('#product-performance-table').DataTable({
            processing: true,
            serverSide: true,
            ajax: {
                url: admin_url + 'pos_inventory/get_product_performance_data',
                type: 'POST',
                data: function(d) {
                    return $.extend({}, d, $('#sales-filter-form').serializeObject());
                }
            },
            columns: [
                { data: 'product_name' },
                { data: 'sku' },
                { data: 'quantity_sold' },
                { data: 'revenue' },
                { data: 'avg_price' },
                { data: 'profit_margin' }
            ],
            order: [[2, 'desc']],
            responsive: true
        });
    },
    
    loadSalesData: function() {
        this.salesTable.ajax.reload();
        this.productTable.ajax.reload();
        this.loadSummaryData();
        this.loadCharts();
    },
    
    loadSummaryData: function() {
        var formData = $('#sales-filter-form').serializeObject();
        
        $.get(admin_url + 'pos_inventory/get_sales_summary', formData, function(response) {
            if (response.success) {
                $('#summary-total-sales').text(currency_symbol + response.data.total_sales);
                $('#summary-transactions').text(response.data.total_transactions);
                $('#summary-avg-sale').text(currency_symbol + response.data.avg_sale);
                $('#summary-items').text(response.data.total_items);
                $('#summary-tax').text(currency_symbol + response.data.total_tax);
                $('#summary-discounts').text(currency_symbol + response.data.total_discounts);
            }
        }, 'json');
    },
    
    loadCharts: function() {
        // Implementation for loading charts
    },
    
    resetFilters: function() {
        $('#sales-filter-form')[0].reset();
        $('.selectpicker').selectpicker('refresh');
        this.loadSalesData();
    },
    
    toggleView: function(viewType) {
        if (viewType === 'summary') {
            $('#sales-summary-cards').show();
        } else {
            $('#sales-summary-cards').hide();
        }
    }
};

// Helper function to serialize form to object
$.fn.serializeObject = function() {
    var o = {};
    var a = this.serializeArray();
    $.each(a, function() {
        if (o[this.name]) {
            if (!o[this.name].push) {
                o[this.name] = [o[this.name]];
            }
            o[this.name].push(this.value || '');
        } else {
            o[this.name] = this.value || '';
        }
    });
    return o;
};
</script>

<?php init_tail(); ?>
