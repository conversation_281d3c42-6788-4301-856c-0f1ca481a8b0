<?php defined('BASEPATH') or exit('No direct script access allowed'); ?>
<?php init_head(); ?>
<div id="wrapper">
    <div class="content">
        <div class="row">
            <div class="col-md-12">
                <div class="panel_s">
                    <div class="panel-body">
                        <div class="row">
                            <div class="col-md-12">
                                <h4 class="no-margin">
                                    <?php echo _l('pos_inventory_dashboard'); ?>
                                </h4>
                                <hr class="hr-panel-heading" />
                            </div>
                        </div>
                        
                        <!-- Dashboard Statistics -->
                        <div class="dashboard-stats">
                            <div class="stat-card success">
                                <div class="stat-value"><?php echo $total_products; ?></div>
                                <div class="stat-label"><?php echo _l('total_products'); ?></div>
                            </div>
                            
                            <div class="stat-card info">
                                <div class="stat-value"><?php echo app_format_money($total_stock_value, get_base_currency()); ?></div>
                                <div class="stat-label"><?php echo _l('total_stock_value'); ?></div>
                            </div>
                            
                            <div class="stat-card warning">
                                <div class="stat-value"><?php echo $low_stock_products; ?></div>
                                <div class="stat-label"><?php echo _l('low_stock_products'); ?></div>
                            </div>
                            
                            <div class="stat-card success">
                                <div class="stat-value"><?php echo app_format_money($today_sales, get_base_currency()); ?></div>
                                <div class="stat-label"><?php echo _l('today_sales'); ?></div>
                            </div>
                            
                            <div class="stat-card warning">
                                <div class="stat-value"><?php echo $pending_purchase_orders; ?></div>
                                <div class="stat-label"><?php echo _l('pending') . ' ' . _l('purchase_orders'); ?></div>
                            </div>
                        </div>
                        
                        <!-- Quick Actions -->
                        <div class="row">
                            <div class="col-md-12">
                                <div class="panel_s">
                                    <div class="panel-body">
                                        <h5><?php echo _l('quick_actions'); ?></h5>
                                        <div class="row">
                                            <div class="col-md-3">
                                                <a href="<?php echo admin_url('pos_inventory/pos'); ?>" class="btn btn-primary btn-block">
                                                    <i class="fa fa-shopping-cart"></i> <?php echo _l('open_pos'); ?>
                                                </a>
                                            </div>
                                            <div class="col-md-3">
                                                <a href="<?php echo admin_url('pos_inventory/product'); ?>" class="btn btn-success btn-block">
                                                    <i class="fa fa-plus"></i> <?php echo _l('add_product'); ?>
                                                </a>
                                            </div>
                                            <div class="col-md-3">
                                                <a href="<?php echo admin_url('pos_inventory/purchase_order'); ?>" class="btn btn-info btn-block">
                                                    <i class="fa fa-file-text"></i> <?php echo _l('create_purchase_order'); ?>
                                                </a>
                                            </div>
                                            <div class="col-md-3">
                                                <a href="<?php echo admin_url('pos_inventory/reports'); ?>" class="btn btn-warning btn-block">
                                                    <i class="fa fa-bar-chart"></i> <?php echo _l('view_reports'); ?>
                                                </a>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <!-- Recent Transactions -->
                            <div class="col-md-6">
                                <div class="panel_s">
                                    <div class="panel-body">
                                        <h5><?php echo _l('recent_transactions'); ?></h5>
                                        <?php if (!empty($recent_transactions)) { ?>
                                            <div class="table-responsive">
                                                <table class="table table-striped">
                                                    <thead>
                                                        <tr>
                                                            <th><?php echo _l('transaction_id'); ?></th>
                                                            <th><?php echo _l('customer'); ?></th>
                                                            <th><?php echo _l('total'); ?></th>
                                                            <th><?php echo _l('date'); ?></th>
                                                        </tr>
                                                    </thead>
                                                    <tbody>
                                                        <?php foreach ($recent_transactions as $transaction) { ?>
                                                            <tr>
                                                                <td>#<?php echo $transaction['id']; ?></td>
                                                                <td><?php echo $transaction['customer_name'] ?: _l('walk_in_customer'); ?></td>
                                                                <td><?php echo app_format_money($transaction['total'], get_base_currency()); ?></td>
                                                                <td><?php echo _dt($transaction['created_at']); ?></td>
                                                            </tr>
                                                        <?php } ?>
                                                    </tbody>
                                                </table>
                                            </div>
                                        <?php } else { ?>
                                            <p class="text-muted"><?php echo _l('no_recent_transactions'); ?></p>
                                        <?php } ?>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- Top Selling Products -->
                            <div class="col-md-6">
                                <div class="panel_s">
                                    <div class="panel-body">
                                        <h5><?php echo _l('top_selling_products'); ?></h5>
                                        <?php if (!empty($top_selling_products)) { ?>
                                            <div class="table-responsive">
                                                <table class="table table-striped">
                                                    <thead>
                                                        <tr>
                                                            <th><?php echo _l('product'); ?></th>
                                                            <th><?php echo _l('quantity_sold'); ?></th>
                                                            <th><?php echo _l('revenue'); ?></th>
                                                        </tr>
                                                    </thead>
                                                    <tbody>
                                                        <?php foreach ($top_selling_products as $product) { ?>
                                                            <tr>
                                                                <td><?php echo $product['name']; ?></td>
                                                                <td><?php echo $product['quantity_sold']; ?></td>
                                                                <td><?php echo app_format_money($product['revenue'], get_base_currency()); ?></td>
                                                            </tr>
                                                        <?php } ?>
                                                    </tbody>
                                                </table>
                                            </div>
                                        <?php } else { ?>
                                            <p class="text-muted"><?php echo _l('no_sales_data'); ?></p>
                                        <?php } ?>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Low Stock Alerts -->
                        <?php if (!empty($low_stock_alerts)) { ?>
                            <div class="row">
                                <div class="col-md-12">
                                    <div class="panel_s">
                                        <div class="panel-body">
                                            <h5 class="text-warning">
                                                <i class="fa fa-exclamation-triangle"></i> <?php echo _l('low_stock_alert'); ?>
                                            </h5>
                                            <div class="table-responsive">
                                                <table class="table table-striped">
                                                    <thead>
                                                        <tr>
                                                            <th><?php echo _l('product'); ?></th>
                                                            <th><?php echo _l('sku'); ?></th>
                                                            <th><?php echo _l('current_stock'); ?></th>
                                                            <th><?php echo _l('low_stock_threshold'); ?></th>
                                                            <th><?php echo _l('action'); ?></th>
                                                        </tr>
                                                    </thead>
                                                    <tbody>
                                                        <?php foreach ($low_stock_alerts as $product) { ?>
                                                            <tr>
                                                                <td><?php echo $product['name']; ?></td>
                                                                <td><?php echo $product['sku']; ?></td>
                                                                <td class="text-danger"><?php echo $product['stock_quantity']; ?></td>
                                                                <td><?php echo $product['low_stock_threshold']; ?></td>
                                                                <td>
                                                                    <a href="<?php echo admin_url('pos_inventory/stock_adjustment?product=' . $product['id']); ?>" 
                                                                       class="btn btn-sm btn-primary">
                                                                        <?php echo _l('adjust_stock'); ?>
                                                                    </a>
                                                                    <a href="<?php echo admin_url('pos_inventory/purchase_order?product=' . $product['id']); ?>" 
                                                                       class="btn btn-sm btn-success">
                                                                        <?php echo _l('create_purchase_order'); ?>
                                                                    </a>
                                                                </td>
                                                            </tr>
                                                        <?php } ?>
                                                    </tbody>
                                                </table>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        <?php } ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<?php init_tail(); ?>
