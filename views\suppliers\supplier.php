<?php defined('BASEPATH') or exit('No direct script access allowed'); ?>
<?php init_head(); ?>
<div id="wrapper">
    <div class="content">
        <div class="row">
            <div class="col-md-12">
                <div class="panel_s">
                    <div class="panel-body">
                        <div class="row">
                            <div class="col-md-8">
                                <h4 class="no-margin">
                                    <i class="fa fa-truck"></i> <?php echo $title; ?>
                                </h4>
                            </div>
                            <div class="col-md-4 text-right">
                                <a href="<?php echo admin_url('pos_inventory/suppliers'); ?>" class="btn btn-default">
                                    <i class="fa fa-arrow-left"></i> <?php echo _l('back_to_suppliers'); ?>
                                </a>
                                <?php if (isset($supplier) && has_permission('pos_suppliers', '', 'delete')): ?>
                                    <button type="button" class="btn btn-danger" onclick="deleteSupplier(<?php echo $supplier['id']; ?>)">
                                        <i class="fa fa-trash"></i> <?php echo _l('delete'); ?>
                                    </button>
                                <?php endif; ?>
                            </div>
                        </div>
                        <hr class="hr-panel-heading" />

                        <?php echo form_open(admin_url('pos_inventory/supplier/' . (isset($supplier) ? $supplier['id'] : '')), ['id' => 'supplier-form']); ?>
                        
                        <div class="row">
                            <div class="col-md-8">
                                <!-- Basic Information -->
                                <div class="panel panel-primary">
                                    <div class="panel-heading">
                                        <h4 class="panel-title"><?php echo _l('basic_information'); ?></h4>
                                    </div>
                                    <div class="panel-body">
                                        <div class="row">
                                            <div class="col-md-6">
                                                <div class="form-group">
                                                    <label for="name"><?php echo _l('supplier_name'); ?> *</label>
                                                    <input type="text" class="form-control" id="name" name="name" 
                                                           value="<?php echo isset($supplier) ? $supplier['name'] : ''; ?>" required>
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="form-group">
                                                    <label for="company"><?php echo _l('company'); ?></label>
                                                    <input type="text" class="form-control" id="company" name="company" 
                                                           value="<?php echo isset($supplier) ? $supplier['company'] : ''; ?>">
                                                </div>
                                            </div>
                                        </div>

                                        <div class="row">
                                            <div class="col-md-6">
                                                <div class="form-group">
                                                    <label for="email"><?php echo _l('email'); ?></label>
                                                    <input type="email" class="form-control" id="email" name="email" 
                                                           value="<?php echo isset($supplier) ? $supplier['email'] : ''; ?>">
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="form-group">
                                                    <label for="phone"><?php echo _l('phone'); ?></label>
                                                    <input type="text" class="form-control" id="phone" name="phone" 
                                                           value="<?php echo isset($supplier) ? $supplier['phone'] : ''; ?>">
                                                </div>
                                            </div>
                                        </div>

                                        <div class="row">
                                            <div class="col-md-6">
                                                <div class="form-group">
                                                    <label for="website"><?php echo _l('website'); ?></label>
                                                    <input type="url" class="form-control" id="website" name="website" 
                                                           value="<?php echo isset($supplier) ? $supplier['website'] : ''; ?>" 
                                                           placeholder="https://example.com">
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="form-group">
                                                    <label for="tax_number"><?php echo _l('tax_number'); ?></label>
                                                    <input type="text" class="form-control" id="tax_number" name="tax_number" 
                                                           value="<?php echo isset($supplier) ? $supplier['tax_number'] : ''; ?>">
                                                </div>
                                            </div>
                                        </div>

                                        <div class="form-group">
                                            <label for="description"><?php echo _l('description'); ?></label>
                                            <textarea class="form-control" id="description" name="description" rows="3"><?php echo isset($supplier) ? $supplier['description'] : ''; ?></textarea>
                                        </div>
                                    </div>
                                </div>

                                <!-- Address Information -->
                                <div class="panel panel-info">
                                    <div class="panel-heading">
                                        <h4 class="panel-title"><?php echo _l('address_information'); ?></h4>
                                    </div>
                                    <div class="panel-body">
                                        <div class="form-group">
                                            <label for="address"><?php echo _l('address'); ?></label>
                                            <textarea class="form-control" id="address" name="address" rows="2"><?php echo isset($supplier) ? $supplier['address'] : ''; ?></textarea>
                                        </div>

                                        <div class="row">
                                            <div class="col-md-6">
                                                <div class="form-group">
                                                    <label for="city"><?php echo _l('city'); ?></label>
                                                    <input type="text" class="form-control" id="city" name="city" 
                                                           value="<?php echo isset($supplier) ? $supplier['city'] : ''; ?>">
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="form-group">
                                                    <label for="state"><?php echo _l('state'); ?></label>
                                                    <input type="text" class="form-control" id="state" name="state" 
                                                           value="<?php echo isset($supplier) ? $supplier['state'] : ''; ?>">
                                                </div>
                                            </div>
                                        </div>

                                        <div class="row">
                                            <div class="col-md-6">
                                                <div class="form-group">
                                                    <label for="zip"><?php echo _l('zip_code'); ?></label>
                                                    <input type="text" class="form-control" id="zip" name="zip" 
                                                           value="<?php echo isset($supplier) ? $supplier['zip'] : ''; ?>">
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="form-group">
                                                    <label for="country"><?php echo _l('country'); ?></label>
                                                    <select class="form-control selectpicker" id="country" name="country" data-live-search="true">
                                                        <option value=""><?php echo _l('select_country'); ?></option>
                                                        <?php foreach(get_all_countries() as $country): ?>
                                                            <option value="<?php echo $country['country_id']; ?>" 
                                                                    <?php echo (isset($supplier) && $supplier['country'] == $country['country_id']) ? 'selected' : ''; ?>>
                                                                <?php echo $country['short_name']; ?>
                                                            </option>
                                                        <?php endforeach; ?>
                                                    </select>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Payment & Terms -->
                                <div class="panel panel-warning">
                                    <div class="panel-heading">
                                        <h4 class="panel-title"><?php echo _l('payment_terms'); ?></h4>
                                    </div>
                                    <div class="panel-body">
                                        <div class="row">
                                            <div class="col-md-6">
                                                <div class="form-group">
                                                    <label for="payment_terms"><?php echo _l('payment_terms_days'); ?></label>
                                                    <input type="number" class="form-control" id="payment_terms" name="payment_terms" 
                                                           value="<?php echo isset($supplier) ? $supplier['payment_terms'] : '30'; ?>" min="0">
                                                    <small class="help-block"><?php echo _l('payment_terms_help'); ?></small>
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="form-group">
                                                    <label for="credit_limit"><?php echo _l('credit_limit'); ?></label>
                                                    <input type="number" class="form-control" id="credit_limit" name="credit_limit" 
                                                           value="<?php echo isset($supplier) ? $supplier['credit_limit'] : ''; ?>" 
                                                           step="0.01" min="0">
                                                </div>
                                            </div>
                                        </div>

                                        <div class="row">
                                            <div class="col-md-6">
                                                <div class="form-group">
                                                    <label for="currency"><?php echo _l('currency'); ?></label>
                                                    <select class="form-control selectpicker" id="currency" name="currency">
                                                        <?php if (isset($currencies) && !empty($currencies)): ?>
                                                            <?php foreach($currencies as $currency): ?>
                                                                <option value="<?php echo $currency['id']; ?>"
                                                                        <?php echo ((isset($supplier) && $supplier['currency'] == $currency['id']) ? 'selected' :
                                                                             ((!isset($supplier) && $currency['isdefault'] == 1) ? 'selected' : '')); ?>>
                                                                    <?php echo $currency['name'] . ' (' . $currency['symbol'] . ')'; ?>
                                                                </option>
                                                            <?php endforeach; ?>
                                                        <?php else: ?>
                                                            <option value="1" selected>US Dollar ($)</option>
                                                        <?php endif; ?>
                                                    </select>
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="form-group">
                                                    <label for="discount_percentage"><?php echo _l('default_discount'); ?> (%)</label>
                                                    <input type="number" class="form-control" id="discount_percentage" name="discount_percentage" 
                                                           value="<?php echo isset($supplier) ? $supplier['discount_percentage'] : '0'; ?>" 
                                                           step="0.01" min="0" max="100">
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Perfex CRM Integration -->
                                <div class="panel panel-success">
                                    <div class="panel-heading">
                                        <h4 class="panel-title"><?php echo _l('perfex_crm_integration'); ?></h4>
                                    </div>
                                    <div class="panel-body">
                                        <div class="form-group">
                                            <label for="perfex_contact_id"><?php echo _l('link_to_perfex_contact'); ?></label>
                                            <select class="form-control selectpicker" id="perfex_contact_id" name="perfex_contact_id" data-live-search="true">
                                                <option value=""><?php echo _l('no_link'); ?></option>
                                                <?php foreach($perfex_contacts as $contact): ?>
                                                    <option value="<?php echo $contact['userid']; ?>" 
                                                            <?php echo (isset($supplier) && $supplier['perfex_contact_id'] == $contact['userid']) ? 'selected' : ''; ?>>
                                                        <?php echo $contact['company']; ?>
                                                    </option>
                                                <?php endforeach; ?>
                                            </select>
                                            <small class="help-block"><?php echo _l('perfex_contact_link_help'); ?></small>
                                        </div>

                                        <div class="checkbox">
                                            <label>
                                                <input type="checkbox" name="auto_sync_contact" value="1" 
                                                       <?php echo (isset($supplier) && $supplier['auto_sync_contact']) ? 'checked' : ''; ?>>
                                                <?php echo _l('auto_sync_with_perfex_contact'); ?>
                                            </label>
                                        </div>
                                    </div>
                                </div>

                                <!-- Status -->
                                <div class="form-group">
                                    <div class="checkbox">
                                        <label>
                                            <input type="checkbox" name="status" value="1"
                                                   <?php echo ((!isset($supplier) || $supplier['status']) ? 'checked' : ''); ?>>
                                            <?php echo _l('active'); ?>
                                        </label>
                                    </div>
                                </div>

                                <div class="form-group">
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fa fa-save"></i> <?php echo _l('save'); ?>
                                    </button>
                                    <a href="<?php echo admin_url('pos_inventory/suppliers'); ?>" class="btn btn-default">
                                        <?php echo _l('cancel'); ?>
                                    </a>
                                </div>
                            </div>

                            <div class="col-md-4">
                                <!-- Supplier Statistics -->
                                <?php if (isset($supplier)): ?>
                                <div class="panel panel-default">
                                    <div class="panel-heading">
                                        <h4 class="panel-title">
                                            <i class="fa fa-bar-chart"></i> <?php echo _l('supplier_statistics'); ?>
                                        </h4>
                                    </div>
                                    <div class="panel-body">
                                        <div class="row">
                                            <div class="col-md-12">
                                                <div class="text-center">
                                                    <h4 class="text-primary" id="total-orders">0</h4>
                                                    <p class="text-muted"><?php echo _l('total_orders'); ?></p>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="col-md-6">
                                                <div class="text-center">
                                                    <h5 class="text-success" id="total-amount">$0</h5>
                                                    <p class="text-muted"><?php echo _l('total_amount'); ?></p>
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="text-center">
                                                    <h5 class="text-info" id="avg-order">$0</h5>
                                                    <p class="text-muted"><?php echo _l('avg_order_value'); ?></p>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="col-md-6">
                                                <div class="text-center">
                                                    <h6 class="text-warning" id="last-order">-</h6>
                                                    <p class="text-muted"><?php echo _l('last_order'); ?></p>
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="text-center">
                                                    <h6 class="text-default" id="products-count">0</h6>
                                                    <p class="text-muted"><?php echo _l('products_supplied'); ?></p>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Recent Purchase History -->
                                <div class="panel panel-default">
                                    <div class="panel-heading">
                                        <h4 class="panel-title">
                                            <i class="fa fa-history"></i> <?php echo _l('recent_purchase_history'); ?>
                                        </h4>
                                    </div>
                                    <div class="panel-body">
                                        <?php if (isset($purchase_history) && !empty($purchase_history)): ?>
                                            <div class="table-responsive">
                                                <table class="table table-striped">
                                                    <thead>
                                                        <tr>
                                                            <th><?php echo _l('po_number'); ?></th>
                                                            <th><?php echo _l('date'); ?></th>
                                                            <th><?php echo _l('amount'); ?></th>
                                                            <th><?php echo _l('status'); ?></th>
                                                        </tr>
                                                    </thead>
                                                    <tbody>
                                                        <?php foreach($purchase_history as $order): ?>
                                                            <tr>
                                                                <td>
                                                                    <a href="<?php echo admin_url('pos_inventory/purchase_order/' . $order['id']); ?>">
                                                                        <?php echo $order['po_number']; ?>
                                                                    </a>
                                                                </td>
                                                                <td><?php echo _d($order['order_date']); ?></td>
                                                                <td><?php echo app_format_money($order['total'], get_base_currency()); ?></td>
                                                                <td>
                                                                    <?php 
                                                                    $status_class = '';
                                                                    switch($order['status']) {
                                                                        case 'pending': $status_class = 'warning'; break;
                                                                        case 'approved': $status_class = 'info'; break;
                                                                        case 'received': $status_class = 'success'; break;
                                                                        case 'cancelled': $status_class = 'danger'; break;
                                                                        default: $status_class = 'default';
                                                                    }
                                                                    ?>
                                                                    <span class="label label-<?php echo $status_class; ?>">
                                                                        <?php echo _l($order['status']); ?>
                                                                    </span>
                                                                </td>
                                                            </tr>
                                                        <?php endforeach; ?>
                                                    </tbody>
                                                </table>
                                            </div>
                                            <div class="text-center">
                                                <a href="<?php echo admin_url('pos_inventory/purchase_orders?supplier=' . $supplier['id']); ?>" class="btn btn-sm btn-default">
                                                    <?php echo _l('view_all_orders'); ?>
                                                </a>
                                            </div>
                                        <?php else: ?>
                                            <p class="text-muted text-center"><?php echo _l('no_purchase_history'); ?></p>
                                        <?php endif; ?>
                                    </div>
                                </div>

                                <!-- Quick Actions -->
                                <div class="panel panel-default">
                                    <div class="panel-heading">
                                        <h4 class="panel-title">
                                            <i class="fa fa-bolt"></i> <?php echo _l('quick_actions'); ?>
                                        </h4>
                                    </div>
                                    <div class="panel-body">
                                        <a href="<?php echo admin_url('pos_inventory/purchase_order?supplier=' . $supplier['id']); ?>" class="btn btn-primary btn-block">
                                            <i class="fa fa-plus"></i> <?php echo _l('create_purchase_order'); ?>
                                        </a>
                                        <a href="<?php echo admin_url('pos_inventory/products?supplier=' . $supplier['id']); ?>" class="btn btn-info btn-block">
                                            <i class="fa fa-cube"></i> <?php echo _l('view_products'); ?>
                                        </a>
                                        <?php if ($supplier['perfex_contact_id']): ?>
                                            <a href="<?php echo admin_url('clients/client/' . $supplier['perfex_contact_id']); ?>" class="btn btn-success btn-block" target="_blank">
                                                <i class="fa fa-external-link"></i> <?php echo _l('view_in_perfex'); ?>
                                            </a>
                                        <?php endif; ?>
                                    </div>
                                </div>
                                <?php endif; ?>
                            </div>
                        </div>

                        <?php echo form_close(); ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
$(document).ready(function() {
    <?php if (isset($supplier)): ?>
        loadSupplierStatistics(<?php echo $supplier['id']; ?>);
    <?php endif; ?>
});

function loadSupplierStatistics(supplierId) {
    $.post(admin_url + 'pos_inventory/get_supplier_statistics_detail', {
        supplier_id: supplierId
    }, function(response) {
        if (response.success) {
            var stats = response.statistics;
            $('#total-orders').text(stats.total_orders || 0);
            $('#total-amount').text(stats.total_amount || '$0');
            $('#avg-order').text(stats.avg_order_value || '$0');
            $('#last-order').text(stats.last_order || '-');
            $('#products-count').text(stats.products_count || 0);
        }
    }, 'json');
}

function deleteSupplier(id) {
    if (confirm('<?php echo _l('confirm_delete_supplier'); ?>')) {
        $.post(admin_url + 'pos_inventory/delete_supplier', {
            id: id
        }, function(response) {
            if (response.success) {
                alert_float('success', response.message);
                window.location.href = admin_url + 'pos_inventory/suppliers';
            } else {
                alert_float('danger', response.message);
            }
        }, 'json');
    }
}
</script>

<?php init_tail(); ?>
