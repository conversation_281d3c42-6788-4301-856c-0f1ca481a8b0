<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Receipt - <?php echo $receipt_data['transaction']['transaction_number']; ?></title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f4f4f4;
        }
        
        .email-container {
            background-color: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .header {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 2px solid #007bff;
        }
        
        .company-name {
            font-size: 28px;
            font-weight: bold;
            color: #007bff;
            margin-bottom: 10px;
        }
        
        .company-info {
            color: #666;
            margin-bottom: 5px;
        }
        
        .receipt-title {
            font-size: 24px;
            color: #333;
            margin-top: 20px;
        }
        
        .transaction-details {
            background-color: #f8f9fa;
            padding: 20px;
            border-radius: 5px;
            margin-bottom: 30px;
        }
        
        .detail-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 8px;
        }
        
        .detail-label {
            font-weight: bold;
            color: #555;
        }
        
        .items-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 30px;
        }
        
        .items-table th {
            background-color: #007bff;
            color: white;
            padding: 12px;
            text-align: left;
            font-weight: bold;
        }
        
        .items-table td {
            padding: 12px;
            border-bottom: 1px solid #dee2e6;
        }
        
        .items-table tr:nth-child(even) {
            background-color: #f8f9fa;
        }
        
        .item-name {
            font-weight: bold;
            color: #333;
        }
        
        .item-sku {
            font-size: 12px;
            color: #666;
            margin-top: 3px;
        }
        
        .text-center {
            text-align: center;
        }
        
        .text-right {
            text-align: right;
        }
        
        .totals-section {
            background-color: #f8f9fa;
            padding: 20px;
            border-radius: 5px;
            margin-bottom: 30px;
        }
        
        .total-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 8px;
            padding: 5px 0;
        }
        
        .total-row.grand-total {
            border-top: 2px solid #007bff;
            padding-top: 15px;
            margin-top: 15px;
            font-size: 18px;
            font-weight: bold;
            color: #007bff;
        }
        
        .payment-section {
            background-color: #e9ecef;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 30px;
        }
        
        .footer {
            text-align: center;
            padding-top: 30px;
            border-top: 1px solid #dee2e6;
            color: #666;
        }
        
        .thank-you {
            font-size: 20px;
            font-weight: bold;
            color: #28a745;
            margin-bottom: 15px;
        }
        
        .notes {
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
        }
        
        .notes-title {
            font-weight: bold;
            color: #856404;
            margin-bottom: 10px;
        }
        
        @media only screen and (max-width: 600px) {
            body {
                padding: 10px;
            }
            
            .email-container {
                padding: 20px;
            }
            
            .company-name {
                font-size: 24px;
            }
            
            .receipt-title {
                font-size: 20px;
            }
            
            .items-table th,
            .items-table td {
                padding: 8px 4px;
                font-size: 14px;
            }
            
            .detail-row {
                flex-direction: column;
                margin-bottom: 10px;
            }
            
            .total-row {
                flex-direction: column;
                text-align: center;
            }
        }
    </style>
</head>
<body>
    <div class="email-container">
        <!-- Header -->
        <div class="header">
            <div class="company-name"><?php echo $receipt_data['company']['name']; ?></div>
            <?php if (!empty($receipt_data['company']['address'])) { ?>
                <div class="company-info"><?php echo nl2br($receipt_data['company']['address']); ?></div>
            <?php } ?>
            <div class="company-info">
                <?php if (!empty($receipt_data['company']['phone'])) { ?>
                    Phone: <?php echo $receipt_data['company']['phone']; ?>
                <?php } ?>
                <?php if (!empty($receipt_data['company']['email'])) { ?>
                    | Email: <?php echo $receipt_data['company']['email']; ?>
                <?php } ?>
            </div>
            <div class="receipt-title">RECEIPT</div>
        </div>

        <!-- Transaction Details -->
        <div class="transaction-details">
            <div class="detail-row">
                <span class="detail-label">Transaction Number:</span>
                <span><?php echo $receipt_data['transaction']['transaction_number']; ?></span>
            </div>
            <div class="detail-row">
                <span class="detail-label">Date & Time:</span>
                <span><?php echo date('F d, Y \a\t H:i', strtotime($receipt_data['transaction']['transaction_date'])); ?></span>
            </div>
            <div class="detail-row">
                <span class="detail-label">Cashier:</span>
                <span><?php echo $receipt_data['transaction']['firstname'] . ' ' . $receipt_data['transaction']['lastname']; ?></span>
            </div>
            <?php if (!empty($receipt_data['transaction']['customer_name'])) { ?>
            <div class="detail-row">
                <span class="detail-label">Customer:</span>
                <span><?php echo $receipt_data['transaction']['customer_name']; ?></span>
            </div>
            <?php } ?>
            <?php if (!empty($receipt_data['transaction']['location_name'])) { ?>
            <div class="detail-row">
                <span class="detail-label">Location:</span>
                <span><?php echo $receipt_data['transaction']['location_name']; ?></span>
            </div>
            <?php } ?>
            <div class="detail-row">
                <span class="detail-label">Payment Method:</span>
                <span><?php echo ucfirst($receipt_data['transaction']['payment_method']); ?></span>
            </div>
        </div>

        <!-- Items -->
        <table class="items-table">
            <thead>
                <tr>
                    <th>Item</th>
                    <th class="text-center">Qty</th>
                    <th class="text-right">Price</th>
                    <th class="text-right">Total</th>
                </tr>
            </thead>
            <tbody>
                <?php foreach ($receipt_data['items'] as $item) { ?>
                <tr>
                    <td>
                        <div class="item-name"><?php echo $item['product_name']; ?></div>
                        <?php if (!empty($item['sku'])) { ?>
                            <div class="item-sku">SKU: <?php echo $item['sku']; ?></div>
                        <?php } ?>
                    </td>
                    <td class="text-center"><?php echo $item['quantity']; ?></td>
                    <td class="text-right"><?php echo app_format_money($item['unit_price'], get_base_currency()); ?></td>
                    <td class="text-right"><?php echo app_format_money($item['total_price'], get_base_currency()); ?></td>
                </tr>
                <?php } ?>
            </tbody>
        </table>

        <!-- Totals -->
        <div class="totals-section">
            <div class="total-row">
                <span>Subtotal:</span>
                <span><?php echo app_format_money($receipt_data['transaction']['subtotal'], get_base_currency()); ?></span>
            </div>
            
            <?php if ($receipt_data['transaction']['discount_amount'] > 0) { ?>
            <div class="total-row">
                <span>Discount (<?php echo $receipt_data['transaction']['discount_value']; ?><?php echo $receipt_data['transaction']['discount_type'] == 'percentage' ? '%' : ''; ?>):</span>
                <span style="color: #28a745;">-<?php echo app_format_money($receipt_data['transaction']['discount_amount'], get_base_currency()); ?></span>
            </div>
            <?php } ?>
            
            <?php if ($receipt_data['transaction']['tax_amount'] > 0) { ?>
            <div class="total-row">
                <span>Tax (<?php echo $receipt_data['transaction']['tax_rate']; ?>%):</span>
                <span><?php echo app_format_money($receipt_data['transaction']['tax_amount'], get_base_currency()); ?></span>
            </div>
            <?php } ?>
            
            <div class="total-row grand-total">
                <span>TOTAL:</span>
                <span><?php echo app_format_money($receipt_data['transaction']['total_amount'], get_base_currency()); ?></span>
            </div>
        </div>

        <!-- Payment Information -->
        <?php if ($receipt_data['transaction']['payment_method'] == 'cash') { ?>
        <div class="payment-section">
            <div class="total-row">
                <span><strong>Amount Received:</strong></span>
                <span><?php echo app_format_money($receipt_data['transaction']['amount_received'], get_base_currency()); ?></span>
            </div>
            <div class="total-row">
                <span><strong>Change Given:</strong></span>
                <span><?php echo app_format_money($receipt_data['transaction']['change_amount'], get_base_currency()); ?></span>
            </div>
        </div>
        <?php } ?>

        <!-- Notes -->
        <?php if (!empty($receipt_data['transaction']['notes'])) { ?>
        <div class="notes">
            <div class="notes-title">Transaction Notes:</div>
            <?php echo nl2br(htmlspecialchars($receipt_data['transaction']['notes'])); ?>
        </div>
        <?php } ?>

        <!-- Footer -->
        <div class="footer">
            <div class="thank-you">Thank You for Your Business!</div>
            <p>We appreciate your patronage and look forward to serving you again.</p>
            <?php if (!empty($receipt_data['company']['website'])) { ?>
                <p>Visit our website: <a href="<?php echo $receipt_data['company']['website']; ?>" style="color: #007bff;"><?php echo $receipt_data['company']['website']; ?></a></p>
            <?php } ?>
            <p style="font-size: 12px; color: #999; margin-top: 20px;">
                This receipt was generated automatically on <?php echo date('F d, Y \a\t H:i'); ?>.<br>
                Please keep this receipt for your records.
            </p>
        </div>
    </div>
</body>
</html>
