<?php defined('BASEPATH') or exit('No direct script access allowed'); ?>
<?php init_head(); ?>
<div id="wrapper">
    <div class="content">
        <div class="row">
            <div class="col-md-12">
                <div class="panel_s">
                    <div class="panel-body">
                        <div class="row">
                            <div class="col-md-12">
                                <h4 class="no-margin">
                                    <?php echo $title; ?>
                                </h4>
                                <hr class="hr-panel-heading" />
                            </div>
                        </div>
                        
                        <!-- Filter Section -->
                        <div class="row">
                            <div class="col-md-12">
                                <div class="panel panel-default">
                                    <div class="panel-body">
                                        <div class="row">
                                            <div class="col-md-3">
                                                <div class="form-group">
                                                    <label for="filter-date-from"><?php echo _l('date_from'); ?></label>
                                                    <input type="date" class="form-control" id="filter-date-from" value="<?php echo date('Y-m-01'); ?>">
                                                </div>
                                            </div>
                                            <div class="col-md-3">
                                                <div class="form-group">
                                                    <label for="filter-date-to"><?php echo _l('date_to'); ?></label>
                                                    <input type="date" class="form-control" id="filter-date-to" value="<?php echo date('Y-m-d'); ?>">
                                                </div>
                                            </div>
                                            <div class="col-md-3">
                                                <div class="form-group">
                                                    <label for="filter-status"><?php echo _l('status'); ?></label>
                                                    <select class="form-control" id="filter-status">
                                                        <option value=""><?php echo _l('all'); ?></option>
                                                        <option value="completed"><?php echo _l('completed'); ?></option>
                                                        <option value="pending"><?php echo _l('pending'); ?></option>
                                                        <option value="cancelled"><?php echo _l('cancelled'); ?></option>
                                                    </select>
                                                </div>
                                            </div>
                                            <div class="col-md-3">
                                                <div class="form-group">
                                                    <label>&nbsp;</label><br>
                                                    <button type="button" class="btn btn-info" id="filter-transactions">
                                                        <i class="fa fa-filter"></i> <?php echo _l('filter'); ?>
                                                    </button>
                                                    <button type="button" class="btn btn-default" id="reset-filters">
                                                        <i class="fa fa-refresh"></i> <?php echo _l('reset'); ?>
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-12">
                                <div class="table-responsive">
                                    <table class="table dt-table" id="transactions-table">
                                        <thead>
                                            <tr>
                                                <th><?php echo _l('transaction_number'); ?></th>
                                                <th><?php echo _l('date'); ?></th>
                                                <th><?php echo _l('customer'); ?></th>
                                                <th><?php echo _l('cashier'); ?></th>
                                                <th><?php echo _l('total_amount'); ?></th>
                                                <th><?php echo _l('payment_method'); ?></th>
                                                <th><?php echo _l('status'); ?></th>
                                                <th><?php echo _l('options'); ?></th>
                                            </tr>
                                        </thead>
                                        <tbody></tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
$(document).ready(function() {
    // Initialize DataTable
    var transactionsTable = $('#transactions-table').DataTable({
        "processing": true,
        "serverSide": true,
        "ajax": {
            "url": "<?php echo admin_url('pos_inventory/transactions_table'); ?>",
            "type": "POST",
            "data": function(d) {
                d.date_from = $('#filter-date-from').val();
                d.date_to = $('#filter-date-to').val();
                d.status = $('#filter-status').val();
            }
        },
        "columns": [
            {"data": "transaction_number"},
            {"data": "transaction_date"},
            {"data": "customer_name"},
            {"data": "cashier_name"},
            {"data": "total_amount"},
            {"data": "payment_method"},
            {"data": "status"},
            {"data": "options", "orderable": false}
        ],
        "order": [[1, "desc"]]
    });

    // Filter transactions
    $('#filter-transactions').on('click', function() {
        transactionsTable.ajax.reload();
    });

    // Reset filters
    $('#reset-filters').on('click', function() {
        $('#filter-date-from').val('<?php echo date('Y-m-01'); ?>');
        $('#filter-date-to').val('<?php echo date('Y-m-d'); ?>');
        $('#filter-status').val('');
        transactionsTable.ajax.reload();
    });
});

// View transaction details
function viewTransaction(id) {
    window.open('<?php echo admin_url('pos_inventory/transaction/'); ?>' + id, '_blank');
}

// Print receipt
function printReceipt(id) {
    window.open('<?php echo admin_url('pos_inventory/print_receipt/'); ?>' + id, '_blank');
}

// Email receipt
function emailReceipt(id) {
    if (confirm('<?php echo _l('confirm_email_receipt'); ?>')) {
        $.post('<?php echo admin_url('pos_inventory/email_receipt'); ?>', {transaction_id: id}, function(response) {
            if (response.success) {
                alert_float('success', response.message);
            } else {
                alert_float('danger', response.message);
            }
        }, 'json');
    }
}

// Refund transaction
function refundTransaction(id) {
    if (confirm('<?php echo _l('confirm_refund_transaction'); ?>')) {
        $.post('<?php echo admin_url('pos_inventory/refund_transaction'); ?>', {transaction_id: id}, function(response) {
            if (response.success) {
                $('#transactions-table').DataTable().ajax.reload();
                alert_float('success', response.message);
            } else {
                alert_float('danger', response.message);
            }
        }, 'json');
    }
}
</script>

<?php init_tail(); ?>
