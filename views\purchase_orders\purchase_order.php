<?php defined('BASEPATH') or exit('No direct script access allowed'); ?>
<?php init_head(); ?>
<div id="wrapper">
    <div class="content">
        <div class="row">
            <div class="col-md-12">
                <div class="panel_s">
                    <div class="panel-body">
                        <div class="row">
                            <div class="col-md-8">
                                <h4 class="no-margin"><?php echo $title; ?></h4>
                            </div>
                            <div class="col-md-4 text-right">
                                <?php if (isset($purchase_order) && $purchase_order): ?>
                                    <div class="btn-group">
                                        <button type="button" class="btn btn-default dropdown-toggle" data-toggle="dropdown">
                                            <?php echo _l('actions'); ?> <span class="caret"></span>
                                        </button>
                                        <ul class="dropdown-menu dropdown-menu-right">
                                            <?php if ($purchase_order['status'] != 'received' && $purchase_order['status'] != 'cancelled'): ?>
                                                <li><a href="<?php echo admin_url('pos_inventory/receive_stock/' . $purchase_order['id']); ?>"><?php echo _l('receive_stock'); ?></a></li>
                                            <?php endif; ?>
                                            <?php if ($purchase_order['status'] == 'received' && !$purchase_order['invoice_id']): ?>
                                                <li><a href="<?php echo admin_url('pos_inventory/convert_po_to_invoice/' . $purchase_order['id']); ?>"><?php echo _l('convert_to_invoice'); ?></a></li>
                                            <?php endif; ?>
                                            <li><a href="#" onclick="printPO()"><?php echo _l('print'); ?></a></li>
                                            <li><a href="#" onclick="emailPO()"><?php echo _l('email'); ?></a></li>
                                        </ul>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>
                        <hr class="hr-panel-heading" />

                        <?php echo form_open(admin_url('pos_inventory/purchase_order/' . (isset($purchase_order) ? $purchase_order['id'] : '')), ['id' => 'purchase-order-form']); ?>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="supplier_id" class="control-label"><?php echo _l('supplier'); ?> <span class="text-danger">*</span></label>
                                    <select name="supplier_id" id="supplier_id" class="form-control selectpicker" data-live-search="true" required>
                                        <option value=""><?php echo _l('select_supplier'); ?></option>
                                        <?php foreach ($suppliers as $supplier): ?>
                                            <option value="<?php echo $supplier['id']; ?>" 
                                                <?php echo (isset($purchase_order) && $purchase_order['supplier_id'] == $supplier['id']) ? 'selected' : ''; ?>>
                                                <?php echo $supplier['name']; ?>
                                            </option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="location_id" class="control-label"><?php echo _l('location'); ?> <span class="text-danger">*</span></label>
                                    <select name="location_id" id="location_id" class="form-control selectpicker" required>
                                        <?php foreach ($locations as $location): ?>
                                            <option value="<?php echo $location['id']; ?>" 
                                                <?php echo (isset($purchase_order) && $purchase_order['location_id'] == $location['id']) ? 'selected' : ''; ?>>
                                                <?php echo $location['name']; ?>
                                            </option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label for="order_date" class="control-label"><?php echo _l('order_date'); ?></label>
                                    <input type="date" name="order_date" id="order_date" class="form-control" 
                                           value="<?php echo isset($purchase_order) ? $purchase_order['order_date'] : date('Y-m-d'); ?>">
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label for="expected_delivery" class="control-label"><?php echo _l('expected_delivery'); ?></label>
                                    <input type="date" name="expected_delivery" id="expected_delivery" class="form-control" 
                                           value="<?php echo isset($purchase_order) ? $purchase_order['expected_delivery'] : ''; ?>">
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label for="status" class="control-label"><?php echo _l('status'); ?></label>
                                    <select name="status" id="status" class="form-control">
                                        <option value="pending" <?php echo (isset($purchase_order) && $purchase_order['status'] == 'pending') ? 'selected' : ''; ?>><?php echo _l('pending'); ?></option>
                                        <option value="ordered" <?php echo (isset($purchase_order) && $purchase_order['status'] == 'ordered') ? 'selected' : ''; ?>><?php echo _l('ordered'); ?></option>
                                        <option value="partially_received" <?php echo (isset($purchase_order) && $purchase_order['status'] == 'partially_received') ? 'selected' : ''; ?>><?php echo _l('partially_received'); ?></option>
                                        <option value="received" <?php echo (isset($purchase_order) && $purchase_order['status'] == 'received') ? 'selected' : ''; ?>><?php echo _l('received'); ?></option>
                                        <option value="cancelled" <?php echo (isset($purchase_order) && $purchase_order['status'] == 'cancelled') ? 'selected' : ''; ?>><?php echo _l('cancelled'); ?></option>
                                        <option value="backorder" <?php echo (isset($purchase_order) && $purchase_order['status'] == 'backorder') ? 'selected' : ''; ?>><?php echo _l('backorder'); ?></option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="payment_terms" class="control-label"><?php echo _l('payment_terms'); ?></label>
                                    <input type="text" name="payment_terms" id="payment_terms" class="form-control" 
                                           value="<?php echo isset($purchase_order) ? $purchase_order['payment_terms'] : ''; ?>" 
                                           placeholder="<?php echo _l('payment_terms_placeholder'); ?>">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="shipping_method" class="control-label"><?php echo _l('shipping_method'); ?></label>
                                    <input type="text" name="shipping_method" id="shipping_method" class="form-control" 
                                           value="<?php echo isset($purchase_order) ? $purchase_order['shipping_method'] : ''; ?>" 
                                           placeholder="<?php echo _l('shipping_method_placeholder'); ?>">
                                </div>
                            </div>
                        </div>

                        <?php if (isset($purchase_order) && $purchase_order['tracking_number']): ?>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="tracking_number" class="control-label"><?php echo _l('tracking_number'); ?></label>
                                    <div class="input-group">
                                        <input type="text" id="tracking_number" class="form-control" 
                                               value="<?php echo $purchase_order['tracking_number']; ?>" readonly>
                                        <span class="input-group-btn">
                                            <button type="button" class="btn btn-default" onclick="editTracking()">
                                                <i class="fa fa-edit"></i>
                                            </button>
                                        </span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <?php endif; ?>

                        <!-- Purchase Order Items -->
                        <div class="row">
                            <div class="col-md-12">
                                <h4><?php echo _l('purchase_order_items'); ?></h4>
                                <div class="table-responsive">
                                    <table class="table table-bordered" id="po-items-table">
                                        <thead>
                                            <tr>
                                                <th width="30%"><?php echo _l('product'); ?></th>
                                                <th width="15%"><?php echo _l('quantity'); ?></th>
                                                <th width="15%"><?php echo _l('unit_cost'); ?></th>
                                                <th width="15%"><?php echo _l('total'); ?></th>
                                                <th width="15%"><?php echo _l('received'); ?></th>
                                                <th width="10%"><?php echo _l('actions'); ?></th>
                                            </tr>
                                        </thead>
                                        <tbody id="po-items-tbody">
                                            <?php if (isset($purchase_order) && !empty($purchase_order['items'])): ?>
                                                <?php foreach ($purchase_order['items'] as $item): ?>
                                                    <tr>
                                                        <td>
                                                            <input type="hidden" name="items[<?php echo $item['id']; ?>][product_id]" value="<?php echo $item['product_id']; ?>">
                                                            <?php echo $item['product_name']; ?>
                                                            <?php if ($item['variant_name']): ?>
                                                                <br><small class="text-muted"><?php echo $item['variant_name']; ?></small>
                                                            <?php endif; ?>
                                                        </td>
                                                        <td>
                                                            <input type="number" name="items[<?php echo $item['id']; ?>][quantity]" 
                                                                   class="form-control item-quantity" value="<?php echo $item['quantity']; ?>" min="1" step="1">
                                                        </td>
                                                        <td>
                                                            <input type="number" name="items[<?php echo $item['id']; ?>][unit_cost]" 
                                                                   class="form-control item-unit-cost" value="<?php echo $item['unit_cost']; ?>" min="0" step="0.01">
                                                        </td>
                                                        <td>
                                                            <span class="item-total"><?php echo number_format($item['quantity'] * $item['unit_cost'], 2); ?></span>
                                                        </td>
                                                        <td>
                                                            <span class="badge badge-info"><?php echo $item['received_quantity']; ?></span>
                                                        </td>
                                                        <td>
                                                            <button type="button" class="btn btn-sm btn-danger remove-item">
                                                                <i class="fa fa-trash"></i>
                                                            </button>
                                                        </td>
                                                    </tr>
                                                <?php endforeach; ?>
                                            <?php endif; ?>
                                        </tbody>
                                        <tfoot>
                                            <tr>
                                                <td colspan="6">
                                                    <button type="button" class="btn btn-info" id="add-item-btn">
                                                        <i class="fa fa-plus"></i> <?php echo _l('add_item'); ?>
                                                    </button>
                                                </td>
                                            </tr>
                                        </tfoot>
                                    </table>
                                </div>
                            </div>
                        </div>

                        <!-- Totals -->
                        <div class="row">
                            <div class="col-md-6 col-md-offset-6">
                                <table class="table">
                                    <tr>
                                        <td><strong><?php echo _l('subtotal'); ?>:</strong></td>
                                        <td class="text-right">
                                            <span id="subtotal-display"><?php echo isset($purchase_order) ? number_format($purchase_order['subtotal'], 2) : '0.00'; ?></span>
                                            <input type="hidden" name="subtotal" id="subtotal" value="<?php echo isset($purchase_order) ? $purchase_order['subtotal'] : 0; ?>">
                                        </td>
                                    </tr>
                                    <tr>
                                        <td><strong><?php echo _l('tax_amount'); ?>:</strong></td>
                                        <td class="text-right">
                                            <input type="number" name="tax_amount" id="tax_amount" class="form-control text-right" 
                                                   value="<?php echo isset($purchase_order) ? $purchase_order['tax_amount'] : 0; ?>" min="0" step="0.01">
                                        </td>
                                    </tr>
                                    <tr>
                                        <td><strong><?php echo _l('shipping_cost'); ?>:</strong></td>
                                        <td class="text-right">
                                            <input type="number" name="shipping_cost" id="shipping_cost" class="form-control text-right" 
                                                   value="<?php echo isset($purchase_order) ? $purchase_order['shipping_cost'] : 0; ?>" min="0" step="0.01">
                                        </td>
                                    </tr>
                                    <tr class="info">
                                        <td><strong><?php echo _l('total'); ?>:</strong></td>
                                        <td class="text-right">
                                            <strong><span id="total-display"><?php echo isset($purchase_order) ? number_format($purchase_order['total'], 2) : '0.00'; ?></span></strong>
                                            <input type="hidden" name="total" id="total" value="<?php echo isset($purchase_order) ? $purchase_order['total'] : 0; ?>">
                                        </td>
                                    </tr>
                                </table>
                            </div>
                        </div>

                        <!-- Notes -->
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="notes" class="control-label"><?php echo _l('notes'); ?></label>
                                    <textarea name="notes" id="notes" class="form-control" rows="4" 
                                              placeholder="<?php echo _l('notes_placeholder'); ?>"><?php echo isset($purchase_order) ? $purchase_order['notes'] : ''; ?></textarea>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="internal_notes" class="control-label"><?php echo _l('internal_notes'); ?></label>
                                    <textarea name="internal_notes" id="internal_notes" class="form-control" rows="4" 
                                              placeholder="<?php echo _l('internal_notes_placeholder'); ?>"><?php echo isset($purchase_order) ? $purchase_order['internal_notes'] : ''; ?></textarea>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-12">
                                <div class="form-group">
                                    <label for="terms_conditions" class="control-label"><?php echo _l('terms_conditions'); ?></label>
                                    <textarea name="terms_conditions" id="terms_conditions" class="form-control" rows="3" 
                                              placeholder="<?php echo _l('terms_conditions_placeholder'); ?>"><?php echo isset($purchase_order) ? $purchase_order['terms_conditions'] : ''; ?></textarea>
                                </div>
                            </div>
                        </div>

                        <div class="btn-bottom-toolbar text-right">
                            <button type="submit" class="btn btn-primary"><?php echo _l('save'); ?></button>
                            <a href="<?php echo admin_url('pos_inventory/purchase_orders'); ?>" class="btn btn-default"><?php echo _l('cancel'); ?></a>
                        </div>

                        <?php echo form_close(); ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Product Selection Modal -->
<div class="modal fade" id="product-selection-modal" tabindex="-1" role="dialog">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal">&times;</button>
                <h4 class="modal-title"><?php echo _l('select_product'); ?></h4>
            </div>
            <div class="modal-body">
                <div class="form-group">
                    <input type="text" id="product-search" class="form-control" placeholder="<?php echo _l('search_products'); ?>">
                </div>
                <div id="product-search-results"></div>
            </div>
        </div>
    </div>
</div>

<!-- Document Upload Modal -->
<?php if (isset($purchase_order)): ?>
<div class="modal fade" id="document-upload-modal" tabindex="-1" role="dialog">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal">&times;</button>
                <h4 class="modal-title"><?php echo _l('upload_document'); ?></h4>
            </div>
            <div class="modal-body">
                <form id="document-upload-form" enctype="multipart/form-data">
                    <input type="hidden" name="po_id" value="<?php echo $purchase_order['id']; ?>">

                    <div class="form-group">
                        <label for="document_name"><?php echo _l('document_name'); ?></label>
                        <input type="text" name="document_name" id="document_name" class="form-control" required>
                    </div>

                    <div class="form-group">
                        <label for="document_type"><?php echo _l('document_type'); ?></label>
                        <select name="document_type" id="document_type" class="form-control">
                            <option value="invoice"><?php echo _l('invoice'); ?></option>
                            <option value="receipt"><?php echo _l('receipt'); ?></option>
                            <option value="contract"><?php echo _l('contract'); ?></option>
                            <option value="specification"><?php echo _l('specification'); ?></option>
                            <option value="other"><?php echo _l('other'); ?></option>
                        </select>
                    </div>

                    <div class="form-group">
                        <label for="document"><?php echo _l('document_file'); ?></label>
                        <input type="file" name="document" id="document" class="form-control" required
                               accept=".pdf,.doc,.docx,.jpg,.jpeg,.png,.gif">
                    </div>

                    <div class="form-group">
                        <label for="description"><?php echo _l('description'); ?></label>
                        <textarea name="description" id="description" class="form-control" rows="3"></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-default" data-dismiss="modal"><?php echo _l('cancel'); ?></button>
                <button type="button" class="btn btn-primary" onclick="uploadDocument()"><?php echo _l('upload'); ?></button>
            </div>
        </div>
    </div>
</div>
<?php endif; ?>

<script>
$(document).ready(function() {
    // Initialize form
    initializePOForm();

    // Load existing documents and backorders if editing
    <?php if (isset($purchase_order)): ?>
        loadDocuments();
        loadBackorders();
    <?php endif; ?>
});

function initializePOForm() {
    // Add item button
    $('#add-item-btn').on('click', function() {
        $('#product-selection-modal').modal('show');
    });

    // Product search
    $('#product-search').on('keyup', function() {
        var search = $(this).val();
        if (search.length >= 2) {
            searchProducts(search);
        }
    });

    // Remove item
    $(document).on('click', '.remove-item', function() {
        $(this).closest('tr').remove();
        calculateTotals();
    });

    // Calculate totals on input change
    $(document).on('input', '.item-quantity, .item-unit-cost, #tax_amount, #shipping_cost', function() {
        calculateTotals();
    });

    // Supplier change
    $('#supplier_id').on('change', function() {
        var supplierId = $(this).val();
        if (supplierId) {
            loadSupplierDetails(supplierId);
        }
    });

    // Form submission
    $('#purchase-order-form').on('submit', function(e) {
        if (!validateForm()) {
            e.preventDefault();
        }
    });
}

function searchProducts(search) {
    $.post(admin_url + 'pos_inventory/search_products_for_po', {
        search: search,
        limit: 20
    }, function(response) {
        if (response.success) {
            displayProductResults(response.products);
        }
    }, 'json');
}

function displayProductResults(products) {
    var html = '<div class="table-responsive"><table class="table table-hover">';
    html += '<thead><tr><th>Product</th><th>SKU</th><th>Cost Price</th><th>Action</th></tr></thead><tbody>';

    $.each(products, function(index, product) {
        html += '<tr>';
        html += '<td>' + product.name + '</td>';
        html += '<td>' + (product.sku || '') + '</td>';
        html += '<td>' + parseFloat(product.cost_price).toFixed(2) + '</td>';
        html += '<td><button type="button" class="btn btn-sm btn-primary" onclick="addProductToOrder(' + product.id + ', \'' + product.name + '\', ' + product.cost_price + ')">Add</button></td>';
        html += '</tr>';
    });

    html += '</tbody></table></div>';
    $('#product-search-results').html(html);
}

function addProductToOrder(productId, productName, costPrice) {
    var itemIndex = Date.now();
    var html = '<tr>';
    html += '<td>';
    html += '<input type="hidden" name="items[' + itemIndex + '][product_id]" value="' + productId + '">';
    html += productName;
    html += '</td>';
    html += '<td><input type="number" name="items[' + itemIndex + '][quantity]" class="form-control item-quantity" value="1" min="1" step="1"></td>';
    html += '<td><input type="number" name="items[' + itemIndex + '][unit_cost]" class="form-control item-unit-cost" value="' + costPrice + '" min="0" step="0.01"></td>';
    html += '<td><span class="item-total">' + costPrice + '</span></td>';
    html += '<td><span class="badge badge-info">0</span></td>';
    html += '<td><button type="button" class="btn btn-sm btn-danger remove-item"><i class="fa fa-trash"></i></button></td>';
    html += '</tr>';

    $('#po-items-tbody').append(html);
    $('#product-selection-modal').modal('hide');
    $('#product-search').val('');
    $('#product-search-results').html('');

    calculateTotals();
}

function calculateTotals() {
    var subtotal = 0;

    $('.item-quantity').each(function() {
        var row = $(this).closest('tr');
        var quantity = parseFloat($(this).val()) || 0;
        var unitCost = parseFloat(row.find('.item-unit-cost').val()) || 0;
        var total = quantity * unitCost;

        row.find('.item-total').text(total.toFixed(2));
        subtotal += total;
    });

    var taxAmount = parseFloat($('#tax_amount').val()) || 0;
    var shippingCost = parseFloat($('#shipping_cost').val()) || 0;
    var total = subtotal + taxAmount + shippingCost;

    $('#subtotal').val(subtotal.toFixed(2));
    $('#subtotal-display').text(subtotal.toFixed(2));
    $('#total').val(total.toFixed(2));
    $('#total-display').text(total.toFixed(2));
}

function loadSupplierDetails(supplierId) {
    $.post(admin_url + 'pos_inventory/get_supplier_details', {
        supplier_id: supplierId
    }, function(response) {
        if (response.success) {
            var supplier = response.supplier;
            if (supplier.payment_terms) {
                $('#payment_terms').val(supplier.payment_terms);
            }
        }
    }, 'json');
}

function validateForm() {
    var isValid = true;
    var errors = [];

    if (!$('#supplier_id').val()) {
        errors.push('Please select a supplier');
        isValid = false;
    }

    if ($('#po-items-tbody tr').length === 0) {
        errors.push('Please add at least one item');
        isValid = false;
    }

    if (!isValid) {
        alert_float('danger', errors.join('<br>'));
    }

    return isValid;
}

<?php if (isset($purchase_order)): ?>
function loadDocuments() {
    // Implementation for loading and displaying documents
    // This would make an AJAX call to get documents and display them
}

function loadBackorders() {
    // Implementation for loading and displaying backorders
    // This would make an AJAX call to get backorders and display them
}

function uploadDocument() {
    var formData = new FormData($('#document-upload-form')[0]);

    $.ajax({
        url: admin_url + 'pos_inventory/upload_po_document',
        type: 'POST',
        data: formData,
        processData: false,
        contentType: false,
        success: function(response) {
            if (response.success) {
                alert_float('success', response.message);
                $('#document-upload-modal').modal('hide');
                loadDocuments();
            } else {
                alert_float('danger', response.message);
            }
        },
        error: function() {
            alert_float('danger', 'Error uploading document');
        }
    });
}

function editTracking() {
    // Implementation for editing tracking number
    var currentTracking = $('#tracking_number').val();
    var newTracking = prompt('Enter new tracking number:', currentTracking);

    if (newTracking !== null && newTracking !== currentTracking) {
        $.post(admin_url + 'pos_inventory/update_tracking', {
            po_id: <?php echo $purchase_order['id']; ?>,
            tracking_number: newTracking
        }, function(response) {
            if (response.success) {
                $('#tracking_number').val(newTracking);
                alert_float('success', response.message);
            } else {
                alert_float('danger', response.message);
            }
        }, 'json');
    }
}

function printPO() {
    window.open(admin_url + 'pos_inventory/print_purchase_order/<?php echo $purchase_order['id']; ?>', '_blank');
}

function emailPO() {
    // Implementation for emailing PO
    var email = prompt('Enter email address:');
    if (email) {
        $.post(admin_url + 'pos_inventory/email_purchase_order', {
            po_id: <?php echo $purchase_order['id']; ?>,
            email: email
        }, function(response) {
            if (response.success) {
                alert_float('success', response.message);
            } else {
                alert_float('danger', response.message);
            }
        }, 'json');
    }
}
<?php endif; ?>
</script>

<?php init_tail(); ?>
