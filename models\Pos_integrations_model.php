<?php

defined('BASEPATH') or exit('No direct script access allowed');

/**
 * POS Integrations Model
 */
class Pos_integrations_model extends App_Model
{
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Get WooCommerce settings
     */
    public function get_woocommerce_settings()
    {
        $this->db->where('platform', 'woocommerce');
        $this->db->where('status', 1);
        return $this->db->get(db_prefix() . 'pos_integrations')->row_array();
    }

    /**
     * Get Shopify settings
     */
    public function get_shopify_settings()
    {
        $this->db->where('platform', 'shopify');
        $this->db->where('status', 1);
        return $this->db->get(db_prefix() . 'pos_integrations')->row_array();
    }

    /**
     * Save integration settings
     */
    public function save_integration_settings($platform, $data)
    {
        $integration_data = [
            'platform' => $platform,
            'name' => $data['name'],
            'api_url' => $data['api_url'],
            'api_key' => $data['api_key'],
            'api_secret' => isset($data['api_secret']) ? $data['api_secret'] : null,
            'access_token' => isset($data['access_token']) ? $data['access_token'] : null,
            'webhook_secret' => isset($data['webhook_secret']) ? $data['webhook_secret'] : null,
            'settings' => isset($data['settings']) ? json_encode($data['settings']) : null,
            'sync_products' => isset($data['sync_products']) ? $data['sync_products'] : 1,
            'sync_inventory' => isset($data['sync_inventory']) ? $data['sync_inventory'] : 1,
            'sync_orders' => isset($data['sync_orders']) ? $data['sync_orders'] : 0,
            'sync_customers' => isset($data['sync_customers']) ? $data['sync_customers'] : 0,
            'sync_categories' => isset($data['sync_categories']) ? $data['sync_categories'] : 1,
            'sync_images' => isset($data['sync_images']) ? $data['sync_images'] : 1,
            'two_way_sync' => isset($data['two_way_sync']) ? $data['two_way_sync'] : 0,
            'auto_sync' => isset($data['auto_sync']) ? $data['auto_sync'] : 0,
            'sync_frequency' => isset($data['sync_frequency']) ? $data['sync_frequency'] : 60,
            'sync_on_product_update' => isset($data['sync_on_product_update']) ? $data['sync_on_product_update'] : 0,
            'sync_on_inventory_change' => isset($data['sync_on_inventory_change']) ? $data['sync_on_inventory_change'] : 0,
            'default_location_id' => isset($data['default_location_id']) ? $data['default_location_id'] : null,
            'price_sync_direction' => isset($data['price_sync_direction']) ? $data['price_sync_direction'] : 'to_platform',
            'inventory_sync_direction' => isset($data['inventory_sync_direction']) ? $data['inventory_sync_direction'] : 'both',
            'status' => isset($data['status']) ? $data['status'] : 1
        ];

        // Check if integration already exists
        $this->db->where('platform', $platform);
        $existing = $this->db->get(db_prefix() . 'pos_integrations')->row();

        if ($existing) {
            $this->db->where('id', $existing->id);
            return $this->db->update(db_prefix() . 'pos_integrations', $integration_data);
        } else {
            $this->db->insert(db_prefix() . 'pos_integrations', $integration_data);
            return $this->db->insert_id();
        }
    }

    /**
     * Test connection to platform
     */
    public function test_connection($platform)
    {
        $integration = null;
        
        if ($platform == 'woocommerce') {
            $integration = $this->get_woocommerce_settings();
        } elseif ($platform == 'shopify') {
            $integration = $this->get_shopify_settings();
        }

        if (!$integration) {
            return ['success' => false, 'message' => 'Integration not configured'];
        }

        try {
            if ($platform == 'woocommerce') {
                return $this->test_woocommerce_connection($integration);
            } elseif ($platform == 'shopify') {
                return $this->test_shopify_connection($integration);
            }
        } catch (Exception $e) {
            return ['success' => false, 'message' => $e->getMessage()];
        }

        return ['success' => false, 'message' => 'Unknown platform'];
    }

    /**
     * Test WooCommerce connection
     */
    private function test_woocommerce_connection($integration)
    {
        $url = rtrim($integration['api_url'], '/') . '/wp-json/wc/v3/system_status';
        
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_USERPWD, $integration['api_key'] . ':' . $integration['api_secret']);
        curl_setopt($ch, CURLOPT_HTTPHEADER, ['Content-Type: application/json']);
        curl_setopt($ch, CURLOPT_TIMEOUT, 30);
        
        $response = curl_exec($ch);
        $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);

        if ($http_code == 200) {
            return ['success' => true, 'message' => 'Connection successful'];
        } else {
            return ['success' => false, 'message' => 'Connection failed. HTTP Code: ' . $http_code];
        }
    }

    /**
     * Test Shopify connection
     */
    private function test_shopify_connection($integration)
    {
        $url = rtrim($integration['api_url'], '/') . '/admin/api/2023-10/shop.json';
        
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_HTTPHEADER, [
            'Content-Type: application/json',
            'X-Shopify-Access-Token: ' . $integration['access_token']
        ]);
        curl_setopt($ch, CURLOPT_TIMEOUT, 30);
        
        $response = curl_exec($ch);
        $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);

        if ($http_code == 200) {
            return ['success' => true, 'message' => 'Connection successful'];
        } else {
            return ['success' => false, 'message' => 'Connection failed. HTTP Code: ' . $http_code];
        }
    }

    /**
     * Start sync process
     */
    public function start_sync($platform, $sync_type)
    {
        $integration = null;
        
        if ($platform == 'woocommerce') {
            $integration = $this->get_woocommerce_settings();
        } elseif ($platform == 'shopify') {
            $integration = $this->get_shopify_settings();
        }

        if (!$integration) {
            return ['success' => false, 'message' => 'Integration not configured'];
        }

        // Create sync log entry
        $log_data = [
            'integration_id' => $integration['id'],
            'sync_type' => $sync_type,
            'status' => 'in_progress',
            'started_at' => date('Y-m-d H:i:s')
        ];

        $this->db->insert(db_prefix() . 'pos_sync_logs', $log_data);
        $log_id = $this->db->insert_id();

        try {
            if ($platform == 'woocommerce') {
                $result = $this->sync_woocommerce($integration, $sync_type, $log_id);
            } elseif ($platform == 'shopify') {
                $result = $this->sync_shopify($integration, $sync_type, $log_id);
            } else {
                throw new Exception('Unknown platform');
            }

            // Update sync log
            $this->db->where('id', $log_id);
            $this->db->update(db_prefix() . 'pos_sync_logs', [
                'status' => 'success',
                'message' => $result['message'],
                'records_processed' => $result['processed'],
                'records_success' => $result['success'],
                'records_failed' => $result['failed'],
                'completed_at' => date('Y-m-d H:i:s')
            ]);

            // Update last sync time
            $this->db->where('id', $integration['id']);
            $this->db->update(db_prefix() . 'pos_integrations', ['last_sync' => date('Y-m-d H:i:s')]);

            return ['success' => true, 'message' => $result['message']];

        } catch (Exception $e) {
            // Update sync log with error
            $this->db->where('id', $log_id);
            $this->db->update(db_prefix() . 'pos_sync_logs', [
                'status' => 'failed',
                'message' => $e->getMessage(),
                'completed_at' => date('Y-m-d H:i:s')
            ]);

            return ['success' => false, 'message' => $e->getMessage()];
        }
    }

    /**
     * Sync with WooCommerce
     */
    private function sync_woocommerce($integration, $sync_type, $log_id)
    {
        $this->load->model('pos_products_model');
        
        $processed = 0;
        $success = 0;
        $failed = 0;

        if ($sync_type == 'products') {
            if ($integration['two_way_sync'] && in_array($integration['price_sync_direction'], ['from_platform', 'both'])) {
                // Sync products from WooCommerce to Perfex
                $result = $this->sync_products_from_woocommerce($integration);
                $processed += $result['processed'];
                $success += $result['success'];
                $failed += $result['failed'];
            }

            if (!$integration['two_way_sync'] || in_array($integration['price_sync_direction'], ['to_platform', 'both'])) {
                // Sync products from Perfex to WooCommerce
                $result = $this->sync_products_to_woocommerce($integration);
                $processed += $result['processed'];
                $success += $result['success'];
                $failed += $result['failed'];
            }

        } elseif ($sync_type == 'inventory') {
            if ($integration['inventory_sync_direction'] == 'from_platform') {
                // Sync inventory from WooCommerce to Perfex
                $result = $this->sync_inventory_from_woocommerce($integration);
                $processed += $result['processed'];
                $success += $result['success'];
                $failed += $result['failed'];
            } elseif ($integration['inventory_sync_direction'] == 'both') {
                // Two-way sync - sync both directions
                $result1 = $this->sync_inventory_to_woocommerce($integration);
                $result2 = $this->sync_inventory_from_woocommerce($integration);

                $processed += $result1['processed'] + $result2['processed'];
                $success += $result1['success'] + $result2['success'];
                $failed += $result1['failed'] + $result2['failed'];
            } else {
                // Default: sync inventory to WooCommerce
                $result = $this->sync_inventory_to_woocommerce($integration);
                $processed += $result['processed'];
                $success += $result['success'];
                $failed += $result['failed'];
            }

        } elseif ($sync_type == 'orders' && $integration['sync_orders']) {
            $result = $this->sync_orders_from_woocommerce($integration);
            $processed += $result['processed'];
            $success += $result['success'];
            $failed += $result['failed'];
        }

        return [
            'message' => "Sync completed. Processed: $processed, Success: $success, Failed: $failed",
            'processed' => $processed,
            'success' => $success,
            'failed' => $failed
        ];
    }

    /**
     * Sync with Shopify
     */
    private function sync_shopify($integration, $sync_type, $log_id)
    {
        $processed = 0;
        $success = 0;
        $failed = 0;

        if ($sync_type == 'products') {
            if ($integration['two_way_sync'] && in_array($integration['price_sync_direction'], ['from_platform', 'both'])) {
                // Sync products from Shopify to Perfex
                $result = $this->sync_products_from_shopify($integration);
                $processed += $result['processed'];
                $success += $result['success'];
                $failed += $result['failed'];
            }

            if (!$integration['two_way_sync'] || in_array($integration['price_sync_direction'], ['to_platform', 'both'])) {
                // Sync products from Perfex to Shopify
                $result = $this->sync_products_to_shopify($integration);
                $processed += $result['processed'];
                $success += $result['success'];
                $failed += $result['failed'];
            }

        } elseif ($sync_type == 'inventory') {
            if ($integration['inventory_sync_direction'] == 'from_platform') {
                // Sync inventory from Shopify to Perfex
                $result = $this->sync_inventory_from_shopify($integration);
                $processed += $result['processed'];
                $success += $result['success'];
                $failed += $result['failed'];
            } elseif ($integration['inventory_sync_direction'] == 'both') {
                // Two-way sync - sync both directions
                $result1 = $this->sync_inventory_to_shopify($integration);
                $result2 = $this->sync_inventory_from_shopify($integration);

                $processed += $result1['processed'] + $result2['processed'];
                $success += $result1['success'] + $result2['success'];
                $failed += $result1['failed'] + $result2['failed'];
            } else {
                // Default: sync inventory to Shopify
                $result = $this->sync_inventory_to_shopify($integration);
                $processed += $result['processed'];
                $success += $result['success'];
                $failed += $result['failed'];
            }

        } elseif ($sync_type == 'orders' && $integration['sync_orders']) {
            $result = $this->sync_orders_from_shopify($integration);
            $processed += $result['processed'];
            $success += $result['success'];
            $failed += $result['failed'];
        }

        // Update last sync timestamps
        $update_data = ['last_sync' => date('Y-m-d H:i:s')];
        if ($sync_type == 'products') {
            $update_data['last_product_sync'] = date('Y-m-d H:i:s');
        } elseif ($sync_type == 'inventory') {
            $update_data['last_inventory_sync'] = date('Y-m-d H:i:s');
        } elseif ($sync_type == 'orders') {
            $update_data['last_order_sync'] = date('Y-m-d H:i:s');
        }

        $this->db->where('id', $integration['id']);
        $this->db->update(db_prefix() . 'pos_integrations', $update_data);

        return [
            'message' => "Shopify sync completed. Processed: $processed, Success: $success, Failed: $failed",
            'processed' => $processed,
            'success' => $success,
            'failed' => $failed
        ];
    }

    /**
     * Get meta value from WooCommerce meta data
     */
    private function get_meta_value($meta_data, $key)
    {
        foreach ($meta_data as $meta) {
            if ($meta['key'] == $key) {
                return $meta['value'];
            }
        }
        return null;
    }

    /**
     * Get recent sync logs
     */
    public function get_recent_sync_logs($limit = 20)
    {
        $this->db->select('sl.*, i.platform, i.name as integration_name');
        $this->db->from(db_prefix() . 'pos_sync_logs sl');
        $this->db->join(db_prefix() . 'pos_integrations i', 'i.id = sl.integration_id');
        $this->db->order_by('sl.created_at', 'DESC');
        $this->db->limit($limit);

        return $this->db->get()->result_array();
    }

    /**
     * Sync products from WooCommerce to Perfex
     */
    private function sync_products_from_woocommerce($integration)
    {
        $this->load->model('pos_products_model');

        $processed = 0;
        $success = 0;
        $failed = 0;
        $page = 1;
        $per_page = 100;

        do {
            $url = rtrim($integration['api_url'], '/') . '/wp-json/wc/v3/products?page=' . $page . '&per_page=' . $per_page;

            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $url);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_USERPWD, $integration['api_key'] . ':' . $integration['api_secret']);
            curl_setopt($ch, CURLOPT_HTTPHEADER, ['Content-Type: application/json']);
            curl_setopt($ch, CURLOPT_TIMEOUT, 30);

            $response = curl_exec($ch);
            $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            curl_close($ch);

            if ($http_code != 200) {
                throw new Exception('Failed to fetch products from WooCommerce');
            }

            $products = json_decode($response, true);

            if (empty($products)) {
                break;
            }

            foreach ($products as $wc_product) {
                $processed++;

                try {
                    // Check if product already exists
                    $this->db->where('woocommerce_id', $wc_product['id']);
                    $existing = $this->db->get(db_prefix() . 'pos_products')->row();

                    $product_data = [
                        'name' => $wc_product['name'],
                        'description' => strip_tags($wc_product['description']),
                        'sku' => $wc_product['sku'] ?: 'WC-' . $wc_product['id'],
                        'sale_price' => $wc_product['price'],
                        'cost_price' => isset($wc_product['meta_data']) ? $this->get_meta_value($wc_product['meta_data'], '_cost_price') : 0,
                        'weight' => $wc_product['weight'] ?: null,
                        'status' => $wc_product['status'] == 'publish' ? 1 : 0,
                        'woocommerce_id' => $wc_product['id']
                    ];

                    if ($existing) {
                        $this->db->where('id', $existing->id);
                        $this->db->update(db_prefix() . 'pos_products', $product_data);
                        $product_id = $existing->id;
                    } else {
                        $product_data['created_by'] = get_staff_user_id();
                        $this->db->insert(db_prefix() . 'pos_products', $product_data);
                        $product_id = $this->db->insert_id();
                    }

                    // Create or update product mapping
                    $this->create_or_update_product_mapping($integration['id'], $product_id, null, $wc_product['id'], null, $wc_product['sku']);

                    // Handle product variants if they exist
                    if (!empty($wc_product['variations'])) {
                        $this->sync_product_variants_from_woocommerce($integration, $product_id, $wc_product['id'], $wc_product['variations']);
                    }

                    $success++;
                } catch (Exception $e) {
                    $failed++;
                }
            }

            $page++;
        } while (count($products) == $per_page);

        return [
            'processed' => $processed,
            'success' => $success,
            'failed' => $failed
        ];
    }

    /**
     * Sync products from Perfex to WooCommerce
     */
    private function sync_products_to_woocommerce($integration)
    {
        $this->load->model('pos_products_model');

        $processed = 0;
        $success = 0;
        $failed = 0;

        // Get products that need to be synced to WooCommerce
        $this->db->select('p.*');
        $this->db->from(db_prefix() . 'pos_products p');
        $this->db->where('p.status', 1);

        // Only sync products that don't have WooCommerce ID or need updates
        $products = $this->db->get()->result_array();

        foreach ($products as $product) {
            $processed++;

            try {
                $product_data = [
                    'name' => $product['name'],
                    'description' => $product['description'],
                    'sku' => $product['sku'],
                    'regular_price' => (string)$product['sale_price'],
                    'weight' => $product['weight'] ?: '',
                    'status' => $product['status'] ? 'publish' : 'draft',
                    'manage_stock' => true,
                    'meta_data' => [
                        [
                            'key' => '_cost_price',
                            'value' => (string)$product['cost_price']
                        ]
                    ]
                ];

                if ($product['woocommerce_id']) {
                    // Update existing product
                    $url = rtrim($integration['api_url'], '/') . '/wp-json/wc/v3/products/' . $product['woocommerce_id'];
                    $method = 'PUT';
                } else {
                    // Create new product
                    $url = rtrim($integration['api_url'], '/') . '/wp-json/wc/v3/products';
                    $method = 'POST';
                }

                $ch = curl_init();
                curl_setopt($ch, CURLOPT_URL, $url);
                curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
                curl_setopt($ch, CURLOPT_CUSTOMREQUEST, $method);
                curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($product_data));
                curl_setopt($ch, CURLOPT_USERPWD, $integration['api_key'] . ':' . $integration['api_secret']);
                curl_setopt($ch, CURLOPT_HTTPHEADER, ['Content-Type: application/json']);
                curl_setopt($ch, CURLOPT_TIMEOUT, 30);

                $response = curl_exec($ch);
                $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
                curl_close($ch);

                if (in_array($http_code, [200, 201])) {
                    $wc_product = json_decode($response, true);

                    // Update product with WooCommerce ID if it's a new product
                    if (!$product['woocommerce_id'] && isset($wc_product['id'])) {
                        $this->db->where('id', $product['id']);
                        $this->db->update(db_prefix() . 'pos_products', ['woocommerce_id' => $wc_product['id']]);

                        // Create product mapping
                        $this->create_or_update_product_mapping($integration['id'], $product['id'], null, $wc_product['id'], null, $product['sku']);
                    }

                    $success++;
                } else {
                    $failed++;
                }
            } catch (Exception $e) {
                $failed++;
            }
        }

        return [
            'processed' => $processed,
            'success' => $success,
            'failed' => $failed
        ];
    }

    /**
     * Sync inventory to WooCommerce
     */
    private function sync_inventory_to_woocommerce($integration)
    {
        $this->load->model('pos_inventory_model');

        $processed = 0;
        $success = 0;
        $failed = 0;

        // Get products with inventory to sync
        $location_id = $integration['default_location_id'];

        $this->db->select('p.*, SUM(i.quantity) as total_quantity');
        $this->db->from(db_prefix() . 'pos_products p');
        $this->db->join(db_prefix() . 'pos_inventory i', 'i.product_id = p.id', 'left');
        $this->db->where('p.woocommerce_id IS NOT NULL');
        $this->db->where('p.status', 1);

        if ($location_id) {
            $this->db->where('i.location_id', $location_id);
        }

        $this->db->group_by('p.id');
        $products = $this->db->get()->result_array();

        foreach ($products as $product) {
            $processed++;

            try {
                $url = rtrim($integration['api_url'], '/') . '/wp-json/wc/v3/products/' . $product['woocommerce_id'];

                $update_data = [
                    'stock_quantity' => (int)$product['total_quantity'],
                    'manage_stock' => true,
                    'stock_status' => $product['total_quantity'] > 0 ? 'instock' : 'outofstock'
                ];

                $ch = curl_init();
                curl_setopt($ch, CURLOPT_URL, $url);
                curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
                curl_setopt($ch, CURLOPT_CUSTOMREQUEST, 'PUT');
                curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($update_data));
                curl_setopt($ch, CURLOPT_USERPWD, $integration['api_key'] . ':' . $integration['api_secret']);
                curl_setopt($ch, CURLOPT_HTTPHEADER, ['Content-Type: application/json']);
                curl_setopt($ch, CURLOPT_TIMEOUT, 30);

                $response = curl_exec($ch);
                $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
                curl_close($ch);

                if ($http_code == 200) {
                    $success++;

                    // Update product mapping sync status
                    $this->update_product_mapping_sync_status($integration['id'], $product['id'], null, 'synced');
                } else {
                    $failed++;
                    $this->update_product_mapping_sync_status($integration['id'], $product['id'], null, 'error', 'HTTP ' . $http_code);
                }
            } catch (Exception $e) {
                $failed++;
                $this->update_product_mapping_sync_status($integration['id'], $product['id'], null, 'error', $e->getMessage());
            }
        }

        return [
            'processed' => $processed,
            'success' => $success,
            'failed' => $failed
        ];
    }

    /**
     * Sync inventory from WooCommerce
     */
    private function sync_inventory_from_woocommerce($integration)
    {
        $this->load->model('pos_inventory_model');

        $processed = 0;
        $success = 0;
        $failed = 0;
        $page = 1;
        $per_page = 100;

        do {
            $url = rtrim($integration['api_url'], '/') . '/wp-json/wc/v3/products?page=' . $page . '&per_page=' . $per_page . '&manage_stock=true';

            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $url);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_USERPWD, $integration['api_key'] . ':' . $integration['api_secret']);
            curl_setopt($ch, CURLOPT_HTTPHEADER, ['Content-Type: application/json']);
            curl_setopt($ch, CURLOPT_TIMEOUT, 30);

            $response = curl_exec($ch);
            $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            curl_close($ch);

            if ($http_code != 200) {
                throw new Exception('Failed to fetch products from WooCommerce');
            }

            $products = json_decode($response, true);

            if (empty($products)) {
                break;
            }

            foreach ($products as $wc_product) {
                $processed++;

                try {
                    // Find corresponding product in Perfex
                    $this->db->where('woocommerce_id', $wc_product['id']);
                    $local_product = $this->db->get(db_prefix() . 'pos_products')->row_array();

                    if ($local_product) {
                        $location_id = $integration['default_location_id'] ?: 1;
                        $wc_stock = (int)$wc_product['stock_quantity'];

                        // Get current local stock
                        $this->db->select('SUM(quantity) as total_quantity');
                        $this->db->from(db_prefix() . 'pos_inventory');
                        $this->db->where('product_id', $local_product['id']);
                        $this->db->where('location_id', $location_id);
                        $current_stock = $this->db->get()->row_array();
                        $local_stock = (int)$current_stock['total_quantity'];

                        if ($wc_stock != $local_stock) {
                            // Adjust inventory to match WooCommerce
                            $adjustment = $wc_stock - $local_stock;

                            $this->pos_inventory_model->adjust_stock([
                                'product_id' => $local_product['id'],
                                'location_id' => $location_id,
                                'adjustment_type' => $adjustment > 0 ? 'increase' : 'decrease',
                                'quantity' => abs($adjustment),
                                'reason' => 'WooCommerce Sync',
                                'notes' => 'Synced from WooCommerce. WC Stock: ' . $wc_stock . ', Local Stock: ' . $local_stock
                            ]);
                        }

                        $success++;
                    }
                } catch (Exception $e) {
                    $failed++;
                }
            }

            $page++;
        } while (count($products) == $per_page);

        return [
            'processed' => $processed,
            'success' => $success,
            'failed' => $failed
        ];
    }

    /**
     * Sync orders from WooCommerce
     */
    private function sync_orders_from_woocommerce($integration)
    {
        $processed = 0;
        $success = 0;
        $failed = 0;
        $page = 1;
        $per_page = 50;

        // Get last sync date to only fetch new orders
        $last_sync = $integration['last_order_sync'] ?: date('Y-m-d', strtotime('-30 days'));

        do {
            $url = rtrim($integration['api_url'], '/') . '/wp-json/wc/v3/orders?page=' . $page . '&per_page=' . $per_page . '&after=' . $last_sync;

            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $url);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_USERPWD, $integration['api_key'] . ':' . $integration['api_secret']);
            curl_setopt($ch, CURLOPT_HTTPHEADER, ['Content-Type: application/json']);
            curl_setopt($ch, CURLOPT_TIMEOUT, 30);

            $response = curl_exec($ch);
            $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            curl_close($ch);

            if ($http_code != 200) {
                throw new Exception('Failed to fetch orders from WooCommerce');
            }

            $orders = json_decode($response, true);

            if (empty($orders)) {
                break;
            }

            foreach ($orders as $wc_order) {
                $processed++;

                try {
                    // Check if order already exists
                    $this->db->where('woocommerce_order_id', $wc_order['id']);
                    $existing = $this->db->get(db_prefix() . 'pos_woocommerce_orders')->row();

                    if (!$existing) {
                        // Create new order record
                        $order_data = [
                            'woocommerce_order_id' => $wc_order['id'],
                            'order_number' => $wc_order['number'],
                            'status' => $wc_order['status'],
                            'total' => $wc_order['total'],
                            'customer_email' => $wc_order['billing']['email'],
                            'customer_name' => $wc_order['billing']['first_name'] . ' ' . $wc_order['billing']['last_name'],
                            'order_data' => json_encode($wc_order),
                            'created_at' => date('Y-m-d H:i:s', strtotime($wc_order['date_created']))
                        ];

                        $this->db->insert(db_prefix() . 'pos_woocommerce_orders', $order_data);
                        $success++;
                    }
                } catch (Exception $e) {
                    $failed++;
                }
            }

            $page++;
        } while (count($orders) == $per_page);

        return [
            'processed' => $processed,
            'success' => $success,
            'failed' => $failed
        ];
    }

    /**
     * Create or update product mapping
     */
    private function create_or_update_product_mapping($integration_id, $product_id, $variant_id, $platform_product_id, $platform_variant_id, $platform_sku)
    {
        // Check if mapping already exists
        $this->db->where('integration_id', $integration_id);
        $this->db->where('product_id', $product_id);
        if ($variant_id) {
            $this->db->where('variant_id', $variant_id);
        } else {
            $this->db->where('variant_id IS NULL');
        }
        $existing = $this->db->get(db_prefix() . 'pos_product_mappings')->row();

        $mapping_data = [
            'platform_product_id' => $platform_product_id,
            'platform_variant_id' => $platform_variant_id,
            'platform_sku' => $platform_sku,
            'sync_enabled' => 1,
            'last_synced' => date('Y-m-d H:i:s'),
            'sync_status' => 'synced'
        ];

        if ($existing) {
            $this->db->where('id', $existing->id);
            return $this->db->update(db_prefix() . 'pos_product_mappings', $mapping_data);
        } else {
            $mapping_data['integration_id'] = $integration_id;
            $mapping_data['product_id'] = $product_id;
            $mapping_data['variant_id'] = $variant_id;

            $this->db->insert(db_prefix() . 'pos_product_mappings', $mapping_data);
            return $this->db->insert_id();
        }
    }

    /**
     * Update product mapping sync status
     */
    private function update_product_mapping_sync_status($integration_id, $product_id, $variant_id, $status, $error = null)
    {
        $this->db->where('integration_id', $integration_id);
        $this->db->where('product_id', $product_id);
        if ($variant_id) {
            $this->db->where('variant_id', $variant_id);
        } else {
            $this->db->where('variant_id IS NULL');
        }

        $update_data = [
            'sync_status' => $status,
            'last_synced' => date('Y-m-d H:i:s')
        ];

        if ($error) {
            $update_data['sync_error'] = $error;
        }

        return $this->db->update(db_prefix() . 'pos_product_mappings', $update_data);
    }

    /**
     * Get product mappings for integration
     */
    public function get_product_mappings($integration_id, $limit = null)
    {
        $this->db->select('pm.*, p.name as product_name, p.sku, pv.name as variant_name');
        $this->db->from(db_prefix() . 'pos_product_mappings pm');
        $this->db->join(db_prefix() . 'pos_products p', 'p.id = pm.product_id');
        $this->db->join(db_prefix() . 'pos_product_variants pv', 'pv.id = pm.variant_id', 'left');
        $this->db->where('pm.integration_id', $integration_id);
        $this->db->order_by('pm.last_synced', 'DESC');

        if ($limit) {
            $this->db->limit($limit);
        }

        return $this->db->get()->result_array();
    }

    /**
     * Get sync statistics
     */
    public function get_sync_statistics($integration_id, $days = 30)
    {
        $date_from = date('Y-m-d', strtotime("-{$days} days"));

        $this->db->select('
            COUNT(*) as total_syncs,
            SUM(CASE WHEN status = "success" THEN 1 ELSE 0 END) as successful_syncs,
            SUM(CASE WHEN status = "failed" THEN 1 ELSE 0 END) as failed_syncs,
            SUM(records_processed) as total_records,
            SUM(records_success) as successful_records,
            SUM(records_failed) as failed_records
        ');
        $this->db->from(db_prefix() . 'pos_sync_logs');
        $this->db->where('integration_id', $integration_id);
        $this->db->where('started_at >=', $date_from);

        return $this->db->get()->row_array();
    }

    /**
     * Trigger product sync on update
     */
    public function trigger_product_sync($product_id, $sync_type = 'product_update')
    {
        // Get all active integrations with auto-sync enabled
        $this->db->where('status', 1);
        $this->db->where('sync_on_product_update', 1);
        $integrations = $this->db->get(db_prefix() . 'pos_integrations')->result_array();

        foreach ($integrations as $integration) {
            // Queue sync job or trigger immediate sync
            $this->start_sync($integration['platform'], 'products');
        }
    }

    /**
     * Trigger inventory sync on change
     */
    public function trigger_inventory_sync($product_id, $location_id = null)
    {
        // Get all active integrations with auto inventory sync enabled
        $this->db->where('status', 1);
        $this->db->where('sync_on_inventory_change', 1);
        $integrations = $this->db->get(db_prefix() . 'pos_integrations')->result_array();

        foreach ($integrations as $integration) {
            // Only sync if location matches or no specific location set
            if (!$integration['default_location_id'] || !$location_id || $integration['default_location_id'] == $location_id) {
                $this->start_sync($integration['platform'], 'inventory');
            }
        }
    }

    /**
     * Sync products from Shopify to Perfex
     */
    private function sync_products_from_shopify($integration)
    {
        $this->load->model('pos_products_model');

        $processed = 0;
        $success = 0;
        $failed = 0;
        $page_info = null;
        $limit = 50;

        do {
            $url = rtrim($integration['api_url'], '/') . '/admin/api/2023-10/products.json?limit=' . $limit;
            if ($page_info) {
                $url .= '&page_info=' . $page_info;
            }

            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $url);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_HTTPHEADER, [
                'Content-Type: application/json',
                'X-Shopify-Access-Token: ' . $integration['access_token']
            ]);
            curl_setopt($ch, CURLOPT_TIMEOUT, 30);

            $response = curl_exec($ch);
            $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            $headers = curl_getinfo($ch, CURLINFO_HEADER_OUT);
            curl_close($ch);

            if ($http_code != 200) {
                throw new Exception('Failed to fetch products from Shopify');
            }

            $response_data = json_decode($response, true);
            $products = $response_data['products'] ?? [];

            if (empty($products)) {
                break;
            }

            foreach ($products as $shopify_product) {
                $processed++;

                try {
                    // Check if product already exists
                    $this->db->where('shopify_id', $shopify_product['id']);
                    $existing = $this->db->get(db_prefix() . 'pos_products')->row();

                    $product_data = [
                        'name' => $shopify_product['title'],
                        'description' => strip_tags($shopify_product['body_html']),
                        'sku' => $shopify_product['variants'][0]['sku'] ?: 'SHOPIFY-' . $shopify_product['id'],
                        'sale_price' => $shopify_product['variants'][0]['price'],
                        'weight' => $shopify_product['variants'][0]['weight'] ?: null,
                        'status' => $shopify_product['status'] == 'active' ? 1 : 0,
                        'shopify_id' => $shopify_product['id']
                    ];

                    if ($existing) {
                        $this->db->where('id', $existing->id);
                        $this->db->update(db_prefix() . 'pos_products', $product_data);
                        $product_id = $existing->id;
                    } else {
                        $product_data['created_by'] = get_staff_user_id();
                        $this->db->insert(db_prefix() . 'pos_products', $product_data);
                        $product_id = $this->db->insert_id();
                    }

                    // Create or update product mapping
                    $this->create_or_update_product_mapping($integration['id'], $product_id, null, $shopify_product['id'], null, $product_data['sku']);

                    // Handle product variants
                    if (count($shopify_product['variants']) > 1) {
                        $this->sync_product_variants_from_shopify($integration, $product_id, $shopify_product['id'], $shopify_product['variants']);
                    }

                    $success++;
                } catch (Exception $e) {
                    $failed++;
                }
            }

            // Get next page info from Link header
            $page_info = $this->extract_shopify_page_info($headers);

        } while ($page_info && count($products) == $limit);

        return [
            'processed' => $processed,
            'success' => $success,
            'failed' => $failed
        ];
    }

    /**
     * Sync products from Perfex to Shopify
     */
    private function sync_products_to_shopify($integration)
    {
        $this->load->model('pos_products_model');

        $processed = 0;
        $success = 0;
        $failed = 0;

        // Get products that need to be synced to Shopify
        $this->db->select('p.*');
        $this->db->from(db_prefix() . 'pos_products p');
        $this->db->where('p.status', 1);

        $products = $this->db->get()->result_array();

        foreach ($products as $product) {
            $processed++;

            try {
                $product_data = [
                    'product' => [
                        'title' => $product['name'],
                        'body_html' => $product['description'],
                        'vendor' => get_option('companyname'),
                        'product_type' => 'Physical',
                        'status' => $product['status'] ? 'active' : 'draft',
                        'variants' => [
                            [
                                'price' => (string)$product['sale_price'],
                                'sku' => $product['sku'],
                                'weight' => $product['weight'] ?: 0,
                                'weight_unit' => 'kg',
                                'inventory_management' => 'shopify',
                                'inventory_policy' => 'deny'
                            ]
                        ]
                    ]
                ];

                if ($product['shopify_id']) {
                    // Update existing product
                    $url = rtrim($integration['api_url'], '/') . '/admin/api/2023-10/products/' . $product['shopify_id'] . '.json';
                    $method = 'PUT';
                } else {
                    // Create new product
                    $url = rtrim($integration['api_url'], '/') . '/admin/api/2023-10/products.json';
                    $method = 'POST';
                }

                $ch = curl_init();
                curl_setopt($ch, CURLOPT_URL, $url);
                curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
                curl_setopt($ch, CURLOPT_CUSTOMREQUEST, $method);
                curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($product_data));
                curl_setopt($ch, CURLOPT_HTTPHEADER, [
                    'Content-Type: application/json',
                    'X-Shopify-Access-Token: ' . $integration['access_token']
                ]);
                curl_setopt($ch, CURLOPT_TIMEOUT, 30);

                $response = curl_exec($ch);
                $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
                curl_close($ch);

                if (in_array($http_code, [200, 201])) {
                    $shopify_product = json_decode($response, true);

                    // Update product with Shopify ID if it's a new product
                    if (!$product['shopify_id'] && isset($shopify_product['product']['id'])) {
                        $this->db->where('id', $product['id']);
                        $this->db->update(db_prefix() . 'pos_products', ['shopify_id' => $shopify_product['product']['id']]);

                        // Create product mapping
                        $this->create_or_update_product_mapping($integration['id'], $product['id'], null, $shopify_product['product']['id'], null, $product['sku']);
                    }

                    $success++;
                } else {
                    $failed++;
                }
            } catch (Exception $e) {
                $failed++;
            }
        }

        return [
            'processed' => $processed,
            'success' => $success,
            'failed' => $failed
        ];
    }

    /**
     * Extract page info from Shopify Link header
     */
    private function extract_shopify_page_info($headers)
    {
        // Parse Link header for pagination
        if (preg_match('/<([^>]+)>;\s*rel="next"/', $headers, $matches)) {
            $url = $matches[1];
            if (preg_match('/page_info=([^&]+)/', $url, $page_matches)) {
                return $page_matches[1];
            }
        }
        return null;
    }

    /**
     * Sync inventory to Shopify
     */
    private function sync_inventory_to_shopify($integration)
    {
        // Implementation for Shopify inventory sync
        return [
            'processed' => 0,
            'success' => 0,
            'failed' => 0
        ];
    }

    /**
     * Sync inventory from Shopify
     */
    private function sync_inventory_from_shopify($integration)
    {
        // Implementation for Shopify inventory sync
        return [
            'processed' => 0,
            'success' => 0,
            'failed' => 0
        ];
    }

    /**
     * Sync orders from Shopify
     */
    private function sync_orders_from_shopify($integration)
    {
        // Implementation for Shopify order sync
        return [
            'processed' => 0,
            'success' => 0,
            'failed' => 0
        ];
    }

    /**
     * Get mapping statistics
     */
    public function get_mapping_statistics($integration_id)
    {
        // Get total POS products
        $this->db->where('status', 1);
        $total_products = $this->db->count_all_results(db_prefix() . 'pos_products');

        // Get mapped products
        $this->db->where('integration_id', $integration_id);
        $mapped_products = $this->db->count_all_results(db_prefix() . 'pos_product_mappings');

        // Get platform products count
        $platform_products = 0;
        $integration = $this->get_integration($integration_id);
        if ($integration) {
            if ($integration['platform'] == 'woocommerce') {
                $platform_products = $this->count_woocommerce_products($integration);
            } else {
                $platform_products = $this->count_shopify_products($integration);
            }
        }

        return [
            'total_products' => $total_products,
            'mapped_products' => $mapped_products,
            'unmapped_products' => $total_products - $mapped_products,
            'platform_products' => $platform_products
        ];
    }

    /**
     * Get sync log statistics
     */
    public function get_sync_log_statistics($integration_id = null)
    {
        // Total logs
        $this->db->select('COUNT(*) as total');
        if ($integration_id) {
            $this->db->where('integration_id', $integration_id);
        }
        $total_logs = $this->db->get(db_prefix() . 'pos_sync_logs')->row()->total;

        // Successful logs
        $this->db->select('COUNT(*) as total');
        $this->db->where('status', 'success');
        if ($integration_id) {
            $this->db->where('integration_id', $integration_id);
        }
        $successful_logs = $this->db->get(db_prefix() . 'pos_sync_logs')->row()->total;

        // Failed logs
        $this->db->select('COUNT(*) as total');
        $this->db->where('status', 'failed');
        if ($integration_id) {
            $this->db->where('integration_id', $integration_id);
        }
        $failed_logs = $this->db->get(db_prefix() . 'pos_sync_logs')->row()->total;

        // Running logs
        $this->db->select('COUNT(*) as total');
        $this->db->where('status', 'running');
        if ($integration_id) {
            $this->db->where('integration_id', $integration_id);
        }
        $running_logs = $this->db->get(db_prefix() . 'pos_sync_logs')->row()->total;

        // Today's logs
        $this->db->select('COUNT(*) as total');
        $this->db->where('DATE(started_at)', date('Y-m-d'));
        if ($integration_id) {
            $this->db->where('integration_id', $integration_id);
        }
        $today_logs = $this->db->get(db_prefix() . 'pos_sync_logs')->row()->total;

        // Average duration
        $this->db->select('AVG(TIMESTAMPDIFF(SECOND, started_at, completed_at)) as avg_duration');
        $this->db->where('status', 'success');
        $this->db->where('completed_at IS NOT NULL');
        if ($integration_id) {
            $this->db->where('integration_id', $integration_id);
        }
        $avg_duration = $this->db->get(db_prefix() . 'pos_sync_logs')->row()->avg_duration;

        return [
            'total_logs' => $total_logs,
            'successful_logs' => $successful_logs,
            'failed_logs' => $failed_logs,
            'running_logs' => $running_logs,
            'today_logs' => $today_logs,
            'avg_duration' => round($avg_duration ?: 0, 1)
        ];
    }

    /**
     * Get integration
     */
    public function get_integration($integration_id)
    {
        $this->db->where('id', $integration_id);
        return $this->db->get(db_prefix() . 'pos_integrations')->row_array();
    }

    /**
     * Get sync log
     */
    public function get_sync_log($log_id)
    {
        $this->db->where('id', $log_id);
        return $this->db->get(db_prefix() . 'pos_sync_logs')->row_array();
    }

    /**
     * Count WooCommerce products
     */
    private function count_woocommerce_products($integration)
    {
        // This would normally use the WooCommerce API
        // For now, return a placeholder value
        return 100;
    }

    /**
     * Count Shopify products
     */
    private function count_shopify_products($integration)
    {
        // This would normally use the Shopify API
        // For now, return a placeholder value
        return 100;
    }

    /**
     * Get sync logs for export
     */
    public function get_sync_logs_for_export($integration_id = null, $platform = null, $sync_type = null, $status = null, $date_from = null, $date_to = null)
    {
        $this->db->select('sl.*, i.name as integration_name, i.platform');
        $this->db->from(db_prefix() . 'pos_sync_logs sl');
        $this->db->join(db_prefix() . 'pos_integrations i', 'i.id = sl.integration_id');

        if ($integration_id) {
            $this->db->where('sl.integration_id', $integration_id);
        }

        if ($platform) {
            $this->db->where('i.platform', $platform);
        }

        if ($sync_type) {
            $this->db->where('sl.sync_type', $sync_type);
        }

        if ($status) {
            $this->db->where('sl.status', $status);
        }

        if ($date_from) {
            $this->db->where('DATE(sl.started_at) >=', $date_from);
        }

        if ($date_to) {
            $this->db->where('DATE(sl.started_at) <=', $date_to);
        }

        $this->db->order_by('sl.id', 'DESC');
        return $this->db->get()->result_array();
    }

    /**
     * Search platform products
     */
    public function search_platform_products($integration_id, $query)
    {
        $integration = $this->get_integration($integration_id);

        if (!$integration) {
            return [];
        }

        // This would normally use the platform API to search products
        // For now, return placeholder data
        return [
            [
                'id' => '1',
                'name' => 'Sample Product 1',
                'sku' => 'SP001',
                'price' => '$19.99'
            ],
            [
                'id' => '2',
                'name' => 'Sample Product 2',
                'sku' => 'SP002',
                'price' => '$29.99'
            ],
            [
                'id' => '3',
                'name' => 'Sample Product 3',
                'sku' => 'SP003',
                'price' => '$39.99'
            ]
        ];
    }
}
