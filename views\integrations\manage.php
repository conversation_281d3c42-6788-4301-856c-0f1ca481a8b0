<?php defined('BASEPATH') or exit('No direct script access allowed'); ?>
<?php init_head(); ?>
<div id="wrapper">
    <div class="content">
        <div class="row">
            <div class="col-md-12">
                <div class="panel_s">
                    <div class="panel-body">
                        <div class="row">
                            <div class="col-md-8">
                                <h4 class="no-margin"><?php echo _l('integrations_management'); ?></h4>
                            </div>
                            <div class="col-md-4 text-right">
                                <div class="btn-group">
                                    <button type="button" class="btn btn-primary dropdown-toggle" data-toggle="dropdown">
                                        <i class="fa fa-plus"></i> <?php echo _l('add_integration'); ?> <span class="caret"></span>
                                    </button>
                                    <ul class="dropdown-menu dropdown-menu-right">
                                        <li><a href="<?php echo admin_url('pos_inventory/configure_integration/woocommerce'); ?>">
                                            <i class="fa fa-wordpress"></i> WooCommerce
                                        </a></li>
                                        <li><a href="<?php echo admin_url('pos_inventory/configure_integration/shopify'); ?>">
                                            <i class="fa fa-shopping-bag"></i> Shopify
                                        </a></li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                        <hr class="hr-panel-heading" />

                        <!-- Integration Status Overview -->
                        <div class="row">
                            <div class="col-md-12">
                                <div class="panel panel-info">
                                    <div class="panel-heading">
                                        <h4 class="panel-title"><?php echo _l('integration_status_overview'); ?></h4>
                                    </div>
                                    <div class="panel-body">
                                        <div class="row">
                                            <div class="col-md-6">
                                                <!-- WooCommerce Integration -->
                                                <div class="integration-card">
                                                    <div class="row">
                                                        <div class="col-md-2 text-center">
                                                            <i class="fa fa-wordpress fa-3x text-primary"></i>
                                                        </div>
                                                        <div class="col-md-10">
                                                            <h4>WooCommerce</h4>
                                                            <?php if ($woocommerce_settings): ?>
                                                                <p class="text-success">
                                                                    <i class="fa fa-check-circle"></i> <?php echo _l('configured'); ?>
                                                                    <?php if ($woocommerce_settings['status']): ?>
                                                                        <span class="label label-success"><?php echo _l('active'); ?></span>
                                                                    <?php else: ?>
                                                                        <span class="label label-warning"><?php echo _l('inactive'); ?></span>
                                                                    <?php endif; ?>
                                                                </p>
                                                                <p><strong><?php echo _l('store_url'); ?>:</strong> <?php echo $woocommerce_settings['api_url']; ?></p>
                                                                <p><strong><?php echo _l('last_sync'); ?>:</strong> 
                                                                    <?php echo $woocommerce_settings['last_sync'] ? _dt($woocommerce_settings['last_sync']) : _l('never'); ?>
                                                                </p>
                                                                <div class="btn-group btn-group-sm">
                                                                    <a href="<?php echo admin_url('pos_inventory/configure_integration/woocommerce'); ?>" class="btn btn-primary">
                                                                        <i class="fa fa-cog"></i> <?php echo _l('configure'); ?>
                                                                    </a>
                                                                    <button type="button" class="btn btn-info" onclick="testConnection('woocommerce')">
                                                                        <i class="fa fa-plug"></i> <?php echo _l('test_connection'); ?>
                                                                    </button>
                                                                    <button type="button" class="btn btn-success" onclick="startSync('woocommerce', 'products')">
                                                                        <i class="fa fa-refresh"></i> <?php echo _l('sync_now'); ?>
                                                                    </button>
                                                                    <a href="<?php echo admin_url('pos_inventory/product_mappings/' . $woocommerce_settings['id']); ?>" class="btn btn-default">
                                                                        <i class="fa fa-link"></i> <?php echo _l('mappings'); ?>
                                                                    </a>
                                                                </div>
                                                            <?php else: ?>
                                                                <p class="text-muted">
                                                                    <i class="fa fa-exclamation-circle"></i> <?php echo _l('not_configured'); ?>
                                                                </p>
                                                                <a href="<?php echo admin_url('pos_inventory/configure_integration/woocommerce'); ?>" class="btn btn-primary btn-sm">
                                                                    <i class="fa fa-plus"></i> <?php echo _l('setup_now'); ?>
                                                                </a>
                                                            <?php endif; ?>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <!-- Shopify Integration -->
                                                <div class="integration-card">
                                                    <div class="row">
                                                        <div class="col-md-2 text-center">
                                                            <i class="fa fa-shopping-bag fa-3x text-success"></i>
                                                        </div>
                                                        <div class="col-md-10">
                                                            <h4>Shopify</h4>
                                                            <?php if ($shopify_settings): ?>
                                                                <p class="text-success">
                                                                    <i class="fa fa-check-circle"></i> <?php echo _l('configured'); ?>
                                                                    <?php if ($shopify_settings['status']): ?>
                                                                        <span class="label label-success"><?php echo _l('active'); ?></span>
                                                                    <?php else: ?>
                                                                        <span class="label label-warning"><?php echo _l('inactive'); ?></span>
                                                                    <?php endif; ?>
                                                                </p>
                                                                <p><strong><?php echo _l('store_url'); ?>:</strong> <?php echo $shopify_settings['api_url']; ?></p>
                                                                <p><strong><?php echo _l('last_sync'); ?>:</strong> 
                                                                    <?php echo $shopify_settings['last_sync'] ? _dt($shopify_settings['last_sync']) : _l('never'); ?>
                                                                </p>
                                                                <div class="btn-group btn-group-sm">
                                                                    <a href="<?php echo admin_url('pos_inventory/configure_integration/shopify'); ?>" class="btn btn-primary">
                                                                        <i class="fa fa-cog"></i> <?php echo _l('configure'); ?>
                                                                    </a>
                                                                    <button type="button" class="btn btn-info" onclick="testConnection('shopify')">
                                                                        <i class="fa fa-plug"></i> <?php echo _l('test_connection'); ?>
                                                                    </button>
                                                                    <button type="button" class="btn btn-success" onclick="startSync('shopify', 'products')">
                                                                        <i class="fa fa-refresh"></i> <?php echo _l('sync_now'); ?>
                                                                    </button>
                                                                    <a href="<?php echo admin_url('pos_inventory/product_mappings/' . $shopify_settings['id']); ?>" class="btn btn-default">
                                                                        <i class="fa fa-link"></i> <?php echo _l('mappings'); ?>
                                                                    </a>
                                                                </div>
                                                            <?php else: ?>
                                                                <p class="text-muted">
                                                                    <i class="fa fa-exclamation-circle"></i> <?php echo _l('not_configured'); ?>
                                                                </p>
                                                                <a href="<?php echo admin_url('pos_inventory/configure_integration/shopify'); ?>" class="btn btn-primary btn-sm">
                                                                    <i class="fa fa-plus"></i> <?php echo _l('setup_now'); ?>
                                                                </a>
                                                            <?php endif; ?>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Sync Statistics -->
                        <div class="row">
                            <div class="col-md-3">
                                <div class="panel panel-default">
                                    <div class="panel-body text-center">
                                        <h3 class="no-margin text-primary" id="total-syncs">0</h3>
                                        <p class="text-muted"><?php echo _l('total_syncs_today'); ?></p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="panel panel-default">
                                    <div class="panel-body text-center">
                                        <h3 class="no-margin text-success" id="successful-syncs">0</h3>
                                        <p class="text-muted"><?php echo _l('successful_syncs'); ?></p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="panel panel-default">
                                    <div class="panel-body text-center">
                                        <h3 class="no-margin text-danger" id="failed-syncs">0</h3>
                                        <p class="text-muted"><?php echo _l('failed_syncs'); ?></p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="panel panel-default">
                                    <div class="panel-body text-center">
                                        <h3 class="no-margin text-info" id="products-synced">0</h3>
                                        <p class="text-muted"><?php echo _l('products_synced'); ?></p>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Quick Actions -->
                        <div class="row">
                            <div class="col-md-12">
                                <div class="panel panel-default">
                                    <div class="panel-heading">
                                        <h4 class="panel-title"><?php echo _l('quick_actions'); ?></h4>
                                    </div>
                                    <div class="panel-body">
                                        <div class="btn-group">
                                            <button type="button" class="btn btn-primary" onclick="syncAll('products')">
                                                <i class="fa fa-cubes"></i> <?php echo _l('sync_all_products'); ?>
                                            </button>
                                            <button type="button" class="btn btn-info" onclick="syncAll('inventory')">
                                                <i class="fa fa-warehouse"></i> <?php echo _l('sync_all_inventory'); ?>
                                            </button>
                                            <button type="button" class="btn btn-success" onclick="syncAll('orders')">
                                                <i class="fa fa-shopping-cart"></i> <?php echo _l('sync_all_orders'); ?>
                                            </button>
                                        </div>
                                        <div class="btn-group pull-right">
                                            <a href="<?php echo admin_url('pos_inventory/sync_logs'); ?>" class="btn btn-default">
                                                <i class="fa fa-list"></i> <?php echo _l('view_sync_logs'); ?>
                                            </a>
                                            <button type="button" class="btn btn-warning" onclick="clearSyncLogs()">
                                                <i class="fa fa-trash"></i> <?php echo _l('clear_old_logs'); ?>
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Recent Sync Logs -->
                        <div class="row">
                            <div class="col-md-12">
                                <div class="panel panel-default">
                                    <div class="panel-heading">
                                        <h4 class="panel-title"><?php echo _l('recent_sync_activity'); ?></h4>
                                    </div>
                                    <div class="panel-body">
                                        <div class="table-responsive">
                                            <table class="table table-striped">
                                                <thead>
                                                    <tr>
                                                        <th><?php echo _l('platform'); ?></th>
                                                        <th><?php echo _l('sync_type'); ?></th>
                                                        <th><?php echo _l('status'); ?></th>
                                                        <th><?php echo _l('records_processed'); ?></th>
                                                        <th><?php echo _l('started_at'); ?></th>
                                                        <th><?php echo _l('duration'); ?></th>
                                                        <th><?php echo _l('actions'); ?></th>
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    <?php if (!empty($sync_logs)): ?>
                                                        <?php foreach ($sync_logs as $log): ?>
                                                            <tr>
                                                                <td>
                                                                    <i class="fa fa-<?php echo $log['platform'] == 'woocommerce' ? 'wordpress' : 'shopping-bag'; ?>"></i>
                                                                    <?php echo ucfirst($log['platform']); ?>
                                                                </td>
                                                                <td><?php echo ucfirst(str_replace('_', ' ', $log['sync_type'])); ?></td>
                                                                <td>
                                                                    <?php if ($log['status'] == 'success'): ?>
                                                                        <span class="label label-success"><?php echo _l('success'); ?></span>
                                                                    <?php elseif ($log['status'] == 'failed'): ?>
                                                                        <span class="label label-danger"><?php echo _l('failed'); ?></span>
                                                                    <?php else: ?>
                                                                        <span class="label label-info"><?php echo _l('in_progress'); ?></span>
                                                                    <?php endif; ?>
                                                                </td>
                                                                <td>
                                                                    <span class="badge badge-info"><?php echo $log['records_processed']; ?></span>
                                                                    <small class="text-muted">
                                                                        (<?php echo $log['records_success']; ?> success, <?php echo $log['records_failed']; ?> failed)
                                                                    </small>
                                                                </td>
                                                                <td><?php echo _dt($log['started_at']); ?></td>
                                                                <td>
                                                                    <?php if ($log['completed_at']): ?>
                                                                        <?php 
                                                                        $duration = strtotime($log['completed_at']) - strtotime($log['started_at']);
                                                                        echo gmdate('H:i:s', $duration);
                                                                        ?>
                                                                    <?php else: ?>
                                                                        <span class="text-muted"><?php echo _l('running'); ?></span>
                                                                    <?php endif; ?>
                                                                </td>
                                                                <td>
                                                                    <button type="button" class="btn btn-sm btn-info" onclick="viewSyncDetails(<?php echo $log['id']; ?>)">
                                                                        <i class="fa fa-eye"></i>
                                                                    </button>
                                                                    <?php if ($log['status'] == 'failed'): ?>
                                                                        <button type="button" class="btn btn-sm btn-warning" onclick="retrySyncLog(<?php echo $log['id']; ?>)">
                                                                            <i class="fa fa-refresh"></i>
                                                                        </button>
                                                                    <?php endif; ?>
                                                                </td>
                                                            </tr>
                                                        <?php endforeach; ?>
                                                    <?php else: ?>
                                                        <tr>
                                                            <td colspan="7" class="text-center text-muted">
                                                                <?php echo _l('no_sync_logs_found'); ?>
                                                            </td>
                                                        </tr>
                                                    <?php endif; ?>
                                                </tbody>
                                            </table>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Sync Progress Modal -->
<div class="modal fade" id="sync-progress-modal" tabindex="-1" role="dialog">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h4 class="modal-title"><?php echo _l('sync_in_progress'); ?></h4>
            </div>
            <div class="modal-body">
                <div class="progress">
                    <div class="progress-bar progress-bar-striped active" role="progressbar" style="width: 0%">
                        <span class="sr-only">0% Complete</span>
                    </div>
                </div>
                <p id="sync-status-text"><?php echo _l('initializing_sync'); ?></p>
            </div>
        </div>
    </div>
</div>

<style>
.integration-card {
    border: 1px solid #ddd;
    border-radius: 5px;
    padding: 15px;
    margin-bottom: 15px;
    background: #f9f9f9;
}

.integration-card h4 {
    margin-top: 0;
}
</style>

<script>
$(document).ready(function() {
    loadSyncStatistics();
    
    // Auto-refresh statistics every 30 seconds
    setInterval(loadSyncStatistics, 30000);
});

function loadSyncStatistics() {
    $.post(admin_url + 'pos_inventory/get_integration_statistics', {}, function(response) {
        if (response.success) {
            var stats = response.statistics;
            $('#total-syncs').text(stats.total_syncs || 0);
            $('#successful-syncs').text(stats.successful_syncs || 0);
            $('#failed-syncs').text(stats.failed_syncs || 0);
            $('#products-synced').text(stats.products_synced || 0);
        }
    }, 'json');
}

function testConnection(platform) {
    $.post(admin_url + 'pos_inventory/test_integration_connection', {
        platform: platform
    }, function(response) {
        if (response.success) {
            alert_float('success', response.message);
        } else {
            alert_float('danger', response.message);
        }
    }, 'json');
}

function startSync(platform, syncType) {
    $('#sync-progress-modal').modal('show');
    $('#sync-status-text').text('Starting ' + syncType + ' sync for ' + platform + '...');
    
    $.post(admin_url + 'pos_inventory/start_sync', {
        platform: platform,
        sync_type: syncType
    }, function(response) {
        $('#sync-progress-modal').modal('hide');
        
        if (response.success) {
            alert_float('success', response.message);
            loadSyncStatistics();
            // Refresh the page to show updated sync logs
            setTimeout(function() {
                location.reload();
            }, 2000);
        } else {
            alert_float('danger', response.message);
        }
    }, 'json');
}

function syncAll(syncType) {
    if (confirm('This will sync ' + syncType + ' for all configured integrations. Continue?')) {
        $('#sync-progress-modal').modal('show');
        $('#sync-status-text').text('Starting ' + syncType + ' sync for all platforms...');
        
        var platforms = [];
        <?php if ($woocommerce_settings && $woocommerce_settings['status']): ?>
            platforms.push('woocommerce');
        <?php endif; ?>
        <?php if ($shopify_settings && $shopify_settings['status']): ?>
            platforms.push('shopify');
        <?php endif; ?>
        
        var completed = 0;
        var total = platforms.length;
        
        if (total === 0) {
            $('#sync-progress-modal').modal('hide');
            alert_float('warning', 'No active integrations found');
            return;
        }
        
        platforms.forEach(function(platform) {
            $.post(admin_url + 'pos_inventory/start_sync', {
                platform: platform,
                sync_type: syncType
            }, function(response) {
                completed++;
                var progress = (completed / total) * 100;
                $('.progress-bar').css('width', progress + '%');
                
                if (completed === total) {
                    $('#sync-progress-modal').modal('hide');
                    alert_float('success', 'Sync completed for all platforms');
                    loadSyncStatistics();
                    setTimeout(function() {
                        location.reload();
                    }, 2000);
                }
            }, 'json');
        });
    }
}

function viewSyncDetails(logId) {
    window.open(admin_url + 'pos_inventory/sync_log_details/' + logId, '_blank', 'width=800,height=600');
}

function retrySyncLog(logId) {
    if (confirm('Retry this sync operation?')) {
        $.post(admin_url + 'pos_inventory/retry_sync_log', {
            log_id: logId
        }, function(response) {
            if (response.success) {
                alert_float('success', response.message);
                location.reload();
            } else {
                alert_float('danger', response.message);
            }
        }, 'json');
    }
}

function clearSyncLogs() {
    if (confirm('This will delete sync logs older than 30 days. Continue?')) {
        $.post(admin_url + 'pos_inventory/clear_old_sync_logs', {}, function(response) {
            if (response.success) {
                alert_float('success', response.message);
                location.reload();
            } else {
                alert_float('danger', response.message);
            }
        }, 'json');
    }
}
</script>

<?php init_tail(); ?>
