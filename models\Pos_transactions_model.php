<?php

defined('BASEPATH') or exit('No direct script access allowed');

/**
 * POS Transactions Model
 */
class Pos_transactions_model extends App_Model
{
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Get all transactions
     */
    public function get_all($where = [])
    {
        // Use COALESCE to handle both 'company' and 'firstname/lastname' for customer name
        $this->db->select('t.*, COALESCE(c.company, CONCAT(c.firstname, " ", c.lastname), "Walk-in Customer") as customer_name, l.name as location_name, s.firstname, s.lastname');
        $this->db->from(db_prefix() . 'pos_transactions t');
        $this->db->join(db_prefix() . 'clients c', 'c.userid = t.customer_id', 'left');
        $this->db->join(db_prefix() . 'pos_locations l', 'l.id = t.location_id');
        $this->db->join(db_prefix() . 'staff s', 's.staffid = t.cashier_id');

        if (!empty($where)) {
            $this->db->where($where);
        }

        $this->db->order_by('t.created_at', 'DESC');

        return $this->db->get()->result_array();
    }

    /**
     * Get transaction by ID
     */
    public function get($id)
    {
        // Use COALESCE to handle both 'company' and 'firstname/lastname' for customer name
        $this->db->select('t.*, COALESCE(c.company, CONCAT(c.firstname, " ", c.lastname), "Walk-in Customer") as customer_name, l.name as location_name, s.firstname, s.lastname');
        $this->db->from(db_prefix() . 'pos_transactions t');
        $this->db->join(db_prefix() . 'clients c', 'c.userid = t.customer_id', 'left');
        $this->db->join(db_prefix() . 'pos_locations l', 'l.id = t.location_id');
        $this->db->join(db_prefix() . 'staff s', 's.staffid = t.cashier_id');
        $this->db->where('t.id', $id);

        $transaction = $this->db->get()->row_array();

        if ($transaction) {
            // Get transaction items
            $transaction['items'] = $this->get_transaction_items($id);
        }

        return $transaction;
    }

    /**
     * Get transaction items
     */
    public function get_transaction_items($transaction_id)
    {
        $this->db->select('ti.*, p.name as product_name, p.sku, pv.name as variant_name');
        $this->db->from(db_prefix() . 'pos_transaction_items ti');
        $this->db->join(db_prefix() . 'pos_products p', 'p.id = ti.product_id');
        $this->db->join(db_prefix() . 'pos_product_variants pv', 'pv.id = ti.variant_id', 'left');
        $this->db->where('ti.transaction_id', $transaction_id);
        $this->db->order_by('ti.id', 'ASC');
        
        return $this->db->get()->result_array();
    }

    /**
     * Create new transaction
     */
    public function create_transaction($data)
    {
        $this->db->trans_start();
        
        // Generate transaction number
        $transaction_number = $this->generate_transaction_number();
        
        // Get default location if not provided
        if (!isset($data['location_id'])) {
            $this->load->model('pos_inventory_model');
            $default_location = $this->pos_inventory_model->get_default_location();
            $data['location_id'] = $default_location['id'];
        }
        
        // Prepare transaction data
        $transaction_data = [
            'transaction_number' => $transaction_number,
            'customer_id' => isset($data['customer_id']) ? $data['customer_id'] : null,
            'location_id' => $data['location_id'],
            'transaction_type' => isset($data['transaction_type']) ? $data['transaction_type'] : 'sale',
            'payment_method' => $data['payment_method'],
            'payment_status' => isset($data['payment_status']) ? $data['payment_status'] : 'paid',
            'subtotal' => $data['subtotal'],
            'tax_amount' => isset($data['tax_amount']) ? $data['tax_amount'] : 0,
            'discount_amount' => isset($data['discount_amount']) ? $data['discount_amount'] : 0,
            'total' => $data['total'],
            'amount_paid' => isset($data['amount_paid']) ? $data['amount_paid'] : $data['total'],
            'change_amount' => isset($data['change_amount']) ? $data['change_amount'] : 0,
            'notes' => isset($data['notes']) ? $data['notes'] : '',
            'cashier_id' => get_staff_user_id()
        ];
        
        $this->db->insert(db_prefix() . 'pos_transactions', $transaction_data);
        $transaction_id = $this->db->insert_id();
        
        // Add transaction items
        if (isset($data['items']) && is_array($data['items'])) {
            foreach ($data['items'] as $item) {
                $this->add_transaction_item($transaction_id, $item);
                
                // Update inventory for sales
                if ($transaction_data['transaction_type'] == 'sale') {
                    $this->update_inventory_for_sale($item, $data['location_id']);
                }
            }
        }
        
        // Create invoice if customer is provided
        if ($data['customer_id'] && isset($data['create_invoice']) && $data['create_invoice']) {
            $invoice_id = $this->create_invoice_from_transaction($transaction_id);
            if ($invoice_id) {
                $this->db->where('id', $transaction_id);
                $this->db->update(db_prefix() . 'pos_transactions', ['invoice_id' => $invoice_id]);
            }
        }
        
        $this->db->trans_complete();
        
        if ($this->db->trans_status() === false) {
            return false;
        }
        
        return $transaction_id;
    }

    /**
     * Add transaction item
     */
    private function add_transaction_item($transaction_id, $item_data)
    {
        $data = [
            'transaction_id' => $transaction_id,
            'product_id' => $item_data['product_id'],
            'variant_id' => isset($item_data['variant_id']) ? $item_data['variant_id'] : null,
            'quantity' => $item_data['quantity'],
            'unit_price' => $item_data['unit_price'],
            'discount_amount' => isset($item_data['discount_amount']) ? $item_data['discount_amount'] : 0,
            'tax_amount' => isset($item_data['tax_amount']) ? $item_data['tax_amount'] : 0,
            'total_amount' => $item_data['total_amount'],
            'batch_number' => isset($item_data['batch_number']) ? $item_data['batch_number'] : null
        ];
        
        return $this->db->insert(db_prefix() . 'pos_transaction_items', $data);
    }

    /**
     * Update inventory for sale
     */
    private function update_inventory_for_sale($item, $location_id)
    {
        $this->load->model('pos_inventory_model');
        
        // Release reserved stock and reduce actual stock
        $this->pos_inventory_model->release_reserved_stock(
            $item['product_id'],
            $item['quantity'],
            $location_id,
            isset($item['variant_id']) ? $item['variant_id'] : null
        );
        
        // Record stock movement
        $movement_data = [
            'product_id' => $item['product_id'],
            'variant_id' => isset($item['variant_id']) ? $item['variant_id'] : null,
            'location_id' => $location_id,
            'movement_type' => 'out',
            'quantity' => -$item['quantity'],
            'reference_type' => 'sale',
            'reference_id' => $item['transaction_id'],
            'batch_number' => isset($item['batch_number']) ? $item['batch_number'] : null,
            'created_by' => get_staff_user_id()
        ];
        
        $this->db->insert(db_prefix() . 'pos_stock_movements', $movement_data);
    }

    /**
     * Generate transaction number
     */
    private function generate_transaction_number()
    {
        $prefix = 'TXN';
        $date = date('Ymd');
        
        // Get last transaction number for today
        $this->db->select('transaction_number');
        $this->db->from(db_prefix() . 'pos_transactions');
        $this->db->like('transaction_number', $prefix . $date, 'after');
        $this->db->order_by('id', 'DESC');
        $this->db->limit(1);
        
        $last_transaction = $this->db->get()->row();
        
        if ($last_transaction) {
            $last_number = (int)substr($last_transaction->transaction_number, -4);
            $new_number = $last_number + 1;
        } else {
            $new_number = 1;
        }
        
        return $prefix . $date . str_pad($new_number, 4, '0', STR_PAD_LEFT);
    }

    /**
     * Create invoice from transaction
     */
    private function create_invoice_from_transaction($transaction_id)
    {
        $transaction = $this->get($transaction_id);
        
        if (!$transaction || !$transaction['customer_id']) {
            return false;
        }
        
        $this->load->model('invoices_model');
        
        // Prepare invoice data
        $invoice_data = [
            'clientid' => $transaction['customer_id'],
            'number' => $this->invoices_model->get_next_invoice_number(),
            'date' => date('Y-m-d'),
            'duedate' => date('Y-m-d'),
            'subtotal' => $transaction['subtotal'],
            'total' => $transaction['total'],
            'adjustment' => -$transaction['discount_amount'],
            'addedfrom' => get_staff_user_id(),
            'status' => 2, // Sent
            'terms' => 'POS Transaction: ' . $transaction['transaction_number']
        ];
        
        $invoice_id = $this->invoices_model->add($invoice_data);
        
        if ($invoice_id) {
            // Add invoice items
            foreach ($transaction['items'] as $item) {
                $item_data = [
                    'description' => $item['product_name'] . ($item['variant_name'] ? ' - ' . $item['variant_name'] : ''),
                    'long_description' => '',
                    'qty' => $item['quantity'],
                    'rate' => $item['unit_price'],
                    'unit' => '',
                    'item_order' => 1
                ];
                
                $this->invoices_model->add_item($item_data, $invoice_id);
            }
        }
        
        return $invoice_id;
    }

    /**
     * Get recent transactions
     */
    public function get_recent_transactions($limit = 10)
    {
        // Use COALESCE to handle both 'company' and 'firstname/lastname' for customer name
        $this->db->select('t.*, COALESCE(c.company, CONCAT(c.firstname, " ", c.lastname), "Walk-in Customer") as customer_name');
        $this->db->from(db_prefix() . 'pos_transactions t');
        $this->db->join(db_prefix() . 'clients c', 'c.userid = t.customer_id', 'left');
        $this->db->order_by('t.created_at', 'DESC');
        $this->db->limit($limit);

        return $this->db->get()->result_array();
    }

    /**
     * Get today's sales
     */
    public function get_today_sales()
    {
        $this->db->select('SUM(total) as total_sales');
        $this->db->from(db_prefix() . 'pos_transactions');
        $this->db->where('transaction_type', 'sale');
        $this->db->where('DATE(created_at)', date('Y-m-d'));
        
        $result = $this->db->get()->row();
        return $result ? $result->total_sales : 0;
    }

    /**
     * Hold order
     */
    public function hold_order($data)
    {
        // Generate order number
        $order_number = $this->generate_held_order_number();
        
        $held_order_data = [
            'order_number' => $order_number,
            'customer_id' => isset($data['customer_id']) ? $data['customer_id'] : null,
            'location_id' => $data['location_id'],
            'order_data' => json_encode($data['cart']),
            'notes' => isset($data['notes']) ? $data['notes'] : '',
            'created_by' => get_staff_user_id()
        ];
        
        $this->db->insert(db_prefix() . 'pos_held_orders', $held_order_data);
        
        return $this->db->insert_id();
    }

    /**
     * Get held orders
     */
    public function get_held_orders()
    {
        $this->db->select('ho.*, c.company as customer_name');
        $this->db->from(db_prefix() . 'pos_held_orders ho');
        $this->db->join(db_prefix() . 'clients c', 'c.userid = ho.customer_id', 'left');
        $this->db->order_by('ho.created_at', 'DESC');
        
        return $this->db->get()->result_array();
    }

    /**
     * Retrieve held order
     */
    public function retrieve_held_order($id)
    {
        $this->db->where('id', $id);
        $held_order = $this->db->get(db_prefix() . 'pos_held_orders')->row_array();
        
        if ($held_order) {
            $held_order['cart'] = json_decode($held_order['order_data'], true);
            
            // Delete the held order
            $this->db->where('id', $id);
            $this->db->delete(db_prefix() . 'pos_held_orders');
        }
        
        return $held_order;
    }

    /**
     * Get held orders count
     */
    public function get_held_orders_count()
    {
        $this->db->select('COUNT(*) as count');
        $this->db->from(db_prefix() . 'pos_held_orders');
        $result = $this->db->get()->row();

        return $result ? (int)$result->count : 0;
    }

    /**
     * Get held order by ID
     */
    public function get_held_order($id)
    {
        $this->db->where('id', $id);
        return $this->db->get(db_prefix() . 'pos_held_orders')->row_array();
    }

    /**
     * Delete held order
     */
    public function delete_held_order($id)
    {
        $this->db->where('id', $id);
        return $this->db->delete(db_prefix() . 'pos_held_orders');
    }

    /**
     * Generate held order number
     */
    private function generate_held_order_number()
    {
        $prefix = 'HOLD';
        $date = date('Ymd');

        // Get last held order number for today
        $this->db->select('order_number');
        $this->db->from(db_prefix() . 'pos_held_orders');
        $this->db->like('order_number', $prefix . $date, 'after');
        $this->db->order_by('id', 'DESC');
        $this->db->limit(1);

        $last_order = $this->db->get()->row();

        if ($last_order) {
            $last_number = (int)substr($last_order->order_number, -4);
            $new_number = $last_number + 1;
        } else {
            $new_number = 1;
        }

        return $prefix . $date . str_pad($new_number, 4, '0', STR_PAD_LEFT);
    }
}
