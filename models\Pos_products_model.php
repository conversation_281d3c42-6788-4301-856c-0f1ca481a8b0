<?php

defined('BASEPATH') or exit('No direct script access allowed');

/**
 * POS Products Model
 */
class Pos_products_model extends App_Model
{
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Get all products
     */
    public function get_all($where = [])
    {
        $this->db->select('p.*, c.name as category_name, COALESCE(SUM(i.quantity), 0) as stock_quantity');
        $this->db->from(db_prefix() . 'pos_products p');
        $this->db->join(db_prefix() . 'pos_categories c', 'c.id = p.category_id', 'left');
        $this->db->join(db_prefix() . 'pos_inventory i', 'i.product_id = p.id', 'left');
        
        if (!empty($where)) {
            $this->db->where($where);
        }
        
        $this->db->group_by('p.id');
        $this->db->order_by('p.name', 'ASC');
        
        return $this->db->get()->result_array();
    }

    /**
     * Get active products for POS
     */
    public function get_active_products()
    {
        return $this->get_all(['p.status' => 1]);
    }

    /**
     * Get product by ID
     */
    public function get($id)
    {
        $this->db->select('p.*, c.name as category_name');
        $this->db->from(db_prefix() . 'pos_products p');
        $this->db->join(db_prefix() . 'pos_categories c', 'c.id = p.category_id', 'left');
        $this->db->where('p.id', $id);
        
        $product = $this->db->get()->row_array();
        
        if ($product) {
            // Get variants
            $product['variants'] = $this->get_product_variants($id);
            
            // Get inventory
            $this->load->model('pos_inventory_model');
            $product['inventory'] = $this->pos_inventory_model->get_inventory_by_product($id);
        }
        
        return $product;
    }

    /**
     * Get product by SKU
     */
    public function get_by_sku($sku)
    {
        $this->db->where('sku', $sku);
        $this->db->where('status', 1);
        return $this->db->get(db_prefix() . 'pos_products')->row_array();
    }

    /**
     * Get product by barcode
     */
    public function get_by_barcode($barcode)
    {
        // Check main product barcode
        $this->db->where('barcode', $barcode);
        $this->db->where('status', 1);
        $product = $this->db->get(db_prefix() . 'pos_products')->row_array();
        
        if ($product) {
            return $product;
        }
        
        // Check variant barcode
        $this->db->select('v.*, p.name as product_name, p.description, p.category_id, p.unit, p.track_inventory, p.status');
        $this->db->from(db_prefix() . 'pos_product_variants v');
        $this->db->join(db_prefix() . 'pos_products p', 'p.id = v.product_id');
        $this->db->where('v.barcode', $barcode);
        $this->db->where('v.status', 1);
        $this->db->where('p.status', 1);
        
        return $this->db->get()->row_array();
    }

    /**
     * Add new product
     */
    public function add($data)
    {
        $this->db->trans_start();
        
        // Prepare product data
        $product_data = [
            'name' => $data['name'],
            'description' => isset($data['description']) ? $data['description'] : '',
            'sku' => $data['sku'],
            'barcode' => isset($data['barcode']) ? $data['barcode'] : null,
            'category_id' => $this->validate_category_id($data['category_id'] ?? null),
            'unit' => isset($data['unit']) ? $data['unit'] : null,
            'cost_price' => isset($data['cost_price']) ? $data['cost_price'] : 0,
            'sale_price' => $data['sale_price'],
            'weight' => isset($data['weight']) ? $data['weight'] : null,
            'dimensions' => isset($data['dimensions']) ? $data['dimensions'] : null,
            'image' => isset($data['image']) ? $data['image'] : null,
            'track_inventory' => isset($data['track_inventory']) ? $data['track_inventory'] : 1,
            'allow_backorders' => isset($data['allow_backorders']) ? $data['allow_backorders'] : 0,
            'low_stock_threshold' => isset($data['low_stock_threshold']) ? $data['low_stock_threshold'] : 5,
            'status' => isset($data['status']) ? $data['status'] : 1,
            'created_by' => get_staff_user_id()
        ];
        
        $this->db->insert(db_prefix() . 'pos_products', $product_data);
        $product_id = $this->db->insert_id();
        
        // Add variants if provided
        if (isset($data['variants']) && is_array($data['variants'])) {
            foreach ($data['variants'] as $variant) {
                $this->add_product_variant($product_id, $variant);
            }
        }
        
        // Add initial inventory if provided
        if (isset($data['initial_stock']) && $data['initial_stock'] > 0) {
            $this->load->model('pos_inventory_model');
            $default_location = $this->pos_inventory_model->get_default_location();
            
            if ($default_location) {
                $this->pos_inventory_model->adjust_stock([
                    'product_id' => $product_id,
                    'location_id' => $default_location['id'],
                    'adjustment_type' => 'set',
                    'quantity' => $data['initial_stock'],
                    'reason' => 'Initial stock'
                ]);
            }
        }
        
        $this->db->trans_complete();
        
        if ($this->db->trans_status() === false) {
            return false;
        }
        
        return $product_id;
    }

    /**
     * Update product
     */
    public function update($data, $id)
    {
        $this->db->trans_start();
        
        // Prepare product data
        $product_data = [
            'name' => $data['name'],
            'description' => isset($data['description']) ? $data['description'] : '',
            'sku' => $data['sku'],
            'barcode' => isset($data['barcode']) ? $data['barcode'] : null,
            'category_id' => $this->validate_category_id($data['category_id'] ?? null),
            'unit' => isset($data['unit']) ? $data['unit'] : null,
            'cost_price' => isset($data['cost_price']) ? $data['cost_price'] : 0,
            'sale_price' => $data['sale_price'],
            'weight' => isset($data['weight']) ? $data['weight'] : null,
            'dimensions' => isset($data['dimensions']) ? $data['dimensions'] : null,
            'track_inventory' => isset($data['track_inventory']) ? $data['track_inventory'] : 1,
            'allow_backorders' => isset($data['allow_backorders']) ? $data['allow_backorders'] : 0,
            'low_stock_threshold' => isset($data['low_stock_threshold']) ? $data['low_stock_threshold'] : 5,
            'status' => isset($data['status']) ? $data['status'] : 1
        ];
        
        if (isset($data['image'])) {
            $product_data['image'] = $data['image'];
        }
        
        $this->db->where('id', $id);
        $this->db->update(db_prefix() . 'pos_products', $product_data);
        
        // Update variants if provided
        if (isset($data['variants']) && is_array($data['variants'])) {
            // Remove existing variants
            $this->db->where('product_id', $id);
            $this->db->delete(db_prefix() . 'pos_product_variants');
            
            // Add new variants
            foreach ($data['variants'] as $variant) {
                $this->add_product_variant($id, $variant);
            }
        }
        
        $this->db->trans_complete();
        
        return $this->db->trans_status();
    }

    /**
     * Delete product
     */
    public function delete($id)
    {
        $this->db->trans_start();
        
        // Delete variants
        $this->db->where('product_id', $id);
        $this->db->delete(db_prefix() . 'pos_product_variants');
        
        // Delete inventory records
        $this->db->where('product_id', $id);
        $this->db->delete(db_prefix() . 'pos_inventory');
        
        // Delete stock movements
        $this->db->where('product_id', $id);
        $this->db->delete(db_prefix() . 'pos_stock_movements');
        
        // Delete product
        $this->db->where('id', $id);
        $this->db->delete(db_prefix() . 'pos_products');
        
        $this->db->trans_complete();
        
        return $this->db->trans_status();
    }

    /**
     * Get product variants
     */
    public function get_product_variants($product_id)
    {
        $this->db->where('product_id', $product_id);
        $this->db->order_by('name', 'ASC');
        return $this->db->get(db_prefix() . 'pos_product_variants')->result_array();
    }

    /**
     * Add product variant
     */
    public function add_product_variant($product_id, $variant_data)
    {
        $data = [
            'product_id' => $product_id,
            'name' => $variant_data['name'],
            'sku' => $variant_data['sku'],
            'barcode' => isset($variant_data['barcode']) ? $variant_data['barcode'] : null,
            'cost_price' => isset($variant_data['cost_price']) ? $variant_data['cost_price'] : 0,
            'sale_price' => $variant_data['sale_price'],
            'weight' => isset($variant_data['weight']) ? $variant_data['weight'] : null,
            'image' => isset($variant_data['image']) ? $variant_data['image'] : null,
            'status' => isset($variant_data['status']) ? $variant_data['status'] : 1
        ];
        
        return $this->db->insert(db_prefix() . 'pos_product_variants', $data);
    }

    /**
     * Get categories
     */
    public function get_categories()
    {
        $this->db->where('status', 1);
        $this->db->order_by('sort_order', 'ASC');
        $this->db->order_by('name', 'ASC');
        return $this->db->get(db_prefix() . 'pos_categories')->result_array();
    }

    /**
     * Get total products count
     */
    public function get_total_products()
    {
        $this->db->where('status', 1);
        return $this->db->count_all_results(db_prefix() . 'pos_products');
    }

    /**
     * Get top selling products
     */
    public function get_top_selling_products($limit = 10)
    {
        $this->db->select('p.name, SUM(ti.quantity) as quantity_sold, SUM(ti.total_amount) as revenue');
        $this->db->from(db_prefix() . 'pos_products p');
        $this->db->join(db_prefix() . 'pos_transaction_items ti', 'ti.product_id = p.id');
        $this->db->join(db_prefix() . 'pos_transactions t', 't.id = ti.transaction_id');
        $this->db->where('t.transaction_type', 'sale');
        $this->db->where('p.status', 1);
        $this->db->group_by('p.id');
        $this->db->order_by('quantity_sold', 'DESC');
        $this->db->limit($limit);
        
        return $this->db->get()->result_array();
    }

    /**
     * Search products
     */
    public function search($term, $limit = 50)
    {
        $this->db->select('p.*, c.name as category_name, COALESCE(SUM(i.quantity), 0) as stock_quantity');
        $this->db->from(db_prefix() . 'pos_products p');
        $this->db->join(db_prefix() . 'pos_categories c', 'c.id = p.category_id', 'left');
        $this->db->join(db_prefix() . 'pos_inventory i', 'i.product_id = p.id', 'left');
        $this->db->where('p.status', 1);

        $this->db->group_start();
        $this->db->like('p.name', $term);
        $this->db->or_like('p.sku', $term);
        $this->db->or_like('p.barcode', $term);
        $this->db->or_like('p.description', $term);
        $this->db->group_end();

        $this->db->group_by('p.id');
        $this->db->order_by('p.name', 'ASC');
        $this->db->limit($limit);

        return $this->db->get()->result_array();
    }

    /**
     * Add category
     */
    public function add_category($data)
    {
        $category_data = [
            'name' => $data['name'],
            'description' => isset($data['description']) ? $data['description'] : '',
            'parent_id' => isset($data['parent_id']) && !empty($data['parent_id']) ? $data['parent_id'] : null,
            'sort_order' => isset($data['sort_order']) ? $data['sort_order'] : 0,
            'status' => isset($data['status']) ? $data['status'] : 1
        ];

        $this->db->insert(db_prefix() . 'pos_categories', $category_data);
        return $this->db->insert_id();
    }

    /**
     * Update category
     */
    public function update_category($data, $id)
    {
        $category_data = [
            'name' => $data['name'],
            'description' => isset($data['description']) ? $data['description'] : '',
            'parent_id' => isset($data['parent_id']) && !empty($data['parent_id']) ? $data['parent_id'] : null,
            'sort_order' => isset($data['sort_order']) ? $data['sort_order'] : 0,
            'status' => isset($data['status']) ? $data['status'] : 1
        ];

        $this->db->where('id', $id);
        return $this->db->update(db_prefix() . 'pos_categories', $category_data);
    }

    /**
     * Get categories with product count
     */
    public function get_categories_with_count()
    {
        $this->db->select('c.*, pc.name as parent_name, COUNT(p.id) as products_count');
        $this->db->from(db_prefix() . 'pos_categories c');
        $this->db->join(db_prefix() . 'pos_categories pc', 'pc.id = c.parent_id', 'left');
        $this->db->join(db_prefix() . 'pos_products p', 'p.category_id = c.id', 'left');
        $this->db->where('c.status', 1);
        $this->db->group_by('c.id');
        $this->db->order_by('c.sort_order', 'ASC');
        $this->db->order_by('c.name', 'ASC');

        return $this->db->get()->result_array();
    }

    /**
     * Delete category
     */
    public function delete_category($id)
    {
        // Check if category has products
        $this->db->where('category_id', $id);
        $products_count = $this->db->count_all_results(db_prefix() . 'pos_products');

        if ($products_count > 0) {
            return false; // Cannot delete category with products
        }

        // Check if category has subcategories
        $this->db->where('parent_id', $id);
        $subcategories_count = $this->db->count_all_results(db_prefix() . 'pos_categories');

        if ($subcategories_count > 0) {
            return false; // Cannot delete category with subcategories
        }

        $this->db->where('id', $id);
        return $this->db->delete(db_prefix() . 'pos_categories');
    }

    /**
     * Get category by ID
     */
    public function get_category($id)
    {
        $this->db->where('id', $id);
        return $this->db->get(db_prefix() . 'pos_categories')->row_array();
    }

    /**
     * Bulk update products
     */
    public function bulk_update($product_ids, $action, $data = [])
    {
        if (empty($product_ids)) {
            return false;
        }

        $this->db->trans_start();

        switch ($action) {
            case 'activate':
                $this->db->where_in('id', $product_ids);
                $this->db->update(db_prefix() . 'pos_products', ['status' => 1]);
                break;

            case 'deactivate':
                $this->db->where_in('id', $product_ids);
                $this->db->update(db_prefix() . 'pos_products', ['status' => 0]);
                break;

            case 'delete':
                foreach ($product_ids as $id) {
                    $this->delete($id);
                }
                break;

            case 'update_category':
                if (isset($data['category_id'])) {
                    $validated_category_id = $this->validate_category_id($data['category_id']);
                    $this->db->where_in('id', $product_ids);
                    $this->db->update(db_prefix() . 'pos_products', ['category_id' => $validated_category_id]);
                }
                break;

            case 'update_price':
                if (isset($data['price_type']) && isset($data['price_value']) && isset($data['price_operation'])) {
                    foreach ($product_ids as $id) {
                        $product = $this->get($id);
                        if ($product) {
                            $current_price = $product['sale_price'];
                            $new_price = $current_price;

                            if ($data['price_type'] == 'percentage') {
                                $change = ($current_price * $data['price_value']) / 100;
                            } else {
                                $change = $data['price_value'];
                            }

                            if ($data['price_operation'] == 'increase') {
                                $new_price = $current_price + $change;
                            } else {
                                $new_price = max(0, $current_price - $change);
                            }

                            $this->db->where('id', $id);
                            $this->db->update(db_prefix() . 'pos_products', ['sale_price' => $new_price]);
                        }
                    }
                }
                break;
        }

        $this->db->trans_complete();
        return $this->db->trans_status();
    }

    /**
     * Import products from CSV
     */
    public function import_from_csv($file_path, $update_existing = false)
    {
        if (!file_exists($file_path)) {
            return ['success' => false, 'message' => 'File not found'];
        }

        $handle = fopen($file_path, 'r');
        if (!$handle) {
            return ['success' => false, 'message' => 'Cannot open file'];
        }

        $this->db->trans_start();

        $imported = 0;
        $updated = 0;
        $errors = 0;
        $line = 0;

        // Skip header row
        fgetcsv($handle);

        while (($data = fgetcsv($handle)) !== false) {
            $line++;

            try {
                $product_data = [
                    'name' => $data[0],
                    'sku' => $data[1],
                    'description' => isset($data[2]) ? $data[2] : '',
                    'sale_price' => isset($data[3]) ? $data[3] : 0,
                    'cost_price' => isset($data[4]) ? $data[4] : 0,
                    'barcode' => isset($data[5]) ? $data[5] : null,
                    'category_id' => isset($data[6]) && !empty($data[6]) ? $this->get_category_id_by_name($data[6]) : null,
                    'unit' => isset($data[7]) ? $data[7] : null,
                    'weight' => isset($data[8]) ? $data[8] : null,
                    'low_stock_threshold' => isset($data[9]) ? $data[9] : 5,
                    'status' => isset($data[10]) ? ($data[10] == 'active' ? 1 : 0) : 1
                ];

                // Check if product exists
                $existing = $this->get_by_sku($product_data['sku']);

                if ($existing && $update_existing) {
                    $this->update($product_data, $existing['id']);
                    $updated++;
                } elseif (!$existing) {
                    $product_data['created_by'] = get_staff_user_id();
                    $this->add($product_data);
                    $imported++;
                }

            } catch (Exception $e) {
                $errors++;
            }
        }

        fclose($handle);
        $this->db->trans_complete();

        if ($this->db->trans_status()) {
            return [
                'success' => true,
                'message' => "Import completed. Imported: $imported, Updated: $updated, Errors: $errors"
            ];
        } else {
            return ['success' => false, 'message' => 'Import failed'];
        }
    }

    /**
     * Get category ID by name
     */
    private function get_category_id_by_name($name)
    {
        $this->db->select('id');
        $this->db->where('name', $name);
        $this->db->where('status', 1);
        $category = $this->db->get(db_prefix() . 'pos_categories')->row();

        return $category ? $category->id : null;
    }

    /**
     * Validate category ID to ensure it exists or return null
     * This prevents foreign key constraint violations
     */
    private function validate_category_id($category_id)
    {
        // If empty string or null, return null
        if (empty($category_id) || $category_id === '') {
            return null;
        }

        // Check if category exists
        $this->db->where('id', $category_id);
        $this->db->where('status', 1);
        $category = $this->db->get(db_prefix() . 'pos_categories')->row();

        // Return the ID if category exists, otherwise null
        return $category ? $category_id : null;
    }

    /**
     * Ensure at least one default category exists
     * Creates a default category if none exist
     */
    public function ensure_default_category()
    {
        // Check if any categories exist
        $count = $this->db->count_all_results(db_prefix() . 'pos_categories');

        if ($count == 0) {
            // Create default category
            $default_category = [
                'name' => 'General',
                'description' => 'Default category for products',
                'parent_id' => null,
                'status' => 1,
                'sort_order' => 1,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ];

            $this->db->insert(db_prefix() . 'pos_categories', $default_category);
            $category_id = $this->db->insert_id();

            log_message('info', 'Created default category with ID: ' . $category_id);
            return $category_id;
        }

        return null;
    }
}
