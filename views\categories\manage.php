<?php defined('BASEPATH') or exit('No direct script access allowed'); ?>
<?php init_head(); ?>
<div id="wrapper">
    <div class="content">
        <div class="row">
            <div class="col-md-12">
                <div class="panel_s">
                    <div class="panel-body">
                        <div class="row">
                            <div class="col-md-12">
                                <h4 class="no-margin">
                                    <?php echo $title; ?>
                                    <a href="#" class="btn btn-info pull-right" data-toggle="modal" data-target="#categoryModal">
                                        <i class="fa fa-plus"></i> <?php echo _l('new_category'); ?>
                                    </a>
                                </h4>
                                <hr class="hr-panel-heading" />
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-12">
                                <div class="table-responsive">
                                    <table class="table dt-table" id="categories-table">
                                        <thead>
                                            <tr>
                                                <th><?php echo _l('name'); ?></th>
                                                <th><?php echo _l('description'); ?></th>
                                                <th><?php echo _l('products_count'); ?></th>
                                                <th><?php echo _l('status'); ?></th>
                                                <th><?php echo _l('options'); ?></th>
                                            </tr>
                                        </thead>
                                        <tbody></tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Category Modal -->
<div class="modal fade" id="categoryModal" tabindex="-1" role="dialog">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
                <h4 class="modal-title"><?php echo _l('add_category'); ?></h4>
            </div>
            <form id="category-form">
                <div class="modal-body">
                    <input type="hidden" name="category_id" id="category-id">
                    <div class="form-group">
                        <label for="category-name"><?php echo _l('name'); ?> *</label>
                        <input type="text" class="form-control" id="category-name" name="name" required>
                    </div>
                    <div class="form-group">
                        <label for="category-description"><?php echo _l('description'); ?></label>
                        <textarea class="form-control" id="category-description" name="description" rows="3"></textarea>
                    </div>
                    <div class="form-group">
                        <label for="category-status"><?php echo _l('status'); ?></label>
                        <select class="form-control" id="category-status" name="status">
                            <option value="1"><?php echo _l('active'); ?></option>
                            <option value="0"><?php echo _l('inactive'); ?></option>
                        </select>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal"><?php echo _l('cancel'); ?></button>
                    <button type="submit" class="btn btn-info"><?php echo _l('save'); ?></button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
$(document).ready(function() {
    // Debug: Check if admin_url is defined
    console.log('Admin URL:', '<?php echo admin_url('pos_inventory/categories_table'); ?>');

    // Initialize DataTable with error handling
    var categoriesTable = $('#categories-table').DataTable({
        "processing": true,
        "serverSide": true,
        "ajax": {
            "url": "<?php echo admin_url('pos_inventory/categories_table'); ?>",
            "type": "POST",
            "error": function(xhr, error, thrown) {
                console.error('DataTables AJAX Error:', error, thrown);
                console.error('Response:', xhr.responseText);
                alert('Error loading categories data. Check console for details.');
            }
        },
        "columns": [
            {"data": "name"},
            {"data": "description"},
            {"data": "products_count"},
            {"data": "status"},
            {"data": "options", "orderable": false}
        ],
        "language": {
            "emptyTable": "No categories found. Click 'New Category' to add one.",
            "loadingRecords": "Loading categories...",
            "processing": "Processing..."
        }
    });

    // Handle form submission
    $('#category-form').on('submit', function(e) {
        e.preventDefault();
        var formData = $(this).serialize();

        console.log('Submitting form data:', formData);
        console.log('Submit URL:', '<?php echo admin_url('pos_inventory/manage_categories'); ?>');

        $.post('<?php echo admin_url('pos_inventory/manage_categories'); ?>', formData, function(response) {
            console.log('Form response:', response);
            if (response.success) {
                $('#categoryModal').modal('hide');
                categoriesTable.ajax.reload();
                alert_float('success', response.message);
            } else {
                alert_float('danger', response.message || 'Error saving category');
            }
        }, 'json').fail(function(xhr, status, error) {
            console.error('Form submission error:', error);
            console.error('Response:', xhr.responseText);
            alert_float('danger', 'Error submitting form. Check console for details.');
        });
    });

    // Reset form when modal is closed
    $('#categoryModal').on('hidden.bs.modal', function() {
        $('#category-form')[0].reset();
        $('#category-id').val('');
        $('.modal-title').text('<?php echo _l('add_category'); ?>');
    });
});

// Edit category function
function editCategory(id, name, description, status) {
    $('#category-id').val(id);
    $('#category-name').val(name);
    $('#category-description').val(description);
    $('#category-status').val(status);
    $('.modal-title').text('<?php echo _l('edit_category'); ?>');
    $('#categoryModal').modal('show');
}

// Delete category function
function deleteCategory(id) {
    if (confirm('<?php echo _l('confirm_delete'); ?>')) {
        $.post('<?php echo admin_url('pos_inventory/delete_category'); ?>', {id: id}, function(response) {
            if (response.success) {
                $('#categories-table').DataTable().ajax.reload();
                alert_float('success', response.message);
            } else {
                alert_float('danger', response.message);
            }
        }, 'json');
    }
}
</script>

<?php init_tail(); ?>
