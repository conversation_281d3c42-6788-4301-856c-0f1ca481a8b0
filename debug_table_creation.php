<?php

// Debug script to test table creation
define('BASEPATH', true);

// Include Perfex CRM bootstrap
require_once('../../index.php');

echo "<h2>POS Inventory - Table Creation Debug</h2>\n";

$CI = &get_instance();

// Load installation functions
require_once('install/install.php');

echo "<h3>Database Connection Test</h3>\n";
if ($CI->db->conn_id) {
    echo "✅ Database connection successful<br>\n";
    echo "Database: " . $CI->db->database . "<br>\n";
    echo "Charset: " . $CI->db->char_set . "<br>\n";
} else {
    echo "❌ Database connection failed<br>\n";
    exit;
}

echo "<h3>Testing Individual Table Creation</h3>\n";

// Test creating pos_categories table
echo "<h4>1. Creating pos_categories table</h4>\n";
try {
    if (!$CI->db->table_exists(db_prefix() . 'pos_categories')) {
        $sql = 'CREATE TABLE `' . db_prefix() . "pos_categories` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `name` varchar(191) NOT NULL,
            `description` text,
            `parent_id` int(11) DEFAULT NULL,
            `image` varchar(255) DEFAULT NULL,
            `status` tinyint(1) NOT NULL DEFAULT 1,
            `sort_order` int(11) DEFAULT 0,
            `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
            `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (`id`),
            KEY `parent_id` (`parent_id`),
            KEY `status` (`status`)
        ) ENGINE=InnoDB DEFAULT CHARSET=" . $CI->db->char_set;
        
        echo "Executing SQL:<br>\n";
        echo "<pre>" . htmlspecialchars($sql) . "</pre>\n";
        
        $result = $CI->db->query($sql);
        if ($result) {
            echo "✅ pos_categories table created successfully<br>\n";
        } else {
            echo "❌ Failed to create pos_categories table<br>\n";
            echo "Error: " . $CI->db->error()['message'] . "<br>\n";
        }
    } else {
        echo "ℹ️ pos_categories table already exists<br>\n";
    }
} catch (Exception $e) {
    echo "❌ Exception creating pos_categories: " . $e->getMessage() . "<br>\n";
}

// Test creating pos_products table
echo "<h4>2. Creating pos_products table</h4>\n";
try {
    if (!$CI->db->table_exists(db_prefix() . 'pos_products')) {
        $sql = 'CREATE TABLE `' . db_prefix() . "pos_products` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `name` varchar(191) NOT NULL,
            `description` text,
            `sku` varchar(100) NOT NULL,
            `barcode` varchar(100) DEFAULT NULL,
            `category_id` int(11) DEFAULT NULL,
            `unit` varchar(50) DEFAULT NULL,
            `cost_price` decimal(15,2) DEFAULT 0.00,
            `sale_price` decimal(15,2) NOT NULL DEFAULT 0.00,
            `weight` decimal(8,2) DEFAULT NULL,
            `dimensions` varchar(100) DEFAULT NULL,
            `image` varchar(255) DEFAULT NULL,
            `track_inventory` tinyint(1) NOT NULL DEFAULT 1,
            `allow_backorders` tinyint(1) NOT NULL DEFAULT 0,
            `low_stock_threshold` int(11) DEFAULT 5,
            `status` tinyint(1) NOT NULL DEFAULT 1,
            `woocommerce_id` int(11) DEFAULT NULL,
            `shopify_id` bigint(20) DEFAULT NULL,
            `created_by` int(11) NOT NULL,
            `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
            `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (`id`),
            UNIQUE KEY `sku` (`sku`),
            KEY `category_id` (`category_id`),
            KEY `barcode` (`barcode`),
            KEY `status` (`status`),
            KEY `woocommerce_id` (`woocommerce_id`),
            KEY `shopify_id` (`shopify_id`)
        ) ENGINE=InnoDB DEFAULT CHARSET=" . $CI->db->char_set;
        
        $result = $CI->db->query($sql);
        if ($result) {
            echo "✅ pos_products table created successfully<br>\n";
        } else {
            echo "❌ Failed to create pos_products table<br>\n";
            echo "Error: " . $CI->db->error()['message'] . "<br>\n";
        }
    } else {
        echo "ℹ️ pos_products table already exists<br>\n";
    }
} catch (Exception $e) {
    echo "❌ Exception creating pos_products: " . $e->getMessage() . "<br>\n";
}

echo "<h3>Testing Full Installation Function</h3>\n";
try {
    echo "Calling pos_inventory_install_database_tables()...<br>\n";
    pos_inventory_install_database_tables();
    echo "✅ Installation function completed<br>\n";
} catch (Exception $e) {
    echo "❌ Installation function failed: " . $e->getMessage() . "<br>\n";
}

echo "<h3>Checking Created Tables</h3>\n";
$required_tables = [
    'pos_categories',
    'pos_products',
    'pos_product_variants',
    'pos_locations',
    'pos_inventory',
    'pos_stock_movements',
    'pos_suppliers',
    'pos_purchase_orders',
    'pos_purchase_order_items',
    'pos_transactions',
    'pos_transaction_items',
    'pos_held_orders',
    'pos_integrations',
    'pos_sync_logs'
];

foreach ($required_tables as $table) {
    $exists = $CI->db->table_exists(db_prefix() . $table);
    $status = $exists ? '✅' : '❌';
    echo "$status " . db_prefix() . $table . "<br>\n";
}

?>
