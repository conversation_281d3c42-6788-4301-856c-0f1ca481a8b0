<?php
defined('BASEPATH') or exit('No direct script access allowed');

/**
 * Migration: Version 1.0.0 - Initial Setup
 * 
 * This migration creates the initial database structure
 * for the Advanced POS & Central Inventory module
 */

class Migration_Version_100 extends App_module_migration
{
    public function up()
    {
        $CI = &get_instance();
        
        // Create pos_categories table
        if (!$CI->db->table_exists(db_prefix() . 'pos_categories')) {
            $CI->db->query('CREATE TABLE `' . db_prefix() . "pos_categories` (
                `id` int(11) NOT NULL AUTO_INCREMENT,
                `name` varchar(191) NOT NULL,
                `description` text,
                `parent_id` int(11) DEFAULT NULL,
                `image` varchar(255) DEFAULT NULL,
                `status` tinyint(1) NOT NULL DEFAULT 1,
                `sort_order` int(11) DEFAULT 0,
                `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
                `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                PRIMARY KEY (`id`),
                <PERSON>EY `parent_id` (`parent_id`),
                KEY `status` (`status`)
            ) ENGINE=InnoDB DEFAULT CHARSET=" . $CI->db->char_set);
        }

        // Create pos_products table
        if (!$CI->db->table_exists(db_prefix() . 'pos_products')) {
            $CI->db->query('CREATE TABLE `' . db_prefix() . "pos_products` (
                `id` int(11) NOT NULL AUTO_INCREMENT,
                `name` varchar(191) NOT NULL,
                `description` text,
                `sku` varchar(100) NOT NULL,
                `barcode` varchar(100) DEFAULT NULL,
                `category_id` int(11) DEFAULT NULL,
                `unit` varchar(50) DEFAULT NULL,
                `cost_price` decimal(15,2) DEFAULT 0.00,
                `sale_price` decimal(15,2) NOT NULL DEFAULT 0.00,
                `weight` decimal(8,2) DEFAULT NULL,
                `dimensions` varchar(100) DEFAULT NULL,
                `image` varchar(255) DEFAULT NULL,
                `track_inventory` tinyint(1) NOT NULL DEFAULT 1,
                `allow_backorders` tinyint(1) NOT NULL DEFAULT 0,
                `low_stock_threshold` int(11) DEFAULT 5,
                `status` tinyint(1) NOT NULL DEFAULT 1,
                `created_by` int(11) NOT NULL,
                `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
                `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                PRIMARY KEY (`id`),
                UNIQUE KEY `sku` (`sku`),
                KEY `category_id` (`category_id`),
                KEY `barcode` (`barcode`),
                KEY `status` (`status`)
            ) ENGINE=InnoDB DEFAULT CHARSET=" . $CI->db->char_set);
        }

        // Create pos_product_variants table
        if (!$CI->db->table_exists(db_prefix() . 'pos_product_variants')) {
            $CI->db->query('CREATE TABLE `' . db_prefix() . "pos_product_variants` (
                `id` int(11) NOT NULL AUTO_INCREMENT,
                `product_id` int(11) NOT NULL,
                `name` varchar(191) NOT NULL,
                `sku` varchar(100) DEFAULT NULL,
                `barcode` varchar(100) DEFAULT NULL,
                `cost_price` decimal(15,2) DEFAULT NULL,
                `sale_price` decimal(15,2) DEFAULT NULL,
                `weight` decimal(8,2) DEFAULT NULL,
                `dimensions` varchar(100) DEFAULT NULL,
                `image` varchar(255) DEFAULT NULL,
                `status` tinyint(1) NOT NULL DEFAULT 1,
                `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
                `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                PRIMARY KEY (`id`),
                KEY `product_id` (`product_id`),
                KEY `sku` (`sku`),
                KEY `barcode` (`barcode`)
            ) ENGINE=InnoDB DEFAULT CHARSET=" . $CI->db->char_set);
        }

        // Create pos_locations table
        if (!$CI->db->table_exists(db_prefix() . 'pos_locations')) {
            $CI->db->query('CREATE TABLE `' . db_prefix() . "pos_locations` (
                `id` int(11) NOT NULL AUTO_INCREMENT,
                `name` varchar(191) NOT NULL,
                `address` text,
                `phone` varchar(50) DEFAULT NULL,
                `email` varchar(100) DEFAULT NULL,
                `manager_id` int(11) DEFAULT NULL,
                `is_default` tinyint(1) NOT NULL DEFAULT 0,
                `status` tinyint(1) NOT NULL DEFAULT 1,
                `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
                `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                PRIMARY KEY (`id`),
                KEY `manager_id` (`manager_id`),
                KEY `status` (`status`)
            ) ENGINE=InnoDB DEFAULT CHARSET=" . $CI->db->char_set);
        }

        // Create pos_inventory table
        if (!$CI->db->table_exists(db_prefix() . 'pos_inventory')) {
            $CI->db->query('CREATE TABLE `' . db_prefix() . "pos_inventory` (
                `id` int(11) NOT NULL AUTO_INCREMENT,
                `product_id` int(11) NOT NULL,
                `variant_id` int(11) DEFAULT NULL,
                `location_id` int(11) NOT NULL,
                `quantity` int(11) NOT NULL DEFAULT 0,
                `reserved_quantity` int(11) NOT NULL DEFAULT 0,
                `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                PRIMARY KEY (`id`),
                UNIQUE KEY `product_variant_location` (`product_id`, `variant_id`, `location_id`),
                KEY `location_id` (`location_id`),
                KEY `quantity` (`quantity`)
            ) ENGINE=InnoDB DEFAULT CHARSET=" . $CI->db->char_set);
        }

        // Create pos_stock_movements table
        if (!$CI->db->table_exists(db_prefix() . 'pos_stock_movements')) {
            $CI->db->query('CREATE TABLE `' . db_prefix() . "pos_stock_movements` (
                `id` int(11) NOT NULL AUTO_INCREMENT,
                `product_id` int(11) NOT NULL,
                `variant_id` int(11) DEFAULT NULL,
                `location_id` int(11) NOT NULL,
                `movement_type` enum('in','out','transfer','adjustment') NOT NULL,
                `quantity` int(11) NOT NULL,
                `unit_cost` decimal(15,2) DEFAULT NULL,
                `reference_type` varchar(50) DEFAULT NULL,
                `reference_id` int(11) DEFAULT NULL,
                `notes` text,
                `created_by` int(11) NOT NULL,
                `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
                PRIMARY KEY (`id`),
                KEY `product_id` (`product_id`),
                KEY `location_id` (`location_id`),
                KEY `movement_type` (`movement_type`),
                KEY `created_at` (`created_at`)
            ) ENGINE=InnoDB DEFAULT CHARSET=" . $CI->db->char_set);
        }

        // Create pos_suppliers table
        if (!$CI->db->table_exists(db_prefix() . 'pos_suppliers')) {
            $CI->db->query('CREATE TABLE `' . db_prefix() . "pos_suppliers` (
                `id` int(11) NOT NULL AUTO_INCREMENT,
                `name` varchar(191) NOT NULL,
                `contact_person` varchar(100) DEFAULT NULL,
                `email` varchar(100) DEFAULT NULL,
                `phone` varchar(50) DEFAULT NULL,
                `address` text,
                `city` varchar(100) DEFAULT NULL,
                `state` varchar(100) DEFAULT NULL,
                `country` varchar(100) DEFAULT NULL,
                `postal_code` varchar(20) DEFAULT NULL,
                `tax_number` varchar(50) DEFAULT NULL,
                `payment_terms` varchar(100) DEFAULT NULL,
                `notes` text,
                `status` tinyint(1) NOT NULL DEFAULT 1,
                `created_by` int(11) NOT NULL,
                `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
                `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                PRIMARY KEY (`id`),
                KEY `status` (`status`)
            ) ENGINE=InnoDB DEFAULT CHARSET=" . $CI->db->char_set);
        }

        return true;
    }

    public function down()
    {
        $CI = &get_instance();
        
        // Drop tables in reverse order to avoid foreign key constraints
        $tables = [
            'pos_suppliers',
            'pos_stock_movements',
            'pos_inventory',
            'pos_locations',
            'pos_product_variants',
            'pos_products',
            'pos_categories'
        ];

        foreach ($tables as $table) {
            if ($CI->db->table_exists(db_prefix() . $table)) {
                $CI->db->query('DROP TABLE `' . db_prefix() . $table . '`');
            }
        }

        return true;
    }
}
