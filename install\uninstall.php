<?php

defined('BASEPATH') or exit('No direct script access allowed');

/**
 * Advanced POS & Central Inventory Module Uninstallation
 *
 * This file handles the complete cleanup when the module is uninstalled
 * including database tables, options, files, and migration records
 */

$CI = &get_instance();

try {
    // Log uninstallation start
    log_message('info', 'Starting POS Inventory module uninstallation');

    // Disable foreign key checks to avoid constraint issues
    $CI->db->query('SET FOREIGN_KEY_CHECKS = 0');

    // Drop tables in reverse order to avoid foreign key constraints
    $tables = [
        'pos_sync_logs',
        'pos_woocommerce_orders',
        'pos_product_mappings',
        'pos_backorders',
        'pos_purchase_order_documents',
        'pos_integrations',
        'pos_held_orders',
        'pos_transaction_items',
        'pos_transactions',
        'pos_purchase_order_items',
        'pos_purchase_orders',
        'pos_suppliers',
        'pos_stock_movements',
        'pos_inventory',
        'pos_locations',
        'pos_product_variants',
        'pos_products',
        'pos_categories'
    ];

    $dropped_tables = [];
    foreach ($tables as $table) {
        $full_table_name = db_prefix() . $table;
        if ($CI->db->table_exists($full_table_name)) {
            try {
                $CI->db->query('DROP TABLE `' . $full_table_name . '`');
                $dropped_tables[] = $table;
                log_message('info', "Dropped table: $table");
            } catch (Exception $e) {
                log_message('error', "Failed to drop table $table: " . $e->getMessage());
            }
        }
    }

    // Re-enable foreign key checks
    $CI->db->query('SET FOREIGN_KEY_CHECKS = 1');

    // Remove module options
    $options = [
        'pos_inventory_enable_pos',
        'pos_inventory_enable_inventory_tracking',
        'pos_inventory_enable_multi_location',
        'pos_inventory_enable_barcode_scanner',
        'pos_inventory_default_payment_method',
        'pos_inventory_tax_inclusive',
        'pos_inventory_auto_print_receipt',
        'pos_inventory_email_receipt_to_customer',
        'pos_inventory_receipt_header',
        'pos_inventory_receipt_footer',
        'pos_inventory_default_location',
        'pos_inventory_low_stock_threshold',
        'pos_inventory_currency',
        'pos_inventory_tax_rate',
        'pos_inventory_receipt_template',
        'pos_inventory_backup_enabled',
        'pos_inventory_sync_frequency',
        'pos_inventory_api_timeout',
        'pos_inventory_cache_enabled',
        'pos_inventory_debug_mode'
    ];

    $removed_options = [];
    foreach ($options as $option) {
        try {
            $CI->db->where('name', $option);
            $affected = $CI->db->delete(db_prefix() . 'options');
            if ($affected > 0) {
                $removed_options[] = $option;
                log_message('info', "Removed option: $option");
            }
        } catch (Exception $e) {
            log_message('error', "Failed to remove option $option: " . $e->getMessage());
        }
    }

    // Remove migration records
    try {
        // Check if migrations table has 'group' column
        $migration_fields = $CI->db->field_data(db_prefix() . 'migrations');
        $has_group_column = false;
        foreach ($migration_fields as $field) {
            if ($field->name === 'group') {
                $has_group_column = true;
                break;
            }
        }

        if ($has_group_column) {
            $CI->db->where('`group`', 'pos_inventory');
        } else {
            // Fallback: remove by version numbers if group column doesn't exist
            $CI->db->where_in('version', [100, 101]);
        }
        $CI->db->delete(db_prefix() . 'migrations');
        log_message('info', 'Removed migration records');
    } catch (Exception $e) {
        log_message('error', 'Failed to remove migration records: ' . $e->getMessage());
    }

    // Remove uploaded files
    pos_inventory_cleanup_files();

    // Remove custom permissions
    pos_inventory_cleanup_permissions();

    // Remove dashboard widgets
    pos_inventory_cleanup_widgets();

    log_message('info', 'POS Inventory module uninstalled successfully');

    echo "Advanced POS & Central Inventory module uninstalled successfully!\n";
    echo "Tables dropped: " . count($dropped_tables) . "\n";
    echo "Options removed: " . count($removed_options) . "\n";

} catch (Exception $e) {
    log_message('error', 'POS Inventory module uninstallation failed: ' . $e->getMessage());
    echo "Error during uninstallation: " . $e->getMessage() . "\n";
}

/**
 * Clean up uploaded files
 */
function pos_inventory_cleanup_files()
{
    $CI = &get_instance();

    try {
        $upload_path = FCPATH . 'modules/pos_inventory/uploads/';

        if (is_dir($upload_path)) {
            // Remove uploaded files but keep directory structure for safety
            $directories = ['products', 'categories', 'suppliers', 'receipts'];

            foreach ($directories as $dir) {
                $dir_path = $upload_path . $dir . '/';
                if (is_dir($dir_path)) {
                    $files = glob($dir_path . '*');
                    foreach ($files as $file) {
                        if (is_file($file)) {
                            unlink($file);
                        }
                    }
                }
            }

            log_message('info', 'Cleaned up uploaded files');
        }
    } catch (Exception $e) {
        log_message('error', 'Failed to cleanup files: ' . $e->getMessage());
    }
}

/**
 * Clean up custom permissions
 */
function pos_inventory_cleanup_permissions()
{
    $CI = &get_instance();

    try {
        // Remove staff capabilities for POS permissions
        $permissions = [
            'pos_dashboard',
            'pos_interface',
            'pos_products',
            'pos_categories',
            'pos_inventory',
            'pos_suppliers',
            'pos_purchase_orders',
            'pos_transactions',
            'pos_integrations',
            'pos_reports'
        ];

        foreach ($permissions as $permission) {
            $CI->db->where('feature', $permission);
            $CI->db->delete(db_prefix() . 'staff_permissions');
        }

        log_message('info', 'Cleaned up permissions');
    } catch (Exception $e) {
        log_message('error', 'Failed to cleanup permissions: ' . $e->getMessage());
    }
}

/**
 * Clean up dashboard widgets
 */
function pos_inventory_cleanup_widgets()
{
    try {
        // Remove dashboard widget configurations if they exist
        $CI = &get_instance();

        $widgets = ['pos_stats', 'pos_inventory_alerts'];

        foreach ($widgets as $widget) {
            $CI->db->where('widget_name', $widget);
            $CI->db->delete(db_prefix() . 'dashboard_widgets');
        }

        log_message('info', 'Cleaned up dashboard widgets');
    } catch (Exception $e) {
        log_message('error', 'Failed to cleanup widgets: ' . $e->getMessage());
    }
}
