<?php

defined('BASEPATH') or exit('No direct script access allowed');

/**
 * Advanced POS & Central Inventory Module Configuration
 * 
 * This file contains module-specific configuration settings
 * and constants used throughout the module.
 */

// Module Information
define('POS_INVENTORY_MODULE_VERSION', '1.0.0');
define('POS_INVENTORY_MODULE_AUTHOR', 'Professional Developer');
define('POS_INVENTORY_MODULE_AUTHOR_URI', 'https://codecanyon.net/');

// Database Configuration
if (!defined('POS_INVENTORY_DB_VERSION')) {
    define('POS_INVENTORY_DB_VERSION', 100); // Database schema version
}
if (!defined('POS_INVENTORY_MIGRATION_GROUP')) {
    define('POS_INVENTORY_MIGRATION_GROUP', 'pos_inventory');
}

// File Upload Configuration
define('POS_INVENTORY_MAX_FILE_SIZE', 5 * 1024 * 1024); // 5MB
define('POS_INVENTORY_ALLOWED_IMAGE_TYPES', 'jpg|jpeg|png|gif|webp');
define('POS_INVENTORY_ALLOWED_DOCUMENT_TYPES', 'pdf|doc|docx|xls|xlsx|csv');

// POS Configuration
define('POS_INVENTORY_DEFAULT_CURRENCY', 'USD');
define('POS_INVENTORY_DEFAULT_TAX_RATE', 0.00);
define('POS_INVENTORY_DEFAULT_PAYMENT_METHOD', 'cash');
define('POS_INVENTORY_RECEIPT_TIMEOUT', 30); // seconds
define('POS_INVENTORY_SESSION_TIMEOUT', 3600); // 1 hour

// Inventory Configuration
define('POS_INVENTORY_DEFAULT_LOW_STOCK_THRESHOLD', 5);
define('POS_INVENTORY_ENABLE_BACKORDERS', true);
define('POS_INVENTORY_TRACK_SERIAL_NUMBERS', false);
define('POS_INVENTORY_AUTO_REORDER', false);

// Integration Configuration
define('POS_INVENTORY_SYNC_BATCH_SIZE', 50);
define('POS_INVENTORY_SYNC_TIMEOUT', 300); // 5 minutes
define('POS_INVENTORY_API_RATE_LIMIT', 100); // requests per minute
define('POS_INVENTORY_WEBHOOK_TIMEOUT', 30); // seconds

// Security Configuration
define('POS_INVENTORY_ENABLE_AUDIT_LOG', true);
define('POS_INVENTORY_SESSION_ENCRYPTION', true);
define('POS_INVENTORY_API_KEY_LENGTH', 32);
define('POS_INVENTORY_PASSWORD_MIN_LENGTH', 8);

// Performance Configuration
define('POS_INVENTORY_CACHE_ENABLED', true);
define('POS_INVENTORY_CACHE_TTL', 3600); // 1 hour
define('POS_INVENTORY_PAGINATION_LIMIT', 25);
define('POS_INVENTORY_SEARCH_LIMIT', 100);

// Notification Configuration
define('POS_INVENTORY_EMAIL_NOTIFICATIONS', true);
define('POS_INVENTORY_SMS_NOTIFICATIONS', false);
define('POS_INVENTORY_PUSH_NOTIFICATIONS', false);
define('POS_INVENTORY_LOW_STOCK_ALERTS', true);

// Reporting Configuration
define('POS_INVENTORY_REPORT_CACHE_TTL', 1800); // 30 minutes
define('POS_INVENTORY_EXPORT_BATCH_SIZE', 1000);
define('POS_INVENTORY_CHART_COLORS', [
    '#3498db', '#e74c3c', '#2ecc71', '#f39c12', 
    '#9b59b6', '#1abc9c', '#34495e', '#e67e22'
]);

// Module Feature Flags
$pos_inventory_features = [
    'multi_location' => true,
    'barcode_scanning' => true,
    'receipt_printing' => true,
    'customer_display' => false,
    'cash_drawer' => true,
    'loyalty_program' => false,
    'gift_cards' => false,
    'layaway' => false,
    'rental_tracking' => false,
    'service_orders' => false,
    'employee_timeclock' => false,
    'commission_tracking' => false,
    'price_levels' => true,
    'quantity_discounts' => true,
    'promotional_pricing' => true,
    'tax_exemptions' => true,
    'multi_currency' => false,
    'offline_mode' => false,
    'mobile_app' => false,
    'api_access' => true,
    'webhook_support' => true,
    'custom_fields' => true,
    'advanced_reporting' => true,
    'data_export' => true,
    'data_import' => true,
    'backup_restore' => false,
    'audit_trail' => true,
    'role_permissions' => true,
    'two_factor_auth' => false,
    'ip_restrictions' => false,
    'session_management' => true
];

// Store feature flags in a global variable for easy access
$GLOBALS['pos_inventory_features'] = $pos_inventory_features;

/**
 * Check if a feature is enabled
 * 
 * @param string $feature Feature name
 * @return bool True if enabled, false otherwise
 */
function pos_inventory_feature_enabled($feature)
{
    global $pos_inventory_features;
    return isset($pos_inventory_features[$feature]) && $pos_inventory_features[$feature] === true;
}

/**
 * Get module configuration value
 * 
 * @param string $key Configuration key
 * @param mixed $default Default value if key not found
 * @return mixed Configuration value
 */
function pos_inventory_config($key, $default = null)
{
    $config_map = [
        'version' => POS_INVENTORY_MODULE_VERSION,
        'author' => POS_INVENTORY_MODULE_AUTHOR,
        'author_uri' => POS_INVENTORY_MODULE_AUTHOR_URI,
        'db_version' => POS_INVENTORY_DB_VERSION,
        'migration_group' => POS_INVENTORY_MIGRATION_GROUP,
        'max_file_size' => POS_INVENTORY_MAX_FILE_SIZE,
        'allowed_image_types' => POS_INVENTORY_ALLOWED_IMAGE_TYPES,
        'allowed_document_types' => POS_INVENTORY_ALLOWED_DOCUMENT_TYPES,
        'default_currency' => POS_INVENTORY_DEFAULT_CURRENCY,
        'default_tax_rate' => POS_INVENTORY_DEFAULT_TAX_RATE,
        'default_payment_method' => POS_INVENTORY_DEFAULT_PAYMENT_METHOD,
        'receipt_timeout' => POS_INVENTORY_RECEIPT_TIMEOUT,
        'session_timeout' => POS_INVENTORY_SESSION_TIMEOUT,
        'default_low_stock_threshold' => POS_INVENTORY_DEFAULT_LOW_STOCK_THRESHOLD,
        'enable_backorders' => POS_INVENTORY_ENABLE_BACKORDERS,
        'track_serial_numbers' => POS_INVENTORY_TRACK_SERIAL_NUMBERS,
        'auto_reorder' => POS_INVENTORY_AUTO_REORDER,
        'sync_batch_size' => POS_INVENTORY_SYNC_BATCH_SIZE,
        'sync_timeout' => POS_INVENTORY_SYNC_TIMEOUT,
        'api_rate_limit' => POS_INVENTORY_API_RATE_LIMIT,
        'webhook_timeout' => POS_INVENTORY_WEBHOOK_TIMEOUT,
        'enable_audit_log' => POS_INVENTORY_ENABLE_AUDIT_LOG,
        'session_encryption' => POS_INVENTORY_SESSION_ENCRYPTION,
        'api_key_length' => POS_INVENTORY_API_KEY_LENGTH,
        'password_min_length' => POS_INVENTORY_PASSWORD_MIN_LENGTH,
        'cache_enabled' => POS_INVENTORY_CACHE_ENABLED,
        'cache_ttl' => POS_INVENTORY_CACHE_TTL,
        'pagination_limit' => POS_INVENTORY_PAGINATION_LIMIT,
        'search_limit' => POS_INVENTORY_SEARCH_LIMIT,
        'email_notifications' => POS_INVENTORY_EMAIL_NOTIFICATIONS,
        'sms_notifications' => POS_INVENTORY_SMS_NOTIFICATIONS,
        'push_notifications' => POS_INVENTORY_PUSH_NOTIFICATIONS,
        'low_stock_alerts' => POS_INVENTORY_LOW_STOCK_ALERTS,
        'report_cache_ttl' => POS_INVENTORY_REPORT_CACHE_TTL,
        'export_batch_size' => POS_INVENTORY_EXPORT_BATCH_SIZE,
        'chart_colors' => POS_INVENTORY_CHART_COLORS
    ];

    return isset($config_map[$key]) ? $config_map[$key] : $default;
}

/**
 * Get all module features
 * 
 * @return array Array of features and their status
 */
function pos_inventory_get_features()
{
    global $pos_inventory_features;
    return $pos_inventory_features;
}

/**
 * Enable a feature
 * 
 * @param string $feature Feature name
 * @return bool True if successful
 */
function pos_inventory_enable_feature($feature)
{
    global $pos_inventory_features;
    $pos_inventory_features[$feature] = true;
    return true;
}

/**
 * Disable a feature
 * 
 * @param string $feature Feature name
 * @return bool True if successful
 */
function pos_inventory_disable_feature($feature)
{
    global $pos_inventory_features;
    $pos_inventory_features[$feature] = false;
    return true;
}

// Module initialization
if (!defined('POS_INVENTORY_INITIALIZED')) {
    define('POS_INVENTORY_INITIALIZED', true);

    // Log module configuration loading using safe logging if available
    if (function_exists('pos_inventory_log_info')) {
        pos_inventory_log_info('POS Inventory module configuration loaded');
    } elseif (function_exists('log_message')) {
        // Fallback to standard logging with error handling
        try {
            log_message('info', '[POS_INVENTORY] Module configuration loaded');
        } catch (Exception $e) {
            // Ignore logging errors
        }
    }
}
