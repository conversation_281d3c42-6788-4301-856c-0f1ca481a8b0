<?php defined('BASEPATH') or exit('No direct script access allowed'); ?>
<?php init_head(); ?>
<div id="wrapper">
    <div class="content">
        <div class="row">
            <div class="col-md-12">
                <div class="panel_s">
                    <div class="panel-body">
                        <div class="row">
                            <div class="col-md-8">
                                <h4 class="no-margin">
                                    <i class="fa fa-<?php echo $platform == 'woocommerce' ? 'wordpress' : 'shopping-bag'; ?>"></i>
                                    <?php echo $title; ?>
                                </h4>
                            </div>
                            <div class="col-md-4 text-right">
                                <a href="<?php echo admin_url('pos_inventory/integrations'); ?>" class="btn btn-default">
                                    <i class="fa fa-arrow-left"></i> <?php echo _l('back_to_integrations'); ?>
                                </a>
                            </div>
                        </div>
                        <hr class="hr-panel-heading" />

                        <?php echo form_open(admin_url('pos_inventory/configure_integration/' . $platform), ['id' => 'integration-form']); ?>
                        
                        <div class="row">
                            <div class="col-md-8">
                                <!-- Basic Settings -->
                                <div class="panel panel-primary">
                                    <div class="panel-heading">
                                        <h4 class="panel-title"><?php echo _l('basic_settings'); ?></h4>
                                    </div>
                                    <div class="panel-body">
                                        <div class="form-group">
                                            <label for="name"><?php echo _l('integration_name'); ?> *</label>
                                            <input type="text" class="form-control" id="name" name="name" 
                                                   value="<?php echo isset($settings['name']) ? $settings['name'] : ucfirst($platform) . ' Store'; ?>" required>
                                            <small class="help-block"><?php echo _l('integration_name_help'); ?></small>
                                        </div>

                                        <div class="form-group">
                                            <label for="api_url"><?php echo _l('store_url'); ?> *</label>
                                            <input type="url" class="form-control" id="api_url" name="api_url" 
                                                   value="<?php echo isset($settings['api_url']) ? $settings['api_url'] : ''; ?>" 
                                                   placeholder="<?php echo $platform == 'woocommerce' ? 'https://yourstore.com' : 'https://yourstore.myshopify.com'; ?>" required>
                                            <small class="help-block">
                                                <?php echo $platform == 'woocommerce' ? _l('woocommerce_url_help') : _l('shopify_url_help'); ?>
                                            </small>
                                        </div>

                                        <?php if ($platform == 'woocommerce'): ?>
                                            <div class="form-group">
                                                <label for="api_key"><?php echo _l('consumer_key'); ?> *</label>
                                                <input type="text" class="form-control" id="api_key" name="api_key" 
                                                       value="<?php echo isset($settings['api_key']) ? $settings['api_key'] : ''; ?>" required>
                                                <small class="help-block"><?php echo _l('woocommerce_consumer_key_help'); ?></small>
                                            </div>

                                            <div class="form-group">
                                                <label for="api_secret"><?php echo _l('consumer_secret'); ?> *</label>
                                                <input type="password" class="form-control" id="api_secret" name="api_secret" 
                                                       value="<?php echo isset($settings['api_secret']) ? $settings['api_secret'] : ''; ?>" required>
                                                <small class="help-block"><?php echo _l('woocommerce_consumer_secret_help'); ?></small>
                                            </div>
                                        <?php else: ?>
                                            <div class="form-group">
                                                <label for="api_key"><?php echo _l('api_key'); ?> *</label>
                                                <input type="password" class="form-control" id="api_key" name="api_key" 
                                                       value="<?php echo isset($settings['api_key']) ? $settings['api_key'] : ''; ?>" required>
                                                <small class="help-block"><?php echo _l('shopify_api_key_help'); ?></small>
                                            </div>

                                            <div class="form-group">
                                                <label for="access_token"><?php echo _l('access_token'); ?> *</label>
                                                <input type="password" class="form-control" id="access_token" name="access_token" 
                                                       value="<?php echo isset($settings['access_token']) ? $settings['access_token'] : ''; ?>" required>
                                                <small class="help-block"><?php echo _l('shopify_access_token_help'); ?></small>
                                            </div>

                                            <div class="form-group">
                                                <label for="webhook_secret"><?php echo _l('webhook_secret'); ?></label>
                                                <input type="password" class="form-control" id="webhook_secret" name="webhook_secret" 
                                                       value="<?php echo isset($settings['webhook_secret']) ? $settings['webhook_secret'] : ''; ?>">
                                                <small class="help-block"><?php echo _l('shopify_webhook_secret_help'); ?></small>
                                            </div>
                                        <?php endif; ?>

                                        <div class="form-group">
                                            <label for="default_location_id"><?php echo _l('default_location'); ?></label>
                                            <select class="form-control selectpicker" id="default_location_id" name="default_location_id">
                                                <option value=""><?php echo _l('select_location'); ?></option>
                                                <?php foreach ($locations as $location): ?>
                                                    <option value="<?php echo $location['id']; ?>" 
                                                            <?php echo (isset($settings['default_location_id']) && $settings['default_location_id'] == $location['id']) ? 'selected' : ''; ?>>
                                                        <?php echo $location['name']; ?>
                                                    </option>
                                                <?php endforeach; ?>
                                            </select>
                                            <small class="help-block"><?php echo _l('default_location_help'); ?></small>
                                        </div>

                                        <div class="form-group">
                                            <div class="checkbox">
                                                <label>
                                                    <input type="checkbox" name="status" value="1" 
                                                           <?php echo (isset($settings['status']) && $settings['status']) ? 'checked' : ''; ?>>
                                                    <?php echo _l('enable_integration'); ?>
                                                </label>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Sync Settings -->
                                <div class="panel panel-info">
                                    <div class="panel-heading">
                                        <h4 class="panel-title"><?php echo _l('sync_settings'); ?></h4>
                                    </div>
                                    <div class="panel-body">
                                        <div class="row">
                                            <div class="col-md-6">
                                                <h5><?php echo _l('what_to_sync'); ?></h5>
                                                <div class="checkbox">
                                                    <label>
                                                        <input type="checkbox" name="sync_products" value="1" 
                                                               <?php echo (!isset($settings['sync_products']) || $settings['sync_products']) ? 'checked' : ''; ?>>
                                                        <?php echo _l('sync_products'); ?>
                                                    </label>
                                                </div>
                                                <div class="checkbox">
                                                    <label>
                                                        <input type="checkbox" name="sync_inventory" value="1" 
                                                               <?php echo (!isset($settings['sync_inventory']) || $settings['sync_inventory']) ? 'checked' : ''; ?>>
                                                        <?php echo _l('sync_inventory'); ?>
                                                    </label>
                                                </div>
                                                <div class="checkbox">
                                                    <label>
                                                        <input type="checkbox" name="sync_categories" value="1" 
                                                               <?php echo (!isset($settings['sync_categories']) || $settings['sync_categories']) ? 'checked' : ''; ?>>
                                                        <?php echo _l('sync_categories'); ?>
                                                    </label>
                                                </div>
                                                <div class="checkbox">
                                                    <label>
                                                        <input type="checkbox" name="sync_images" value="1" 
                                                               <?php echo (!isset($settings['sync_images']) || $settings['sync_images']) ? 'checked' : ''; ?>>
                                                        <?php echo _l('sync_images'); ?>
                                                    </label>
                                                </div>
                                                <div class="checkbox">
                                                    <label>
                                                        <input type="checkbox" name="sync_orders" value="1" 
                                                               <?php echo (isset($settings['sync_orders']) && $settings['sync_orders']) ? 'checked' : ''; ?>>
                                                        <?php echo _l('sync_orders'); ?>
                                                    </label>
                                                </div>
                                                <div class="checkbox">
                                                    <label>
                                                        <input type="checkbox" name="sync_customers" value="1" 
                                                               <?php echo (isset($settings['sync_customers']) && $settings['sync_customers']) ? 'checked' : ''; ?>>
                                                        <?php echo _l('sync_customers'); ?>
                                                    </label>
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <h5><?php echo _l('sync_direction'); ?></h5>
                                                <div class="form-group">
                                                    <label for="price_sync_direction"><?php echo _l('price_sync_direction'); ?></label>
                                                    <select class="form-control" id="price_sync_direction" name="price_sync_direction">
                                                        <option value="to_platform" <?php echo (isset($settings['price_sync_direction']) && $settings['price_sync_direction'] == 'to_platform') ? 'selected' : ''; ?>>
                                                            <?php echo _l('pos_to_platform'); ?>
                                                        </option>
                                                        <option value="from_platform" <?php echo (isset($settings['price_sync_direction']) && $settings['price_sync_direction'] == 'from_platform') ? 'selected' : ''; ?>>
                                                            <?php echo _l('platform_to_pos'); ?>
                                                        </option>
                                                        <option value="both" <?php echo (isset($settings['price_sync_direction']) && $settings['price_sync_direction'] == 'both') ? 'selected' : ''; ?>>
                                                            <?php echo _l('bidirectional'); ?>
                                                        </option>
                                                    </select>
                                                </div>

                                                <div class="form-group">
                                                    <label for="inventory_sync_direction"><?php echo _l('inventory_sync_direction'); ?></label>
                                                    <select class="form-control" id="inventory_sync_direction" name="inventory_sync_direction">
                                                        <option value="to_platform" <?php echo (isset($settings['inventory_sync_direction']) && $settings['inventory_sync_direction'] == 'to_platform') ? 'selected' : ''; ?>>
                                                            <?php echo _l('pos_to_platform'); ?>
                                                        </option>
                                                        <option value="from_platform" <?php echo (isset($settings['inventory_sync_direction']) && $settings['inventory_sync_direction'] == 'from_platform') ? 'selected' : ''; ?>>
                                                            <?php echo _l('platform_to_pos'); ?>
                                                        </option>
                                                        <option value="both" <?php echo (!isset($settings['inventory_sync_direction']) || $settings['inventory_sync_direction'] == 'both') ? 'selected' : ''; ?>>
                                                            <?php echo _l('bidirectional'); ?>
                                                        </option>
                                                    </select>
                                                </div>

                                                <div class="checkbox">
                                                    <label>
                                                        <input type="checkbox" name="two_way_sync" value="1" 
                                                               <?php echo (isset($settings['two_way_sync']) && $settings['two_way_sync']) ? 'checked' : ''; ?>>
                                                        <?php echo _l('enable_two_way_sync'); ?>
                                                    </label>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Automation Settings -->
                                <div class="panel panel-warning">
                                    <div class="panel-heading">
                                        <h4 class="panel-title"><?php echo _l('automation_settings'); ?></h4>
                                    </div>
                                    <div class="panel-body">
                                        <div class="checkbox">
                                            <label>
                                                <input type="checkbox" name="auto_sync" value="1" 
                                                       <?php echo (isset($settings['auto_sync']) && $settings['auto_sync']) ? 'checked' : ''; ?>>
                                                <?php echo _l('enable_auto_sync'); ?>
                                            </label>
                                        </div>

                                        <div class="form-group">
                                            <label for="sync_frequency"><?php echo _l('sync_frequency_minutes'); ?></label>
                                            <select class="form-control" id="sync_frequency" name="sync_frequency">
                                                <option value="15" <?php echo (isset($settings['sync_frequency']) && $settings['sync_frequency'] == 15) ? 'selected' : ''; ?>>15 <?php echo _l('minutes'); ?></option>
                                                <option value="30" <?php echo (isset($settings['sync_frequency']) && $settings['sync_frequency'] == 30) ? 'selected' : ''; ?>>30 <?php echo _l('minutes'); ?></option>
                                                <option value="60" <?php echo (!isset($settings['sync_frequency']) || $settings['sync_frequency'] == 60) ? 'selected' : ''; ?>>1 <?php echo _l('hour'); ?></option>
                                                <option value="120" <?php echo (isset($settings['sync_frequency']) && $settings['sync_frequency'] == 120) ? 'selected' : ''; ?>>2 <?php echo _l('hours'); ?></option>
                                                <option value="360" <?php echo (isset($settings['sync_frequency']) && $settings['sync_frequency'] == 360) ? 'selected' : ''; ?>>6 <?php echo _l('hours'); ?></option>
                                                <option value="720" <?php echo (isset($settings['sync_frequency']) && $settings['sync_frequency'] == 720) ? 'selected' : ''; ?>>12 <?php echo _l('hours'); ?></option>
                                                <option value="1440" <?php echo (isset($settings['sync_frequency']) && $settings['sync_frequency'] == 1440) ? 'selected' : ''; ?>>24 <?php echo _l('hours'); ?></option>
                                            </select>
                                        </div>

                                        <div class="checkbox">
                                            <label>
                                                <input type="checkbox" name="sync_on_product_update" value="1" 
                                                       <?php echo (isset($settings['sync_on_product_update']) && $settings['sync_on_product_update']) ? 'checked' : ''; ?>>
                                                <?php echo _l('sync_on_product_update'); ?>
                                            </label>
                                        </div>

                                        <div class="checkbox">
                                            <label>
                                                <input type="checkbox" name="sync_on_inventory_change" value="1" 
                                                       <?php echo (isset($settings['sync_on_inventory_change']) && $settings['sync_on_inventory_change']) ? 'checked' : ''; ?>>
                                                <?php echo _l('sync_on_inventory_change'); ?>
                                            </label>
                                        </div>
                                    </div>
                                </div>

                                <div class="form-group">
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fa fa-save"></i> <?php echo _l('save_settings'); ?>
                                    </button>
                                    <button type="button" class="btn btn-info" id="test-connection">
                                        <i class="fa fa-plug"></i> <?php echo _l('test_connection'); ?>
                                    </button>
                                    <a href="<?php echo admin_url('pos_inventory/integrations'); ?>" class="btn btn-default">
                                        <?php echo _l('cancel'); ?>
                                    </a>
                                </div>
                            </div>

                            <div class="col-md-4">
                                <!-- Help Panel -->
                                <div class="panel panel-default">
                                    <div class="panel-heading">
                                        <h4 class="panel-title">
                                            <i class="fa fa-question-circle"></i> <?php echo _l('setup_help'); ?>
                                        </h4>
                                    </div>
                                    <div class="panel-body">
                                        <?php if ($platform == 'woocommerce'): ?>
                                            <h5><?php echo _l('woocommerce_setup_steps'); ?></h5>
                                            <ol>
                                                <li><?php echo _l('woocommerce_step_1'); ?></li>
                                                <li><?php echo _l('woocommerce_step_2'); ?></li>
                                                <li><?php echo _l('woocommerce_step_3'); ?></li>
                                                <li><?php echo _l('woocommerce_step_4'); ?></li>
                                            </ol>
                                            <p><strong><?php echo _l('permissions_required'); ?>:</strong></p>
                                            <ul>
                                                <li>Read</li>
                                                <li>Write</li>
                                            </ul>
                                        <?php else: ?>
                                            <h5><?php echo _l('shopify_setup_steps'); ?></h5>
                                            <ol>
                                                <li><?php echo _l('shopify_step_1'); ?></li>
                                                <li><?php echo _l('shopify_step_2'); ?></li>
                                                <li><?php echo _l('shopify_step_3'); ?></li>
                                                <li><?php echo _l('shopify_step_4'); ?></li>
                                            </ol>
                                            <p><strong><?php echo _l('permissions_required'); ?>:</strong></p>
                                            <ul>
                                                <li>read_products</li>
                                                <li>write_products</li>
                                                <li>read_inventory</li>
                                                <li>write_inventory</li>
                                                <li>read_orders</li>
                                                <li>read_customers</li>
                                            </ul>
                                        <?php endif; ?>
                                    </div>
                                </div>

                                <!-- Status Panel -->
                                <?php if (isset($settings) && $settings): ?>
                                <div class="panel panel-success">
                                    <div class="panel-heading">
                                        <h4 class="panel-title">
                                            <i class="fa fa-info-circle"></i> <?php echo _l('integration_status'); ?>
                                        </h4>
                                    </div>
                                    <div class="panel-body">
                                        <p><strong><?php echo _l('status'); ?>:</strong> 
                                            <?php if ($settings['status']): ?>
                                                <span class="label label-success"><?php echo _l('active'); ?></span>
                                            <?php else: ?>
                                                <span class="label label-warning"><?php echo _l('inactive'); ?></span>
                                            <?php endif; ?>
                                        </p>
                                        <p><strong><?php echo _l('last_sync'); ?>:</strong> 
                                            <?php echo isset($settings['last_sync']) && $settings['last_sync'] ? _dt($settings['last_sync']) : _l('never'); ?>
                                        </p>
                                        <p><strong><?php echo _l('created_at'); ?>:</strong> 
                                            <?php echo _dt($settings['created_at']); ?>
                                        </p>
                                    </div>
                                </div>
                                <?php endif; ?>
                            </div>
                        </div>

                        <?php echo form_close(); ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
$(document).ready(function() {
    // Test connection
    $('#test-connection').click(function() {
        var formData = $('#integration-form').serialize();
        
        $(this).prop('disabled', true).html('<i class="fa fa-spinner fa-spin"></i> <?php echo _l('testing'); ?>...');
        
        $.post(admin_url + 'pos_inventory/test_integration_connection', formData + '&platform=<?php echo $platform; ?>', function(response) {
            $('#test-connection').prop('disabled', false).html('<i class="fa fa-plug"></i> <?php echo _l('test_connection'); ?>');
            
            if (response.success) {
                alert_float('success', response.message);
            } else {
                alert_float('danger', response.message);
            }
        }, 'json').fail(function() {
            $('#test-connection').prop('disabled', false).html('<i class="fa fa-plug"></i> <?php echo _l('test_connection'); ?>');
            alert_float('danger', '<?php echo _l('connection_test_failed'); ?>');
        });
    });
});
</script>

<?php init_tail(); ?>
