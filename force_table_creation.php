<?php

// Force table creation script
define('BASEPATH', true);

// Include Perfex CRM bootstrap
require_once('../../index.php');

echo "<h2>POS Inventory - Force Table Creation</h2>\n";

$CI = &get_instance();

echo "<h3>Database Information</h3>\n";
echo "Database: " . $CI->db->database . "<br>\n";
echo "Charset: " . $CI->db->char_set . "<br>\n";
echo "Prefix: " . db_prefix() . "<br>\n";

// Define all table creation SQL
$tables = [
    'pos_categories' => 'CREATE TABLE `' . db_prefix() . 'pos_categories` (
        `id` int(11) NOT NULL AUTO_INCREMENT,
        `name` varchar(191) NOT NULL,
        `description` text,
        `parent_id` int(11) DEFAULT NULL,
        `image` varchar(255) DEFAULT NULL,
        `status` tinyint(1) NOT NULL DEFAULT 1,
        `sort_order` int(11) DEFAULT 0,
        `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
        `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        PRIMARY KEY (`id`),
        KEY `parent_id` (`parent_id`),
        KEY `status` (`status`)
    ) ENGINE=InnoDB DEFAULT CHARSET=' . $CI->db->char_set,
    
    'pos_products' => 'CREATE TABLE `' . db_prefix() . 'pos_products` (
        `id` int(11) NOT NULL AUTO_INCREMENT,
        `name` varchar(191) NOT NULL,
        `description` text,
        `sku` varchar(100) NOT NULL,
        `barcode` varchar(100) DEFAULT NULL,
        `category_id` int(11) DEFAULT NULL,
        `unit` varchar(50) DEFAULT NULL,
        `cost_price` decimal(15,2) DEFAULT 0.00,
        `sale_price` decimal(15,2) NOT NULL DEFAULT 0.00,
        `weight` decimal(8,2) DEFAULT NULL,
        `dimensions` varchar(100) DEFAULT NULL,
        `image` varchar(255) DEFAULT NULL,
        `track_inventory` tinyint(1) NOT NULL DEFAULT 1,
        `allow_backorders` tinyint(1) NOT NULL DEFAULT 0,
        `low_stock_threshold` int(11) DEFAULT 5,
        `status` tinyint(1) NOT NULL DEFAULT 1,
        `woocommerce_id` int(11) DEFAULT NULL,
        `shopify_id` bigint(20) DEFAULT NULL,
        `created_by` int(11) NOT NULL DEFAULT 1,
        `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
        `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        PRIMARY KEY (`id`),
        UNIQUE KEY `sku` (`sku`),
        KEY `category_id` (`category_id`),
        KEY `barcode` (`barcode`),
        KEY `status` (`status`)
    ) ENGINE=InnoDB DEFAULT CHARSET=' . $CI->db->char_set,
    
    'pos_locations' => 'CREATE TABLE `' . db_prefix() . 'pos_locations` (
        `id` int(11) NOT NULL AUTO_INCREMENT,
        `name` varchar(191) NOT NULL,
        `address` text,
        `phone` varchar(50) DEFAULT NULL,
        `email` varchar(100) DEFAULT NULL,
        `manager_id` int(11) DEFAULT NULL,
        `is_default` tinyint(1) NOT NULL DEFAULT 0,
        `status` tinyint(1) NOT NULL DEFAULT 1,
        `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
        `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        PRIMARY KEY (`id`),
        KEY `status` (`status`)
    ) ENGINE=InnoDB DEFAULT CHARSET=' . $CI->db->char_set,
    
    'pos_inventory' => 'CREATE TABLE `' . db_prefix() . 'pos_inventory` (
        `id` int(11) NOT NULL AUTO_INCREMENT,
        `product_id` int(11) NOT NULL,
        `variant_id` int(11) DEFAULT NULL,
        `location_id` int(11) NOT NULL,
        `quantity` int(11) NOT NULL DEFAULT 0,
        `reserved_quantity` int(11) NOT NULL DEFAULT 0,
        `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        PRIMARY KEY (`id`),
        UNIQUE KEY `product_variant_location` (`product_id`, `variant_id`, `location_id`),
        KEY `location_id` (`location_id`),
        KEY `quantity` (`quantity`)
    ) ENGINE=InnoDB DEFAULT CHARSET=' . $CI->db->char_set
];

// Create basic tables first
echo "<h3>Creating Core Tables</h3>\n";
foreach ($tables as $table_name => $sql) {
    echo "<h4>Creating $table_name</h4>\n";
    
    // Check if table exists
    if ($CI->db->table_exists(db_prefix() . $table_name)) {
        echo "ℹ️ Table already exists<br>\n";
        continue;
    }
    
    try {
        $result = $CI->db->query($sql);
        if ($result) {
            echo "✅ Table created successfully<br>\n";
        } else {
            $error = $CI->db->error();
            echo "❌ Failed to create table<br>\n";
            echo "Error: " . $error['message'] . "<br>\n";
        }
    } catch (Exception $e) {
        echo "❌ Exception: " . $e->getMessage() . "<br>\n";
    }
}

// Create remaining tables using simplified SQL
echo "<h3>Creating Additional Tables</h3>\n";
$additional_tables = [
    'pos_product_variants',
    'pos_stock_movements', 
    'pos_suppliers',
    'pos_purchase_orders',
    'pos_purchase_order_items',
    'pos_transactions',
    'pos_transaction_items',
    'pos_held_orders',
    'pos_integrations',
    'pos_sync_logs'
];

foreach ($additional_tables as $table_name) {
    if (!$CI->db->table_exists(db_prefix() . $table_name)) {
        echo "⚠️ Table $table_name needs to be created manually<br>\n";
    } else {
        echo "✅ Table $table_name exists<br>\n";
    }
}

// Insert default location if tables were created
if ($CI->db->table_exists(db_prefix() . 'pos_locations')) {
    echo "<h3>Creating Default Location</h3>\n";
    $existing_location = $CI->db->get(db_prefix() . 'pos_locations')->row();
    if (!$existing_location) {
        $CI->db->insert(db_prefix() . 'pos_locations', [
            'name' => 'Main Store',
            'address' => 'Default Location',
            'is_default' => 1,
            'status' => 1
        ]);
        echo "✅ Default location created<br>\n";
    } else {
        echo "ℹ️ Location already exists<br>\n";
    }
}

echo "<h3>Final Status Check</h3>\n";
$required_tables = [
    'pos_categories', 'pos_products', 'pos_locations', 'pos_inventory'
];

$all_created = true;
foreach ($required_tables as $table) {
    $exists = $CI->db->table_exists(db_prefix() . $table);
    $status = $exists ? '✅' : '❌';
    echo "$status " . db_prefix() . $table . "<br>\n";
    if (!$exists) $all_created = false;
}

if ($all_created) {
    echo "<br>🎉 <strong>Core tables created successfully!</strong><br>\n";
    echo "<a href='../../admin/modules'>Try activating the module again</a><br>\n";
} else {
    echo "<br>⚠️ <strong>Some tables are missing. Check the errors above.</strong><br>\n";
}

?>
