# POS Inventory Module .htaccess Configuration
# This file provides security and routing configuration for the module

# Prevent direct access to sensitive files
<Files "*.php">
    Order Allow,<PERSON>y
    Allow from all
</Files>

# Block access to configuration files
<FilesMatch "\.(ini|log|conf|sql|bak|backup)$">
    Order Allow,Deny
    Deny from all
</FilesMatch>

# Block access to version control files
<FilesMatch "\.(git|svn|hg)">
    Order Allow,Deny
    Deny from all
</FilesMatch>

# Prevent access to sensitive directories
RedirectMatch 403 ^/modules/pos_inventory/(config|install|migrations|tests)/.*$

# Enable directory browsing protection
Options -Indexes

# Set default index file
DirectoryIndex index.php index.html

# Handle 404 errors by redirecting to main application
ErrorDocument 404 /index.php

# Security headers
<IfModule mod_headers.c>
    # Prevent clickjacking
    Header always append X-Frame-Options SAMEORIGIN
    
    # Prevent MIME type sniffing
    Header always set X-Content-Type-Options nosniff
    
    # Enable XSS protection
    Header always set X-XSS-Protection "1; mode=block"
    
    # Referrer policy
    Header always set Referrer-Policy "strict-origin-when-cross-origin"
</IfModule>

# Compression for better performance
<IfModule mod_deflate.c>
    AddOutputFilterByType DEFLATE text/plain
    AddOutputFilterByType DEFLATE text/html
    AddOutputFilterByType DEFLATE text/xml
    AddOutputFilterByType DEFLATE text/css
    AddOutputFilterByType DEFLATE application/xml
    AddOutputFilterByType DEFLATE application/xhtml+xml
    AddOutputFilterByType DEFLATE application/rss+xml
    AddOutputFilterByType DEFLATE application/javascript
    AddOutputFilterByType DEFLATE application/x-javascript
</IfModule>

# Cache control for static assets
<IfModule mod_expires.c>
    ExpiresActive On
    ExpiresByType text/css "access plus 1 month"
    ExpiresByType application/javascript "access plus 1 month"
    ExpiresByType image/png "access plus 1 month"
    ExpiresByType image/jpg "access plus 1 month"
    ExpiresByType image/jpeg "access plus 1 month"
    ExpiresByType image/gif "access plus 1 month"
    ExpiresByType image/ico "access plus 1 month"
    ExpiresByType image/icon "access plus 1 month"
    ExpiresByType text/x-icon "access plus 1 month"
    ExpiresByType application/x-icon "access plus 1 month"
</IfModule>

# URL Rewriting for clean URLs (if needed)
<IfModule mod_rewrite.c>
    RewriteEngine On
    
    # Redirect direct access to module directory to main application
    RewriteCond %{REQUEST_URI} ^/modules/pos_inventory/?$
    RewriteRule ^(.*)$ /admin/pos_inventory [R=302,L]
    
    # Handle API requests
    RewriteCond %{REQUEST_URI} ^/modules/pos_inventory/api/
    RewriteRule ^api/(.*)$ controllers/Api.php/$1 [L,QSA]
</IfModule>
