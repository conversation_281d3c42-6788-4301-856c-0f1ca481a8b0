<?php

defined('BASEPATH') or exit('No direct script access allowed');

/**
 * POS Inventory Model
 */
class Pos_inventory_model extends App_Model
{
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Get total stock value
     */
    public function get_total_stock_value()
    {
        $this->db->select('SUM(i.quantity * p.cost_price) as total_value');
        $this->db->from(db_prefix() . 'pos_inventory i');
        $this->db->join(db_prefix() . 'pos_products p', 'p.id = i.product_id');
        $this->db->where('p.status', 1);
        $query = $this->db->get();
        
        $result = $query->row();
        return $result ? $result->total_value : 0;
    }

    /**
     * Get low stock products count
     */
    public function get_low_stock_products_count()
    {
        $this->db->select('COUNT(*) as count');
        $this->db->from(db_prefix() . 'pos_products p');
        $this->db->join(db_prefix() . 'pos_inventory i', 'i.product_id = p.id', 'left');
        $this->db->where('p.status', 1);
        $this->db->where('p.track_inventory', 1);
        $this->db->group_start();
        $this->db->where('i.quantity <=', 'p.low_stock_threshold', false);
        $this->db->or_where('i.quantity IS NULL');
        $this->db->group_end();
        $query = $this->db->get();
        
        $result = $query->row();
        return $result ? $result->count : 0;
    }

    /**
     * Get low stock products
     */
    public function get_low_stock_products($limit = null)
    {
        $this->db->select('p.*, COALESCE(SUM(i.quantity), 0) as stock_quantity');
        $this->db->from(db_prefix() . 'pos_products p');
        $this->db->join(db_prefix() . 'pos_inventory i', 'i.product_id = p.id', 'left');
        $this->db->where('p.status', 1);
        $this->db->where('p.track_inventory', 1);
        $this->db->group_by('p.id');
        $this->db->having('stock_quantity <=', 'p.low_stock_threshold', false);
        $this->db->order_by('stock_quantity', 'ASC');
        
        if ($limit) {
            $this->db->limit($limit);
        }
        
        return $this->db->get()->result_array();
    }

    /**
     * Get locations
     */
    public function get_locations()
    {
        $this->db->where('status', 1);
        $this->db->order_by('is_default', 'DESC');
        $this->db->order_by('name', 'ASC');
        return $this->db->get(db_prefix() . 'pos_locations')->result_array();
    }

    /**
     * Get default location
     */
    public function get_default_location()
    {
        $this->db->where('is_default', 1);
        $this->db->where('status', 1);
        return $this->db->get(db_prefix() . 'pos_locations')->row_array();
    }

    /**
     * Get product stock by location
     */
    public function get_product_stock($product_id, $location_id = null, $variant_id = null)
    {
        $this->db->select('SUM(quantity) as total_quantity, SUM(reserved_quantity) as total_reserved');
        $this->db->from(db_prefix() . 'pos_inventory');
        $this->db->where('product_id', $product_id);
        
        if ($location_id) {
            $this->db->where('location_id', $location_id);
        }
        
        if ($variant_id) {
            $this->db->where('variant_id', $variant_id);
        }
        
        $query = $this->db->get();
        $result = $query->row();
        
        return [
            'quantity' => $result ? $result->total_quantity : 0,
            'reserved' => $result ? $result->total_reserved : 0,
            'available' => $result ? ($result->total_quantity - $result->total_reserved) : 0
        ];
    }

    /**
     * Adjust stock
     */
    public function adjust_stock($data)
    {
        $this->db->trans_start();
        
        $product_id = $data['product_id'];
        $location_id = $data['location_id'];
        $variant_id = isset($data['variant_id']) ? $data['variant_id'] : null;
        $adjustment_type = $data['adjustment_type'];
        $quantity = (int)$data['quantity'];
        $reason = $data['reason'];
        $notes = isset($data['notes']) ? $data['notes'] : '';
        $batch_number = isset($data['batch_number']) ? $data['batch_number'] : null;
        
        // Get current stock
        $current_stock = $this->get_product_stock($product_id, $location_id, $variant_id);
        
        // Calculate new quantity based on adjustment type
        switch ($adjustment_type) {
            case 'increase':
                $new_quantity = $current_stock['quantity'] + $quantity;
                $movement_quantity = $quantity;
                break;
            case 'decrease':
                $new_quantity = max(0, $current_stock['quantity'] - $quantity);
                $movement_quantity = -$quantity;
                break;
            case 'set':
                $new_quantity = $quantity;
                $movement_quantity = $quantity - $current_stock['quantity'];
                break;
            default:
                return false;
        }
        
        // Update or insert inventory record
        $inventory_data = [
            'product_id' => $product_id,
            'variant_id' => $variant_id,
            'location_id' => $location_id,
            'batch_number' => $batch_number
        ];
        
        $existing = $this->db->get_where(db_prefix() . 'pos_inventory', $inventory_data)->row();
        
        if ($existing) {
            $this->db->where('id', $existing->id);
            $this->db->update(db_prefix() . 'pos_inventory', ['quantity' => $new_quantity]);
        } else {
            $inventory_data['quantity'] = $new_quantity;
            $this->db->insert(db_prefix() . 'pos_inventory', $inventory_data);
        }
        
        // Record stock movement
        $movement_data = [
            'product_id' => $product_id,
            'variant_id' => $variant_id,
            'location_id' => $location_id,
            'movement_type' => 'adjustment',
            'quantity' => $movement_quantity,
            'reference_type' => 'adjustment',
            'notes' => $reason . ($notes ? ' - ' . $notes : ''),
            'batch_number' => $batch_number,
            'created_by' => get_staff_user_id()
        ];
        
        $this->db->insert(db_prefix() . 'pos_stock_movements', $movement_data);
        
        $this->db->trans_complete();
        
        return $this->db->trans_status();
    }

    /**
     * Transfer stock between locations
     */
    public function transfer_stock($data)
    {
        $this->db->trans_start();
        
        $product_id = $data['product_id'];
        $from_location = $data['from_location'];
        $to_location = $data['to_location'];
        $variant_id = isset($data['variant_id']) ? $data['variant_id'] : null;
        $quantity = (int)$data['quantity'];
        $notes = isset($data['notes']) ? $data['notes'] : '';
        $batch_number = isset($data['batch_number']) ? $data['batch_number'] : null;
        
        // Check if enough stock available at source location
        $source_stock = $this->get_product_stock($product_id, $from_location, $variant_id);
        if ($source_stock['available'] < $quantity) {
            return false;
        }
        
        // Decrease stock at source location
        $this->adjust_stock([
            'product_id' => $product_id,
            'location_id' => $from_location,
            'variant_id' => $variant_id,
            'adjustment_type' => 'decrease',
            'quantity' => $quantity,
            'reason' => 'Transfer out',
            'notes' => $notes,
            'batch_number' => $batch_number
        ]);
        
        // Increase stock at destination location
        $this->adjust_stock([
            'product_id' => $product_id,
            'location_id' => $to_location,
            'variant_id' => $variant_id,
            'adjustment_type' => 'increase',
            'quantity' => $quantity,
            'reason' => 'Transfer in',
            'notes' => $notes,
            'batch_number' => $batch_number
        ]);
        
        $this->db->trans_complete();
        
        return $this->db->trans_status();
    }

    /**
     * Get payment methods
     */
    public function get_payment_methods()
    {
        return [
            'cash' => _l('cash'),
            'card' => _l('card'),
            'bank_transfer' => _l('bank_transfer'),
            'wallet' => _l('wallet'),
            'custom' => _l('custom_payment')
        ];
    }

    /**
     * Reserve stock for transaction
     */
    public function reserve_stock($product_id, $quantity, $location_id = null, $variant_id = null)
    {
        if (!$location_id) {
            $default_location = $this->get_default_location();
            $location_id = $default_location['id'];
        }
        
        $this->db->where('product_id', $product_id);
        $this->db->where('location_id', $location_id);
        if ($variant_id) {
            $this->db->where('variant_id', $variant_id);
        }
        
        $inventory = $this->db->get(db_prefix() . 'pos_inventory')->row();
        
        if ($inventory && ($inventory->quantity - $inventory->reserved_quantity) >= $quantity) {
            $this->db->where('id', $inventory->id);
            $this->db->set('reserved_quantity', 'reserved_quantity + ' . $quantity, false);
            return $this->db->update(db_prefix() . 'pos_inventory');
        }
        
        return false;
    }

    /**
     * Release reserved stock
     */
    public function release_reserved_stock($product_id, $quantity, $location_id = null, $variant_id = null)
    {
        if (!$location_id) {
            $default_location = $this->get_default_location();
            $location_id = $default_location['id'];
        }
        
        $this->db->where('product_id', $product_id);
        $this->db->where('location_id', $location_id);
        if ($variant_id) {
            $this->db->where('variant_id', $variant_id);
        }
        
        $inventory = $this->db->get(db_prefix() . 'pos_inventory')->row();
        
        if ($inventory) {
            $new_reserved = max(0, $inventory->reserved_quantity - $quantity);
            $this->db->where('id', $inventory->id);
            $this->db->update(db_prefix() . 'pos_inventory', ['reserved_quantity' => $new_reserved]);
            return true;
        }
        
        return false;
    }

    /**
     * Reduce stock for sale
     */
    public function reduce_stock($product_id, $location_id, $quantity, $variant_id = null)
    {
        $this->db->trans_start();

        // Get current inventory record
        $this->db->where('product_id', $product_id);
        $this->db->where('location_id', $location_id);
        if ($variant_id) {
            $this->db->where('variant_id', $variant_id);
        }

        $inventory = $this->db->get(db_prefix() . 'pos_inventory')->row();

        if ($inventory) {
            $new_quantity = max(0, $inventory->quantity - $quantity);
            $new_reserved = max(0, $inventory->reserved_quantity - $quantity);

            $this->db->where('id', $inventory->id);
            $this->db->update(db_prefix() . 'pos_inventory', [
                'quantity' => $new_quantity,
                'reserved_quantity' => $new_reserved
            ]);

            // Record stock movement
            $movement_data = [
                'product_id' => $product_id,
                'variant_id' => $variant_id,
                'location_id' => $location_id,
                'movement_type' => 'out',
                'quantity' => -$quantity,
                'reference_type' => 'sale',
                'notes' => 'Stock reduced for sale',
                'created_by' => get_staff_user_id(),
                'created_at' => date('Y-m-d H:i:s')
            ];

            $this->db->insert(db_prefix() . 'pos_stock_movements', $movement_data);
        }

        $this->db->trans_complete();

        return $this->db->trans_status();
    }

    /**
     * Get inventory by product
     */
    public function get_inventory_by_product($product_id)
    {
        $this->db->select('i.*, l.name as location_name');
        $this->db->from(db_prefix() . 'pos_inventory i');
        $this->db->join(db_prefix() . 'pos_locations l', 'l.id = i.location_id');
        $this->db->where('i.product_id', $product_id);
        $this->db->order_by('l.name', 'ASC');

        return $this->db->get()->result_array();
    }
}
