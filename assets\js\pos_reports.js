// POS Reports & Analytics JavaScript
var Reports = {
    charts: {},
    currentFilters: {
        date_from: null,
        date_to: null,
        location_id: null
    },
    
    init: function() {
        this.bindEvents();
        this.initializeFilters();
        this.loadDashboardData();
    },
    
    bindEvents: function() {
        var self = this;
        
        // Date filter form
        $('#date-filter-form').submit(function(e) {
            e.preventDefault();
            self.updateFilters();
            self.loadDashboardData();
        });
        
        // Reset filters
        $('#reset-filters').click(function() {
            self.resetFilters();
        });
        
        // Refresh dashboard
        $('#refresh-dashboard').click(function() {
            self.loadDashboardData();
        });
        
        // Chart period buttons
        $('.chart-period').click(function() {
            $('.chart-period').removeClass('btn-primary').addClass('btn-default');
            $(this).removeClass('btn-default').addClass('btn-primary');
            
            var period = $(this).data('period');
            self.loadSalesTrend(period);
        });
        
        // Export buttons
        $('#export-pdf').click(function() {
            self.exportReport('pdf');
        });
        
        $('#export-excel').click(function() {
            self.exportReport('excel');
        });
        
        $('#export-csv').click(function() {
            self.exportReport('csv');
        });
    },
    
    initializeFilters: function() {
        this.currentFilters.date_from = $('#date_from').val();
        this.currentFilters.date_to = $('#date_to').val();
        this.currentFilters.location_id = $('#location_filter').val();
    },
    
    updateFilters: function() {
        this.currentFilters.date_from = $('#date_from').val();
        this.currentFilters.date_to = $('#date_to').val();
        this.currentFilters.location_id = $('#location_filter').val();
    },
    
    resetFilters: function() {
        $('#date_from').val(new Date().getFullYear() + '-' + String(new Date().getMonth() + 1).padStart(2, '0') + '-01');
        $('#date_to').val(new Date().toISOString().split('T')[0]);
        $('#location_filter').val('').trigger('change');
        $('.selectpicker').selectpicker('refresh');
        
        this.updateFilters();
        this.loadDashboardData();
    },
    
    loadDashboardData: function() {
        this.showLoading();
        this.loadKPIs();
        this.loadSalesTrend();
        this.loadPaymentMethodsChart();
        this.loadTopProductsChart();
        this.loadHourlySalesChart();
    },
    
    loadKPIs: function() {
        var self = this;
        
        $.get(admin_url + 'pos_inventory/get_dashboard_kpis', this.currentFilters, function(response) {
            if (response.success) {
                self.updateKPICards(response.kpis);
            }
        }, 'json');
    },
    
    updateKPICards: function(kpis) {
        // Total Sales
        $('#total-sales').html(currency_symbol + this.formatNumber(kpis.total_sales.value));
        this.updateKPIChange('#sales-change', '#sales-progress', kpis.total_sales.change);
        
        // Total Transactions
        $('#total-transactions').html(this.formatNumber(kpis.total_transactions.value));
        this.updateKPIChange('#transactions-change', '#transactions-progress', kpis.total_transactions.change);
        
        // Average Sale
        $('#average-sale').html(currency_symbol + this.formatNumber(kpis.average_sale.value));
        this.updateKPIChange('#average-change', '#average-progress', kpis.average_sale.change);
        
        // Items Sold
        $('#items-sold').html(this.formatNumber(kpis.items_sold.value));
        this.updateKPIChange('#items-change', '#items-progress', kpis.items_sold.change);
    },
    
    updateKPIChange: function(changeSelector, progressSelector, changeValue) {
        var changeText = '';
        var progressWidth = Math.min(Math.abs(changeValue), 100);
        
        if (changeValue > 0) {
            changeText = '<i class="fa fa-arrow-up text-success"></i> +' + changeValue + '%';
            $(progressSelector).removeClass('bg-danger').addClass('bg-success');
        } else if (changeValue < 0) {
            changeText = '<i class="fa fa-arrow-down text-danger"></i> ' + changeValue + '%';
            $(progressSelector).removeClass('bg-success').addClass('bg-danger');
        } else {
            changeText = '<i class="fa fa-minus text-muted"></i> 0%';
        }
        
        $(changeSelector).html(changeText);
        $(progressSelector).css('width', progressWidth + '%');
    },
    
    loadSalesTrend: function(period) {
        var self = this;
        period = period || 90;
        
        var params = Object.assign({}, this.currentFilters, {period: period});
        
        $.get(admin_url + 'pos_inventory/get_sales_trend', params, function(response) {
            if (response.success) {
                self.renderSalesTrendChart(response.data);
            }
        }, 'json');
    },
    
    renderSalesTrendChart: function(data) {
        var ctx = document.getElementById('sales-trend-chart').getContext('2d');
        
        if (this.charts.salesTrend) {
            this.charts.salesTrend.destroy();
        }
        
        this.charts.salesTrend = new Chart(ctx, {
            type: 'line',
            data: {
                labels: data.map(item => this.formatDate(item.date)),
                datasets: [{
                    label: 'Sales',
                    data: data.map(item => item.sales),
                    borderColor: '#007bff',
                    backgroundColor: 'rgba(0, 123, 255, 0.1)',
                    tension: 0.4,
                    fill: true
                }, {
                    label: 'Transactions',
                    data: data.map(item => item.transactions),
                    borderColor: '#28a745',
                    backgroundColor: 'rgba(40, 167, 69, 0.1)',
                    tension: 0.4,
                    yAxisID: 'y1'
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        type: 'linear',
                        display: true,
                        position: 'left',
                        title: {
                            display: true,
                            text: 'Sales (' + currency_symbol + ')'
                        }
                    },
                    y1: {
                        type: 'linear',
                        display: true,
                        position: 'right',
                        title: {
                            display: true,
                            text: 'Transactions'
                        },
                        grid: {
                            drawOnChartArea: false,
                        },
                    }
                },
                plugins: {
                    legend: {
                        display: true,
                        position: 'top'
                    },
                    tooltip: {
                        mode: 'index',
                        intersect: false,
                        callbacks: {
                            label: function(context) {
                                var label = context.dataset.label || '';
                                if (label) {
                                    label += ': ';
                                }
                                if (context.datasetIndex === 0) {
                                    label += currency_symbol + context.parsed.y.toFixed(2);
                                } else {
                                    label += context.parsed.y;
                                }
                                return label;
                            }
                        }
                    }
                }
            }
        });
    },
    
    loadPaymentMethodsChart: function() {
        var self = this;
        
        $.get(admin_url + 'pos_inventory/get_payment_methods_data', this.currentFilters, function(response) {
            if (response.success) {
                self.renderPaymentMethodsChart(response.data);
            }
        }, 'json');
    },
    
    renderPaymentMethodsChart: function(data) {
        var ctx = document.getElementById('payment-methods-chart').getContext('2d');
        
        if (this.charts.paymentMethods) {
            this.charts.paymentMethods.destroy();
        }
        
        var colors = ['#007bff', '#28a745', '#ffc107', '#dc3545', '#6f42c1'];
        
        this.charts.paymentMethods = new Chart(ctx, {
            type: 'doughnut',
            data: {
                labels: data.map(item => this.capitalizeFirst(item.payment_method)),
                datasets: [{
                    data: data.map(item => item.total),
                    backgroundColor: colors.slice(0, data.length),
                    borderWidth: 2,
                    borderColor: '#fff'
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom'
                    },
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                var total = context.dataset.data.reduce((a, b) => a + b, 0);
                                var percentage = ((context.parsed / total) * 100).toFixed(1);
                                return context.label + ': ' + currency_symbol + context.parsed.toFixed(2) + ' (' + percentage + '%)';
                            }
                        }
                    }
                }
            }
        });
    },
    
    loadTopProductsChart: function() {
        var self = this;
        
        $.get(admin_url + 'pos_inventory/get_top_products_data', this.currentFilters, function(response) {
            if (response.success) {
                self.renderTopProductsChart(response.data);
            }
        }, 'json');
    },
    
    renderTopProductsChart: function(data) {
        var ctx = document.getElementById('top-products-chart').getContext('2d');
        
        if (this.charts.topProducts) {
            this.charts.topProducts.destroy();
        }
        
        this.charts.topProducts = new Chart(ctx, {
            type: 'bar',
            data: {
                labels: data.map(item => item.product_name),
                datasets: [{
                    label: 'Quantity Sold',
                    data: data.map(item => item.quantity_sold),
                    backgroundColor: '#007bff',
                    borderColor: '#0056b3',
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                indexAxis: 'y',
                scales: {
                    x: {
                        beginAtZero: true,
                        title: {
                            display: true,
                            text: 'Quantity Sold'
                        }
                    }
                },
                plugins: {
                    legend: {
                        display: false
                    }
                }
            }
        });
    },
    
    loadHourlySalesChart: function() {
        var self = this;
        
        $.get(admin_url + 'pos_inventory/get_hourly_sales_data', this.currentFilters, function(response) {
            if (response.success) {
                self.renderHourlySalesChart(response.data);
            }
        }, 'json');
    },
    
    renderHourlySalesChart: function(data) {
        var ctx = document.getElementById('hourly-sales-chart').getContext('2d');
        
        if (this.charts.hourlySales) {
            this.charts.hourlySales.destroy();
        }
        
        this.charts.hourlySales = new Chart(ctx, {
            type: 'bar',
            data: {
                labels: data.map(item => item.hour + ':00'),
                datasets: [{
                    label: 'Sales',
                    data: data.map(item => item.sales),
                    backgroundColor: '#28a745',
                    borderColor: '#1e7e34',
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true,
                        title: {
                            display: true,
                            text: 'Sales (' + currency_symbol + ')'
                        }
                    },
                    x: {
                        title: {
                            display: true,
                            text: 'Hour of Day'
                        }
                    }
                },
                plugins: {
                    legend: {
                        display: false
                    },
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                return 'Sales: ' + currency_symbol + context.parsed.y.toFixed(2);
                            }
                        }
                    }
                }
            }
        });
    },
    
    exportReport: function(format) {
        var params = Object.assign({}, this.currentFilters, {format: format});
        var url = admin_url + 'pos_inventory/export_dashboard_report?' + $.param(params);
        window.open(url, '_blank');
    },
    
    showLoading: function() {
        $('#loading-overlay').show();
    },
    
    hideLoading: function() {
        $('#loading-overlay').hide();
    },
    
    formatNumber: function(num) {
        return parseFloat(num).toLocaleString('en-US', {
            minimumFractionDigits: 2,
            maximumFractionDigits: 2
        });
    },
    
    formatDate: function(dateStr) {
        var date = new Date(dateStr);
        return date.toLocaleDateString('en-US', {
            month: 'short',
            day: 'numeric'
        });
    },
    
    capitalizeFirst: function(str) {
        return str.charAt(0).toUpperCase() + str.slice(1).replace('_', ' ');
    }
};

// Initialize when document is ready
$(document).ready(function() {
    if (typeof Chart !== 'undefined') {
        Reports.init();
    }
});
