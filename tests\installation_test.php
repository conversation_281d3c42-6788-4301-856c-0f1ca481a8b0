<?php

defined('BASEPATH') or exit('No direct script access allowed');

/**
 * POS Inventory Module Installation Test
 * 
 * This script tests the complete installation and activation process
 * to ensure everything works correctly following Perfex CRM standards
 */

class POS_Inventory_Installation_Test
{
    private $CI;
    private $test_results = [];
    private $errors = [];

    public function __construct()
    {
        $this->CI = &get_instance();
    }

    /**
     * Run all installation tests
     */
    public function run_all_tests()
    {
        echo "<h2>POS Inventory Module Installation Test</h2>\n";
        echo "<p>Testing installation and activation process...</p>\n";

        $this->test_system_requirements();
        $this->test_file_structure();
        $this->test_database_tables();
        $this->test_permissions();
        $this->test_menu_items();
        $this->test_options();
        $this->test_migration_records();
        $this->test_version_management();
        $this->test_upload_directories();
        $this->test_language_files();

        $this->display_results();
    }

    /**
     * Test system requirements
     */
    private function test_system_requirements()
    {
        echo "<h3>Testing System Requirements</h3>\n";

        // Load version management
        if (file_exists(FCPATH . 'modules/pos_inventory/config/version.php')) {
            require_once(FCPATH . 'modules/pos_inventory/config/version.php');
        }

        if (function_exists('pos_inventory_check_requirements')) {
            $requirements = pos_inventory_check_requirements();
            $all_met = true;

            foreach ($requirements as $name => $req) {
                if ($req['met']) {
                    $this->test_results[] = "✅ $name: {$req['current']} (required: {$req['required']})";
                } else {
                    $this->errors[] = "❌ $name: {$req['current']} (required: {$req['required']})";
                    $all_met = false;
                }
            }

            if ($all_met) {
                $this->test_results[] = "✅ All system requirements met";
            }
        } else {
            $this->errors[] = "❌ Version management functions not loaded";
        }
    }

    /**
     * Test file structure
     */
    private function test_file_structure()
    {
        echo "<h3>Testing File Structure</h3>\n";

        $required_files = [
            'pos_inventory.php' => 'Main module file',
            'controllers/Pos_inventory.php' => 'Main controller',
            'models/Pos_inventory_model.php' => 'Main model',
            'install/install.php' => 'Installation script',
            'install/uninstall.php' => 'Uninstallation script',
            'config/module_config.php' => 'Module configuration',
            'config/version.php' => 'Version management',
            'helpers/pos_inventory_helper.php' => 'Helper functions',
            'language/english/pos_inventory_lang.php' => 'Language file',
            'migrations/100_version_100_initial_setup.php' => 'Initial migration',
            'migrations/101_version_101_pos_transactions.php' => 'Transactions migration'
        ];

        $base_path = FCPATH . 'modules/pos_inventory/';

        foreach ($required_files as $file => $description) {
            if (file_exists($base_path . $file)) {
                $this->test_results[] = "✅ $description: $file";
            } else {
                $this->errors[] = "❌ Missing $description: $file";
            }
        }

        $required_directories = [
            'controllers',
            'models',
            'views',
            'assets/css',
            'assets/js',
            'install',
            'config',
            'helpers',
            'language/english',
            'migrations',
            'uploads'
        ];

        foreach ($required_directories as $dir) {
            if (is_dir($base_path . $dir)) {
                $this->test_results[] = "✅ Directory exists: $dir";
            } else {
                $this->errors[] = "❌ Missing directory: $dir";
            }
        }
    }

    /**
     * Test database tables
     */
    private function test_database_tables()
    {
        echo "<h3>Testing Database Tables</h3>\n";

        $required_tables = [
            'pos_categories' => 'Product categories',
            'pos_products' => 'Products',
            'pos_product_variants' => 'Product variants',
            'pos_locations' => 'Store locations',
            'pos_inventory' => 'Inventory tracking',
            'pos_stock_movements' => 'Stock movements',
            'pos_suppliers' => 'Suppliers',
            'pos_purchase_orders' => 'Purchase orders',
            'pos_purchase_order_items' => 'Purchase order items',
            'pos_transactions' => 'POS transactions',
            'pos_transaction_items' => 'Transaction items',
            'pos_held_orders' => 'Held orders',
            'pos_integrations' => 'E-commerce integrations',
            'pos_sync_logs' => 'Sync logs'
        ];

        foreach ($required_tables as $table => $description) {
            $full_table_name = db_prefix() . $table;
            if ($this->CI->db->table_exists($full_table_name)) {
                $count = $this->CI->db->count_all($full_table_name);
                $this->test_results[] = "✅ $description table: $table ($count records)";
            } else {
                $this->errors[] = "❌ Missing table: $table ($description)";
            }
        }
    }

    /**
     * Test permissions
     */
    private function test_permissions()
    {
        echo "<h3>Testing Permissions</h3>\n";

        $required_permissions = [
            'pos_dashboard',
            'pos_interface',
            'pos_products',
            'pos_categories',
            'pos_inventory',
            'pos_suppliers',
            'pos_purchase_orders',
            'pos_transactions',
            'pos_integrations',
            'pos_reports'
        ];

        foreach ($required_permissions as $permission) {
            // Check if permission exists in staff_permissions table
            $exists = $this->CI->db->get_where(db_prefix() . 'staff_permissions', ['feature' => $permission])->num_rows() > 0;
            
            if ($exists || function_exists('has_permission')) {
                $this->test_results[] = "✅ Permission registered: $permission";
            } else {
                $this->errors[] = "❌ Permission not found: $permission";
            }
        }
    }

    /**
     * Test menu items
     */
    private function test_menu_items()
    {
        echo "<h3>Testing Menu Items</h3>\n";

        // Test if menu initialization function exists
        if (function_exists('pos_inventory_module_init_menu_items')) {
            $this->test_results[] = "✅ Menu initialization function exists";
        } else {
            $this->errors[] = "❌ Menu initialization function missing";
        }

        // Test if main module file is loaded
        if (defined('POS_INVENTORY_MODULE_NAME')) {
            $this->test_results[] = "✅ Module constants defined";
        } else {
            $this->errors[] = "❌ Module constants not defined";
        }
    }

    /**
     * Test options
     */
    private function test_options()
    {
        echo "<h3>Testing Module Options</h3>\n";

        $expected_options = [
            'pos_inventory_enable_pos',
            'pos_inventory_enable_inventory_tracking',
            'pos_inventory_enable_multi_location',
            'pos_inventory_default_payment_method',
            'pos_inventory_currency'
        ];

        foreach ($expected_options as $option) {
            $value = $this->CI->db->get_where(db_prefix() . 'options', ['name' => $option])->row();
            if ($value) {
                $this->test_results[] = "✅ Option exists: $option = {$value->value}";
            } else {
                $this->errors[] = "❌ Option missing: $option";
            }
        }
    }

    /**
     * Test migration records
     */
    private function test_migration_records()
    {
        echo "<h3>Testing Migration Records</h3>\n";

        // Check if migration records exist
        // First check if migrations table has 'group' column
        $migration_fields = $this->CI->db->field_data(db_prefix() . 'migrations');
        $has_group_column = false;
        foreach ($migration_fields as $field) {
            if ($field->name === 'group') {
                $has_group_column = true;
                break;
            }
        }

        if ($has_group_column) {
            $migrations = $this->CI->db->get_where(db_prefix() . 'migrations', ['`group`' => 'pos_inventory'])->result();
        } else {
            // Fallback: check by version numbers
            $this->CI->db->where_in('version', [100, 101]);
            $migrations = $this->CI->db->get(db_prefix() . 'migrations')->result();
        }

        if (!empty($migrations)) {
            foreach ($migrations as $migration) {
                $this->test_results[] = "✅ Migration record: version {$migration->version}";
            }
        } else {
            $this->errors[] = "❌ No migration records found";
        }

        // Check if latest version is recorded
        if (defined('VERSION_POS_INVENTORY')) {
            $latest_migration = $this->CI->db->get_where(db_prefix() . 'migrations', [
                'version' => VERSION_POS_INVENTORY,
                '`group`' => 'pos_inventory'
            ])->row();

            if ($latest_migration) {
                $this->test_results[] = "✅ Latest version migration exists: " . VERSION_POS_INVENTORY;
            } else {
                $this->errors[] = "❌ Latest version migration missing: " . VERSION_POS_INVENTORY;
            }
        }
    }

    /**
     * Test version management
     */
    private function test_version_management()
    {
        echo "<h3>Testing Version Management</h3>\n";

        if (function_exists('pos_inventory_get_version')) {
            $version = pos_inventory_get_version();
            $this->test_results[] = "✅ Current version: $version";
        } else {
            $this->errors[] = "❌ Version function not available";
        }

        if (function_exists('pos_inventory_get_installed_version')) {
            $installed = pos_inventory_get_installed_version();
            $this->test_results[] = "✅ Installed version: " . ($installed ?: 'Not detected');
        }

        if (function_exists('pos_inventory_requirements_met')) {
            $met = pos_inventory_requirements_met();
            if ($met) {
                $this->test_results[] = "✅ All requirements met";
            } else {
                $this->errors[] = "❌ Some requirements not met";
            }
        }
    }

    /**
     * Test upload directories
     */
    private function test_upload_directories()
    {
        echo "<h3>Testing Upload Directories</h3>\n";

        $upload_dirs = [
            'uploads/',
            'uploads/products/',
            'uploads/categories/',
            'uploads/suppliers/',
            'uploads/receipts/'
        ];

        $base_path = FCPATH . 'modules/pos_inventory/';

        foreach ($upload_dirs as $dir) {
            $full_path = $base_path . $dir;
            if (is_dir($full_path)) {
                $writable = is_writable($full_path);
                if ($writable) {
                    $this->test_results[] = "✅ Upload directory writable: $dir";
                } else {
                    $this->errors[] = "❌ Upload directory not writable: $dir";
                }

                // Check for index.html security file
                if (file_exists($full_path . 'index.html')) {
                    $this->test_results[] = "✅ Security file exists: {$dir}index.html";
                } else {
                    $this->errors[] = "❌ Security file missing: {$dir}index.html";
                }
            } else {
                $this->errors[] = "❌ Upload directory missing: $dir";
            }
        }
    }

    /**
     * Test language files
     */
    private function test_language_files()
    {
        echo "<h3>Testing Language Files</h3>\n";

        $lang_file = FCPATH . 'modules/pos_inventory/language/english/pos_inventory_lang.php';
        
        if (file_exists($lang_file)) {
            $this->test_results[] = "✅ Language file exists";
            
            // Test if language file is properly formatted
            $content = file_get_contents($lang_file);
            if (strpos($content, '$lang[') !== false) {
                $this->test_results[] = "✅ Language file properly formatted";
            } else {
                $this->errors[] = "❌ Language file format issue";
            }
        } else {
            $this->errors[] = "❌ Language file missing";
        }
    }

    /**
     * Display test results
     */
    private function display_results()
    {
        echo "<h2>Test Results Summary</h2>\n";
        
        echo "<h3>✅ Passed Tests (" . count($this->test_results) . ")</h3>\n";
        echo "<ul>\n";
        foreach ($this->test_results as $result) {
            echo "<li>$result</li>\n";
        }
        echo "</ul>\n";

        if (!empty($this->errors)) {
            echo "<h3>❌ Failed Tests (" . count($this->errors) . ")</h3>\n";
            echo "<ul style='color: red;'>\n";
            foreach ($this->errors as $error) {
                echo "<li>$error</li>\n";
            }
            echo "</ul>\n";
        }

        $total_tests = count($this->test_results) + count($this->errors);
        $success_rate = round((count($this->test_results) / $total_tests) * 100, 2);

        echo "<h3>Overall Result</h3>\n";
        echo "<p><strong>Success Rate: $success_rate% ($total_tests total tests)</strong></p>\n";

        if (empty($this->errors)) {
            echo "<p style='color: green; font-weight: bold;'>🎉 All tests passed! Module is ready for production.</p>\n";
        } else {
            echo "<p style='color: red; font-weight: bold;'>⚠️ Some tests failed. Please review and fix the issues above.</p>\n";
        }
    }
}

// Run the test if accessed directly
if (isset($_GET['run_test']) && $_GET['run_test'] == '1') {
    $test = new POS_Inventory_Installation_Test();
    $test->run_all_tests();
}
