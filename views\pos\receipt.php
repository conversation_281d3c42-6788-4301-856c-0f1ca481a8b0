<?php defined('BASEPATH') or exit('No direct script access allowed'); ?>
<?php init_head(); ?>
<div id="wrapper">
    <div class="content">
        <div class="row">
            <div class="col-md-8 col-md-offset-2">
                <div class="panel_s">
                    <div class="panel-body">
                        <div class="receipt-container" id="receipt-content">
                            <!-- Receipt Header -->
                            <div class="receipt-header text-center" style="margin-bottom: 30px;">
                                <h2 style="margin: 0; color: #333;">
                                    <?php echo $receipt_data['company']['name']; ?>
                                </h2>
                                <?php if (!empty($receipt_data['company']['address'])) { ?>
                                    <p style="margin: 5px 0; color: #666;">
                                        <?php echo nl2br($receipt_data['company']['address']); ?>
                                    </p>
                                <?php } ?>
                                <div style="margin: 10px 0;">
                                    <?php if (!empty($receipt_data['company']['phone'])) { ?>
                                        <span style="margin-right: 15px;">
                                            <i class="fa fa-phone"></i> <?php echo $receipt_data['company']['phone']; ?>
                                        </span>
                                    <?php } ?>
                                    <?php if (!empty($receipt_data['company']['email'])) { ?>
                                        <span>
                                            <i class="fa fa-envelope"></i> <?php echo $receipt_data['company']['email']; ?>
                                        </span>
                                    <?php } ?>
                                </div>
                                <hr style="border-top: 2px solid #333; margin: 20px 0;">
                                <h3 style="margin: 10px 0; color: #333;">RECEIPT</h3>
                            </div>

                            <!-- Transaction Details -->
                            <div class="receipt-details" style="margin-bottom: 30px;">
                                <div class="row">
                                    <div class="col-md-6">
                                        <strong>Transaction #:</strong> <?php echo $receipt_data['transaction']['transaction_number']; ?><br>
                                        <strong>Date:</strong> <?php echo date('M d, Y H:i', strtotime($receipt_data['transaction']['transaction_date'])); ?><br>
                                        <strong>Cashier:</strong> <?php echo $receipt_data['transaction']['firstname'] . ' ' . $receipt_data['transaction']['lastname']; ?><br>
                                        <?php if (!empty($receipt_data['transaction']['location_name'])) { ?>
                                            <strong>Location:</strong> <?php echo $receipt_data['transaction']['location_name']; ?><br>
                                        <?php } ?>
                                    </div>
                                    <div class="col-md-6 text-right">
                                        <?php if (!empty($receipt_data['transaction']['customer_name'])) { ?>
                                            <strong>Customer:</strong> <?php echo $receipt_data['transaction']['customer_name']; ?><br>
                                            <?php if (!empty($receipt_data['transaction']['customer_email'])) { ?>
                                                <strong>Email:</strong> <?php echo $receipt_data['transaction']['customer_email']; ?><br>
                                            <?php } ?>
                                        <?php } else { ?>
                                            <strong>Customer:</strong> Walk-in Customer<br>
                                        <?php } ?>
                                        <strong>Payment:</strong> <?php echo ucfirst($receipt_data['transaction']['payment_method']); ?>
                                    </div>
                                </div>
                            </div>

                            <!-- Items Table -->
                            <div class="receipt-items" style="margin-bottom: 30px;">
                                <table class="table table-bordered" style="margin-bottom: 0;">
                                    <thead style="background-color: #f8f9fa;">
                                        <tr>
                                            <th style="padding: 12px; border: 1px solid #dee2e6;">Item</th>
                                            <th style="padding: 12px; border: 1px solid #dee2e6; text-align: center;">Qty</th>
                                            <th style="padding: 12px; border: 1px solid #dee2e6; text-align: right;">Price</th>
                                            <th style="padding: 12px; border: 1px solid #dee2e6; text-align: right;">Total</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($receipt_data['items'] as $item) { ?>
                                        <tr>
                                            <td style="padding: 10px; border: 1px solid #dee2e6;">
                                                <strong><?php echo $item['product_name']; ?></strong>
                                                <?php if (!empty($item['sku'])) { ?>
                                                    <br><small class="text-muted">SKU: <?php echo $item['sku']; ?></small>
                                                <?php } ?>
                                            </td>
                                            <td style="padding: 10px; border: 1px solid #dee2e6; text-align: center;">
                                                <?php echo $item['quantity']; ?>
                                            </td>
                                            <td style="padding: 10px; border: 1px solid #dee2e6; text-align: right;">
                                                <?php echo app_format_money($item['unit_price'], get_base_currency()); ?>
                                            </td>
                                            <td style="padding: 10px; border: 1px solid #dee2e6; text-align: right;">
                                                <?php echo app_format_money($item['total_price'], get_base_currency()); ?>
                                            </td>
                                        </tr>
                                        <?php } ?>
                                    </tbody>
                                </table>
                            </div>

                            <!-- Totals -->
                            <div class="receipt-totals" style="margin-bottom: 30px;">
                                <div class="row">
                                    <div class="col-md-6 col-md-offset-6">
                                        <table class="table" style="margin-bottom: 0;">
                                            <tr>
                                                <td style="padding: 8px; border: none; text-align: right;"><strong>Subtotal:</strong></td>
                                                <td style="padding: 8px; border: none; text-align: right;">
                                                    <?php echo app_format_money($receipt_data['transaction']['subtotal'], get_base_currency()); ?>
                                                </td>
                                            </tr>
                                            <?php if ($receipt_data['transaction']['discount_amount'] > 0) { ?>
                                            <tr>
                                                <td style="padding: 8px; border: none; text-align: right;">
                                                    <strong>Discount (<?php echo $receipt_data['transaction']['discount_value']; ?><?php echo $receipt_data['transaction']['discount_type'] == 'percentage' ? '%' : ''; ?>):</strong>
                                                </td>
                                                <td style="padding: 8px; border: none; text-align: right; color: #28a745;">
                                                    -<?php echo app_format_money($receipt_data['transaction']['discount_amount'], get_base_currency()); ?>
                                                </td>
                                            </tr>
                                            <?php } ?>
                                            <?php if ($receipt_data['transaction']['tax_amount'] > 0) { ?>
                                            <tr>
                                                <td style="padding: 8px; border: none; text-align: right;">
                                                    <strong>Tax (<?php echo $receipt_data['transaction']['tax_rate']; ?>%):</strong>
                                                </td>
                                                <td style="padding: 8px; border: none; text-align: right;">
                                                    <?php echo app_format_money($receipt_data['transaction']['tax_amount'], get_base_currency()); ?>
                                                </td>
                                            </tr>
                                            <?php } ?>
                                            <tr style="border-top: 2px solid #333;">
                                                <td style="padding: 12px 8px; border: none; text-align: right; font-size: 18px;"><strong>TOTAL:</strong></td>
                                                <td style="padding: 12px 8px; border: none; text-align: right; font-size: 18px; font-weight: bold;">
                                                    <?php echo app_format_money($receipt_data['transaction']['total_amount'], get_base_currency()); ?>
                                                </td>
                                            </tr>
                                            <?php if ($receipt_data['transaction']['payment_method'] == 'cash') { ?>
                                            <tr>
                                                <td style="padding: 8px; border: none; text-align: right;"><strong>Amount Received:</strong></td>
                                                <td style="padding: 8px; border: none; text-align: right;">
                                                    <?php echo app_format_money($receipt_data['transaction']['amount_received'], get_base_currency()); ?>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td style="padding: 8px; border: none; text-align: right;"><strong>Change:</strong></td>
                                                <td style="padding: 8px; border: none; text-align: right;">
                                                    <?php echo app_format_money($receipt_data['transaction']['change_amount'], get_base_currency()); ?>
                                                </td>
                                            </tr>
                                            <?php } ?>
                                        </table>
                                    </div>
                                </div>
                            </div>

                            <!-- Footer -->
                            <div class="receipt-footer text-center" style="margin-top: 40px; padding-top: 20px; border-top: 1px dashed #ccc;">
                                <p style="margin: 10px 0; font-size: 14px;">
                                    <strong>Thank you for your business!</strong>
                                </p>
                                <?php if (!empty($receipt_data['company']['website'])) { ?>
                                    <p style="margin: 5px 0; color: #666;">
                                        Visit us at: <?php echo $receipt_data['company']['website']; ?>
                                    </p>
                                <?php } ?>
                                <p style="margin: 15px 0; font-size: 12px; color: #999;">
                                    This receipt was generated on <?php echo date('M d, Y H:i'); ?>
                                </p>
                                <?php if (!empty($receipt_data['transaction']['notes'])) { ?>
                                    <div style="margin-top: 20px; padding: 10px; background-color: #f8f9fa; border-radius: 4px;">
                                        <strong>Notes:</strong><br>
                                        <?php echo nl2br(htmlspecialchars($receipt_data['transaction']['notes'])); ?>
                                    </div>
                                <?php } ?>
                            </div>
                        </div>

                        <!-- Action Buttons -->
                        <div class="receipt-actions text-center" style="margin-top: 30px; padding-top: 20px; border-top: 1px solid #e9ecef;">
                            <button type="button" class="btn btn-primary" onclick="window.print()">
                                <i class="fa fa-print"></i> <?php echo _l('print_receipt'); ?>
                            </button>
                            <button type="button" class="btn btn-success" id="email-receipt-btn">
                                <i class="fa fa-envelope"></i> <?php echo _l('email_receipt'); ?>
                            </button>
                            <a href="<?php echo admin_url('pos_inventory/pos'); ?>" class="btn btn-default">
                                <i class="fa fa-arrow-left"></i> <?php echo _l('back_to_pos'); ?>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Email Receipt Modal -->
<div class="modal fade" id="emailReceiptModal" tabindex="-1" role="dialog">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
                <h4 class="modal-title"><?php echo _l('email_receipt'); ?></h4>
            </div>
            <form id="email-receipt-form">
                <div class="modal-body">
                    <div class="form-group">
                        <label for="receipt-email"><?php echo _l('email_address'); ?> <span class="text-danger">*</span></label>
                        <input type="email" class="form-control" id="receipt-email" name="email" 
                               value="<?php echo $receipt_data['transaction']['customer_email'] ?? ''; ?>" required>
                    </div>
                    <input type="hidden" name="transaction_id" value="<?php echo $receipt_data['transaction']['id']; ?>">
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal"><?php echo _l('cancel'); ?></button>
                    <button type="submit" class="btn btn-primary"><?php echo _l('send_email'); ?></button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
$(document).ready(function() {
    // Email receipt
    $('#email-receipt-btn').click(function() {
        $('#emailReceiptModal').modal('show');
    });
    
    $('#email-receipt-form').submit(function(e) {
        e.preventDefault();
        
        var formData = $(this).serialize();
        
        $.post(admin_url + 'pos_inventory/email_receipt', formData, function(response) {
            if (response.success) {
                alert_float('success', response.message);
                $('#emailReceiptModal').modal('hide');
            } else {
                alert_float('danger', response.message);
            }
        }, 'json');
    });
    
    // Print styles
    var printStyles = `
        <style>
            @media print {
                body * { visibility: hidden; }
                #receipt-content, #receipt-content * { visibility: visible; }
                #receipt-content { position: absolute; left: 0; top: 0; width: 100%; }
                .receipt-actions { display: none !important; }
                .panel_s { box-shadow: none !important; border: none !important; }
            }
        </style>
    `;
    $('head').append(printStyles);
});
</script>

<?php init_tail(); ?>
