<?php

// Test script to manually test activation
define('BASEPATH', true);

// Include Perfex CRM bootstrap
require_once('../../index.php');

// Load our module files
require_once('pos_inventory.php');

echo "<h2>POS Inventory Module - Activation Test</h2>\n";

echo "<h3>Testing Requirements:</h3>\n";
if (function_exists('pos_inventory_check_requirements')) {
    $requirements = pos_inventory_check_requirements();
    
    foreach ($requirements as $name => $req) {
        $status = $req['met'] ? '✅ PASS' : '❌ FAIL';
        echo "$status $name: {$req['current']} (required: {$req['required']})<br>\n";
    }
    
    echo "<br>\n";
    $all_met = pos_inventory_requirements_met();
    echo "All Requirements Met: " . ($all_met ? '✅ YES' : '❌ NO') . "<br>\n";
} else {
    echo "❌ Version management functions not loaded<br>\n";
}

echo "<h3>Testing Activation Hook:</h3>\n";
try {
    echo "Calling activation hook...<br>\n";
    pos_inventory_module_activation_hook();
    echo "✅ Activation completed successfully!<br>\n";
} catch (Exception $e) {
    echo "❌ Activation failed: " . $e->getMessage() . "<br>\n";
    echo "Error details: " . $e->getTraceAsString() . "<br>\n";
}

echo "<h3>Module Status Check:</h3>\n";
$CI = &get_instance();

// Check if module is in modules table
$module = $CI->db->get_where(db_prefix() . 'modules', ['module_name' => 'pos_inventory'])->row();
if (!$module) {
    $module = $CI->db->get_where(db_prefix() . 'modules', ['system_name' => 'pos_inventory'])->row();
}

if ($module) {
    echo "✅ Module found in database<br>\n";
    echo "Module status: " . ($module->active ? 'Active' : 'Inactive') . "<br>\n";
    if (isset($module->installed_version)) {
        echo "Installed version: " . $module->installed_version . "<br>\n";
    }
} else {
    echo "❌ Module not found in database<br>\n";
}

// Check if tables exist
echo "<h3>Database Tables Check:</h3>\n";
$required_tables = [
    'pos_categories',
    'pos_products', 
    'pos_locations',
    'pos_inventory',
    'pos_transactions'
];

foreach ($required_tables as $table) {
    $exists = $CI->db->table_exists(db_prefix() . $table);
    $status = $exists ? '✅' : '❌';
    echo "$status Table: " . db_prefix() . $table . "<br>\n";
}

?>
