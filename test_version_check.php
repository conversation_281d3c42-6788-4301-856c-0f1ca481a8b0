<?php

// Test script to check version detection
define('BASEPATH', true);

// Include Perfex CRM bootstrap
require_once('../../index.php');

// Load our version management
require_once('config/version.php');

echo "<h2>POS Inventory Module - Version Check Test</h2>\n";

echo "<h3>System Information:</h3>\n";
echo "PHP Version: " . PHP_VERSION . "<br>\n";

echo "<h3>Perfex CRM Version Detection:</h3>\n";
$perfex_version = pos_inventory_get_perfex_version();
echo "Detected Perfex Version: " . $perfex_version . "<br>\n";

echo "<h3>Requirements Check:</h3>\n";
if (function_exists('pos_inventory_check_requirements')) {
    $requirements = pos_inventory_check_requirements();
    
    foreach ($requirements as $name => $req) {
        $status = $req['met'] ? '✅ PASS' : '❌ FAIL';
        echo "$status $name: {$req['current']} (required: {$req['required']})<br>\n";
    }
    
    echo "<br>\n";
    $all_met = pos_inventory_requirements_met();
    echo "All Requirements Met: " . ($all_met ? '✅ YES' : '❌ NO') . "<br>\n";
} else {
    echo "❌ Version management functions not loaded<br>\n";
}

echo "<h3>Database Check:</h3>\n";
$CI = &get_instance();

// Check if migrations table exists
if ($CI->db->table_exists(db_prefix() . 'migrations')) {
    echo "✅ Migrations table exists<br>\n";
    
    // Get latest migration
    $migration = $CI->db->select('version')
                       ->from(db_prefix() . 'migrations')
                       ->where('`group`', '')
                       ->order_by('version', 'DESC')
                       ->limit(1)
                       ->get()
                       ->row();
    
    if ($migration) {
        echo "Latest migration version: " . $migration->version . "<br>\n";
    }
} else {
    echo "❌ Migrations table not found<br>\n";
}

// Check if get_option function works
if (function_exists('get_option')) {
    echo "✅ get_option function available<br>\n";
    $version_option = get_option('perfex_current_version');
    echo "get_option('perfex_current_version'): " . ($version_option ?: 'empty/null') . "<br>\n";
} else {
    echo "❌ get_option function not available<br>\n";
}

echo "<h3>Module Status:</h3>\n";
// Check if module is in modules table
$module = $CI->db->get_where(db_prefix() . 'modules', ['module_name' => 'pos_inventory'])->row();
if (!$module) {
    $module = $CI->db->get_where(db_prefix() . 'modules', ['system_name' => 'pos_inventory'])->row();
}

if ($module) {
    echo "✅ Module found in database<br>\n";
    echo "Module status: " . ($module->active ? 'Active' : 'Inactive') . "<br>\n";
    if (isset($module->installed_version)) {
        echo "Installed version: " . $module->installed_version . "<br>\n";
    }
} else {
    echo "❌ Module not found in database<br>\n";
}

?>
