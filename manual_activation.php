<?php

// Manual activation script
define('BASEPATH', true);

// Include Perfex CRM bootstrap
require_once('../../index.php');

echo "<h2>POS Inventory Module - Manual Activation</h2>\n";

// Load our module files
require_once('pos_inventory.php');

echo "<h3>System Information:</h3>\n";
echo "PHP Version: " . PHP_VERSION . "<br>\n";
echo "Current Time: " . date('Y-m-d H:i:s') . "<br>\n";

echo "<h3>Constants Check:</h3>\n";
if (defined('POS_INVENTORY_VERSION')) {
    echo "Module Version: " . POS_INVENTORY_VERSION . "<br>\n";
}
if (defined('POS_INVENTORY_MIN_PERFEX_VERSION')) {
    echo "Min Perfex Version: " . POS_INVENTORY_MIN_PERFEX_VERSION . "<br>\n";
}

echo "<h3>Attempting Manual Activation:</h3>\n";
try {
    echo "Starting activation process...<br>\n";
    
    // Call the activation hook directly
    pos_inventory_module_activation_hook();
    
    echo "✅ <strong>Activation completed successfully!</strong><br>\n";
    
    // Check if module is now active in database
    $CI = &get_instance();
    $module = $CI->db->get_where(db_prefix() . 'modules', ['module_name' => 'pos_inventory'])->row();
    if (!$module) {
        $module = $CI->db->get_where(db_prefix() . 'modules', ['system_name' => 'pos_inventory'])->row();
    }
    
    if ($module) {
        echo "Module found in database: " . ($module->active ? 'Active' : 'Inactive') . "<br>\n";
        
        // Try to activate it in the database if not already active
        if (!$module->active) {
            echo "Activating module in database...<br>\n";
            $where_field = isset($module->module_name) ? 'module_name' : 'system_name';
            $CI->db->where($where_field, 'pos_inventory');
            $CI->db->update(db_prefix() . 'modules', ['active' => 1]);
            echo "✅ Module activated in database<br>\n";
        }
    } else {
        echo "⚠️ Module not found in database - this might be normal for first installation<br>\n";
    }
    
} catch (Exception $e) {
    echo "❌ <strong>Activation failed:</strong> " . $e->getMessage() . "<br>\n";
    echo "<strong>Error details:</strong><br>\n";
    echo "<pre>" . $e->getTraceAsString() . "</pre>\n";
}

echo "<h3>Post-Activation Status:</h3>\n";

// Check database tables
$CI = &get_instance();
$tables_to_check = [
    'pos_categories',
    'pos_products',
    'pos_locations',
    'pos_inventory'
];

echo "<strong>Database Tables:</strong><br>\n";
foreach ($tables_to_check as $table) {
    $exists = $CI->db->table_exists(db_prefix() . $table);
    $status = $exists ? '✅' : '❌';
    echo "$status " . db_prefix() . $table . "<br>\n";
}

// Check module status
echo "<br><strong>Module Status:</strong><br>\n";
$module = $CI->db->get_where(db_prefix() . 'modules', ['module_name' => 'pos_inventory'])->row();
if (!$module) {
    $module = $CI->db->get_where(db_prefix() . 'modules', ['system_name' => 'pos_inventory'])->row();
}

if ($module) {
    echo "✅ Module found in database<br>\n";
    echo "Status: " . ($module->active ? 'Active' : 'Inactive') . "<br>\n";
    if (isset($module->installed_version)) {
        echo "Installed Version: " . $module->installed_version . "<br>\n";
    }
} else {
    echo "❌ Module not found in database<br>\n";
}

echo "<br><a href='../../admin/modules'>← Back to Modules</a><br>\n";

?>
