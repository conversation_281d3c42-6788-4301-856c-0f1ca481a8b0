<?php

defined('BASEPATH') or exit('No direct script access allowed');

// Get CodeIgniter instance
$CI = &get_instance();

$aColumns = [
    'i.id',
    'p.name',
    'p.sku',
    'l.name as location_name',
    'i.quantity',
    'i.reserved_quantity',
    '(i.quantity - i.reserved_quantity) as available_quantity',
    '(i.quantity * p.cost_price) as stock_value',
    'p.low_stock_threshold',
    'i.updated_at'
];

$sIndexColumn = 'i.id';
$sTable       = db_prefix() . 'pos_inventory i';

$join = [
    'JOIN ' . db_prefix() . 'pos_products p ON p.id = i.product_id',
    'JOIN ' . db_prefix() . 'pos_locations l ON l.id = i.location_id'
];

$where = ['AND p.status = 1'];

// Location filter
if ($CI->input->post('location') && $CI->input->post('location') != '') {
    $where[] = 'AND i.location_id = ' . $CI->db->escape_str($CI->input->post('location'));
}

// Category filter
if ($CI->input->post('category') && $CI->input->post('category') != '') {
    $where[] = 'AND p.category_id = ' . $CI->db->escape_str($CI->input->post('category'));
}

// Stock status filter
if ($CI->input->post('stock_status') && $CI->input->post('stock_status') != '') {
    $stock_filter = $CI->input->post('stock_status');
    switch ($stock_filter) {
        case 'in_stock':
            $where[] = 'AND i.quantity > p.low_stock_threshold';
            break;
        case 'low_stock':
            $where[] = 'AND i.quantity <= p.low_stock_threshold AND i.quantity > 0';
            break;
        case 'out_of_stock':
            $where[] = 'AND i.quantity = 0';
            break;
    }
}

$result = data_tables_init($aColumns, $sIndexColumn, $sTable, $join, $where, [
    'i.id',
    'i.product_id',
    'i.location_id',
    'i.quantity',
    'i.reserved_quantity',
    'i.batch_number',
    'i.expiry_date',
    'i.updated_at',
    'p.name',
    'p.sku',
    'p.cost_price',
    'p.sale_price',
    'p.low_stock_threshold',
    'p.track_inventory',
    'l.name as location_name'
]);

$output  = $result['output'];
$rResult = $result['rResult'];

foreach ($rResult as $aRow) {
    $row = [];

    // Checkbox for bulk actions
    $row[] = '<input type="checkbox" class="individual-checkbox" value="' . $aRow['id'] . '">';

    // Product name with image
    $product_name = '<strong>' . $aRow['name'] . '</strong>';
    if (!empty($aRow['batch_number'])) {
        $product_name .= '<br><small class="text-muted">Batch: ' . $aRow['batch_number'] . '</small>';
    }
    if (!empty($aRow['expiry_date'])) {
        $expiry_date = date('M d, Y', strtotime($aRow['expiry_date']));
        $days_to_expiry = (strtotime($aRow['expiry_date']) - time()) / (60 * 60 * 24);
        
        $expiry_class = 'text-muted';
        if ($days_to_expiry <= 0) {
            $expiry_class = 'text-danger';
        } elseif ($days_to_expiry <= 30) {
            $expiry_class = 'text-warning';
        }
        
        $product_name .= '<br><small class="' . $expiry_class . '">Expires: ' . $expiry_date . '</small>';
    }
    $row[] = $product_name;

    // SKU
    $row[] = '<code>' . $aRow['sku'] . '</code>';

    // Location
    $row[] = $aRow['location_name'];

    // Quantity
    $quantity = (int)$aRow['quantity'];
    $low_threshold = (int)$aRow['low_stock_threshold'];
    
    if ($quantity == 0) {
        $quantity_display = '<span class="label label-danger">' . $quantity . '</span>';
    } elseif ($quantity <= $low_threshold) {
        $quantity_display = '<span class="label label-warning">' . $quantity . '</span>';
    } else {
        $quantity_display = '<span class="label label-success">' . $quantity . '</span>';
    }
    $row[] = $quantity_display;

    // Reserved quantity
    $reserved = (int)$aRow['reserved_quantity'];
    if ($reserved > 0) {
        $row[] = '<span class="label label-info">' . $reserved . '</span>';
    } else {
        $row[] = '<span class="text-muted">0</span>';
    }

    // Available quantity
    $available = $quantity - $reserved;
    if ($available <= 0) {
        $available_display = '<span class="label label-danger">' . $available . '</span>';
    } elseif ($available <= $low_threshold) {
        $available_display = '<span class="label label-warning">' . $available . '</span>';
    } else {
        $available_display = '<span class="label label-success">' . $available . '</span>';
    }
    $row[] = $available_display;

    // Stock value
    $stock_value = $quantity * $aRow['cost_price'];
    $row[] = '<strong>' . app_format_money($stock_value, get_base_currency()) . '</strong>';

    // Status
    if ($quantity == 0) {
        $status = '<span class="label label-danger">' . _l('out_of_stock') . '</span>';
    } elseif ($quantity <= $low_threshold) {
        $status = '<span class="label label-warning">' . _l('low_stock') . '</span>';
    } else {
        $status = '<span class="label label-success">' . _l('in_stock') . '</span>';
    }
    $row[] = $status;

    // Last updated
    $row[] = '<small class="text-muted">' . time_ago($aRow['updated_at']) . '</small>';

    // Actions
    $options = '';
    
    if (has_permission('pos_inventory', '', 'edit')) {
        $options .= '<a href="#" class="btn btn-default btn-icon quick-adjust" 
                        data-product-id="' . $aRow['product_id'] . '" 
                        data-location-id="' . $aRow['location_id'] . '"
                        data-product-name="' . htmlspecialchars($aRow['name']) . '"
                        data-location-name="' . htmlspecialchars($aRow['location_name']) . '"
                        data-current-stock="' . $quantity . '"
                        data-toggle="tooltip" title="' . _l('quick_adjustment') . '">
                        <i class="fa fa-plus-minus"></i>
                    </a>';
        
        $options .= '<a href="' . admin_url('pos_inventory/stock_adjustment?product=' . $aRow['product_id'] . '&location=' . $aRow['location_id']) . '" 
                        class="btn btn-info btn-icon" data-toggle="tooltip" title="' . _l('stock_adjustment') . '">
                        <i class="fa fa-edit"></i>
                    </a>';
        
        $options .= '<a href="' . admin_url('pos_inventory/stock_transfer?product=' . $aRow['product_id'] . '&from=' . $aRow['location_id']) . '" 
                        class="btn btn-success btn-icon" data-toggle="tooltip" title="' . _l('stock_transfer') . '">
                        <i class="fa fa-exchange"></i>
                    </a>';
    }
    
    if (has_permission('pos_inventory', '', 'view')) {
        $options .= '<a href="#" class="btn btn-default btn-icon view-movements" 
                        data-product-id="' . $aRow['product_id'] . '" 
                        data-location-id="' . $aRow['location_id'] . '"
                        data-toggle="tooltip" title="' . _l('view_movements') . '">
                        <i class="fa fa-history"></i>
                    </a>';
    }

    $row[] = $options;

    $output['aaData'][] = $row;
}

// Add summary data to output
$CI = &get_instance();

// Get summary statistics
$CI->db->select('
    COUNT(DISTINCT p.id) as total_products,
    SUM(i.quantity * p.cost_price) as total_value,
    SUM(CASE WHEN i.quantity <= p.low_stock_threshold AND i.quantity > 0 THEN 1 ELSE 0 END) as low_stock_count,
    SUM(CASE WHEN i.quantity = 0 THEN 1 ELSE 0 END) as out_of_stock_count
');
$CI->db->from(db_prefix() . 'pos_inventory i');
$CI->db->join(db_prefix() . 'pos_products p', 'p.id = i.product_id');
$CI->db->where('p.status', 1);

// Apply same filters as main query
if ($CI->input->post('location') && $CI->input->post('location') != '') {
    $CI->db->where('i.location_id', $CI->input->post('location'));
}
if ($CI->input->post('category') && $CI->input->post('category') != '') {
    $CI->db->where('p.category_id', $CI->input->post('category'));
}

$summary = $CI->db->get()->row_array();

$output['summary'] = [
    'total_products' => $summary['total_products'] ?: 0,
    'total_value' => app_format_money($summary['total_value'] ?: 0, get_base_currency()),
    'low_stock_count' => $summary['low_stock_count'] ?: 0,
    'out_of_stock_count' => $summary['out_of_stock_count'] ?: 0
];
