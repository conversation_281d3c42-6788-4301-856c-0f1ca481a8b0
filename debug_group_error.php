<?php

// Debug script to find the exact source of the group column error
define('BASEPATH', true);

// Include Perfex CRM bootstrap
require_once('../../index.php');

echo "<h2>Debug: Group Column Error</h2>\n";

// Set up error handler to catch the exact location
set_error_handler(function($severity, $message, $file, $line) {
    if (strpos($message, 'group') !== false) {
        echo "<strong>Error caught:</strong><br>\n";
        echo "Message: $message<br>\n";
        echo "File: $file<br>\n";
        echo "Line: $line<br>\n";
        echo "Backtrace:<br>\n";
        echo "<pre>" . print_r(debug_backtrace(), true) . "</pre>\n";
    }
    return false; // Let PHP handle the error normally
});

echo "<h3>Testing Version Detection Functions</h3>\n";

// Load our module files
require_once('pos_inventory.php');

echo "<h4>1. Testing pos_inventory_get_perfex_version()</h4>\n";
try {
    if (function_exists('pos_inventory_get_perfex_version')) {
        $version = pos_inventory_get_perfex_version();
        echo "✅ Function executed successfully: $version<br>\n";
    } else {
        echo "❌ Function not available<br>\n";
    }
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "<br>\n";
    echo "File: " . $e->getFile() . " Line: " . $e->getLine() . "<br>\n";
}

echo "<h4>2. Testing pos_inventory_get_installed_version()</h4>\n";
try {
    if (function_exists('pos_inventory_get_installed_version')) {
        $version = pos_inventory_get_installed_version();
        echo "✅ Function executed successfully: " . ($version ?: 'null') . "<br>\n";
    } else {
        echo "❌ Function not available<br>\n";
    }
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "<br>\n";
    echo "File: " . $e->getFile() . " Line: " . $e->getLine() . "<br>\n";
}

echo "<h4>3. Testing Direct Migration Query</h4>\n";
try {
    $CI = &get_instance();
    echo "Testing query with backticks...<br>\n";
    $result = $CI->db->select('version')
                    ->from(db_prefix() . 'migrations')
                    ->where('`group`', '')
                    ->order_by('version', 'DESC')
                    ->limit(1)
                    ->get()
                    ->row();
    echo "✅ Query with backticks successful<br>\n";
    
    echo "Testing query without backticks...<br>\n";
    $result2 = $CI->db->select('version')
                     ->from(db_prefix() . 'migrations')
                     ->where('group', '')
                     ->order_by('version', 'DESC')
                     ->limit(1)
                     ->get()
                     ->row();
    echo "✅ Query without backticks successful (unexpected!)<br>\n";
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "<br>\n";
    echo "File: " . $e->getFile() . " Line: " . $e->getLine() . "<br>\n";
}

echo "<h4>4. Check Migration Table Structure</h4>\n";
try {
    $CI = &get_instance();
    $fields = $CI->db->field_data(db_prefix() . 'migrations');
    echo "Migration table fields:<br>\n";
    foreach ($fields as $field) {
        echo "- {$field->name} ({$field->type})<br>\n";
    }
} catch (Exception $e) {
    echo "❌ Error checking table structure: " . $e->getMessage() . "<br>\n";
}

echo "<h4>5. Testing Activation Hook</h4>\n";
try {
    echo "Calling pos_inventory_module_activation_hook()...<br>\n";
    pos_inventory_module_activation_hook();
    echo "✅ Activation hook completed successfully<br>\n";
} catch (Exception $e) {
    echo "❌ Activation hook failed: " . $e->getMessage() . "<br>\n";
    echo "File: " . $e->getFile() . " Line: " . $e->getLine() . "<br>\n";
    echo "Stack trace:<br>\n";
    echo "<pre>" . $e->getTraceAsString() . "</pre>\n";
}

restore_error_handler();

?>
