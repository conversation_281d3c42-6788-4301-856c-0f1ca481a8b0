/* Advanced POS & Central Inventory Module Styles */

/* Dashboard Styles */
.dashboard-stats {
    display: flex;
    flex-wrap: wrap;
    gap: 20px;
    margin-bottom: 30px;
}

.stat-card {
    flex: 1;
    min-width: 200px;
    background: #fff;
    border-radius: 8px;
    padding: 20px;
    text-align: center;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    border-left: 4px solid #007bff;
}

.stat-card.success {
    border-left-color: #28a745;
}

.stat-card.info {
    border-left-color: #17a2b8;
}

.stat-card.warning {
    border-left-color: #ffc107;
}

.stat-card.danger {
    border-left-color: #dc3545;
}

.stat-value {
    font-size: 2rem;
    font-weight: 700;
    color: #495057;
    margin-bottom: 5px;
}

.stat-label {
    font-size: 0.875rem;
    color: #6c757d;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* POS Interface Styles */
.pos-interface {
    background: #f8f9fa;
    min-height: 100vh;
}

.pos-header {
    background: #fff;
    border-bottom: 1px solid #dee2e6;
    padding: 15px 20px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.pos-content {
    display: flex;
    height: calc(100vh - 80px);
}

.pos-products-section {
    flex: 2;
    padding: 20px;
    overflow-y: auto;
}

.pos-cart-section {
    flex: 1;
    background: #fff;
    border-left: 1px solid #dee2e6;
    padding: 20px;
    overflow-y: auto;
}

/* Product Grid */
.products-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 15px;
    margin-top: 20px;
}

.product-card {
    background: #fff;
    border: 1px solid #dee2e6;
    border-radius: 8px;
    padding: 15px;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.product-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.15);
    border-color: #007bff;
}

.product-image {
    width: 80px;
    height: 80px;
    object-fit: cover;
    border-radius: 4px;
    margin-bottom: 10px;
}

.product-name {
    font-weight: 600;
    margin-bottom: 5px;
    font-size: 14px;
    color: #333;
}

.product-price {
    color: #007bff;
    font-weight: 700;
    font-size: 16px;
}

.product-stock {
    font-size: 12px;
    color: #6c757d;
    margin-top: 5px;
}

.product-stock.low-stock {
    color: #dc3545;
    font-weight: 600;
}

.product-stock.out-of-stock {
    color: #dc3545;
    font-weight: 700;
}

/* Cart Styles */
.cart-header {
    border-bottom: 1px solid #dee2e6;
    padding-bottom: 15px;
    margin-bottom: 20px;
}

.cart-items {
    max-height: 300px;
    overflow-y: auto;
    margin-bottom: 20px;
}

.cart-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 0;
    border-bottom: 1px solid #f1f3f4;
}

.cart-item:last-child {
    border-bottom: none;
}

.cart-item-info {
    flex: 1;
}

.cart-item-name {
    font-weight: 600;
    font-size: 14px;
    margin-bottom: 2px;
}

.cart-item-price {
    color: #6c757d;
    font-size: 12px;
}

.cart-item-quantity {
    display: flex;
    align-items: center;
    margin: 0 10px;
}

.quantity-btn {
    background: #f8f9fa;
    border: 1px solid #dee2e6;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    border-radius: 4px;
}

.quantity-btn:hover {
    background: #e9ecef;
}

.quantity-input {
    width: 50px;
    text-align: center;
    border: 1px solid #dee2e6;
    height: 30px;
    margin: 0 5px;
    border-radius: 4px;
}

.cart-item-total {
    font-weight: 600;
    color: #007bff;
}

.cart-remove {
    color: #dc3545;
    cursor: pointer;
    margin-left: 10px;
}

.cart-remove:hover {
    color: #c82333;
}

/* Cart Summary */
.cart-summary {
    border-top: 1px solid #dee2e6;
    padding-top: 15px;
}

.summary-row {
    display: flex;
    justify-content: space-between;
    margin-bottom: 10px;
}

.summary-row.total {
    font-weight: 700;
    font-size: 18px;
    color: #007bff;
    border-top: 1px solid #dee2e6;
    padding-top: 10px;
    margin-top: 15px;
}

/* Payment Section */
.payment-section {
    margin-top: 20px;
}

.payment-methods {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 10px;
    margin-bottom: 15px;
}

.payment-method {
    padding: 10px;
    border: 2px solid #dee2e6;
    border-radius: 6px;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
}

.payment-method.active {
    border-color: #007bff;
    background: #e7f3ff;
    color: #007bff;
}

.payment-method:hover {
    border-color: #007bff;
}

/* Action Buttons */
.pos-actions {
    margin-top: 20px;
}

.btn-pos {
    width: 100%;
    padding: 12px;
    margin-bottom: 10px;
    border-radius: 6px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.btn-pos-primary {
    background: #007bff;
    border-color: #007bff;
    color: #fff;
}

.btn-pos-primary:hover {
    background: #0056b3;
    border-color: #0056b3;
}

.btn-pos-success {
    background: #28a745;
    border-color: #28a745;
    color: #fff;
}

.btn-pos-success:hover {
    background: #1e7e34;
    border-color: #1e7e34;
}

.btn-pos-warning {
    background: #ffc107;
    border-color: #ffc107;
    color: #212529;
}

.btn-pos-warning:hover {
    background: #e0a800;
    border-color: #e0a800;
}

.btn-pos-danger {
    background: #dc3545;
    border-color: #dc3545;
    color: #fff;
}

.btn-pos-danger:hover {
    background: #c82333;
    border-color: #c82333;
}

/* Search Bar */
.pos-search {
    position: relative;
    margin-bottom: 20px;
}

.pos-search input {
    width: 100%;
    padding: 12px 45px 12px 15px;
    border: 2px solid #dee2e6;
    border-radius: 25px;
    font-size: 16px;
    outline: none;
    transition: border-color 0.3s ease;
}

.pos-search input:focus {
    border-color: #007bff;
}

.pos-search .search-icon {
    position: absolute;
    right: 15px;
    top: 50%;
    transform: translateY(-50%);
    color: #6c757d;
}

/* Customer Selection */
.customer-section {
    background: #f8f9fa;
    padding: 15px;
    border-radius: 6px;
    margin-bottom: 20px;
}

.customer-select {
    width: 100%;
    padding: 8px 12px;
    border: 1px solid #dee2e6;
    border-radius: 4px;
    margin-bottom: 10px;
}

/* Responsive Design */
@media (max-width: 768px) {
    .pos-content {
        flex-direction: column;
        height: auto;
    }
    
    .pos-cart-section {
        border-left: none;
        border-top: 1px solid #dee2e6;
    }
    
    .products-grid {
        grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
        gap: 10px;
    }
    
    .payment-methods {
        grid-template-columns: 1fr;
    }
}

/* Dashboard Styles */
.dashboard-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.stat-card {
    background: #fff;
    padding: 25px;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    border-left: 4px solid #007bff;
}

.stat-card.success {
    border-left-color: #28a745;
}

.stat-card.warning {
    border-left-color: #ffc107;
}

.stat-card.danger {
    border-left-color: #dc3545;
}

.stat-card.info {
    border-left-color: #17a2b8;
}

.stat-value {
    font-size: 2rem;
    font-weight: 700;
    color: #333;
    margin-bottom: 5px;
}

.stat-label {
    color: #6c757d;
    font-size: 14px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* Table Styles */
.pos-table {
    background: #fff;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.pos-table th {
    background: #f8f9fa;
    font-weight: 600;
    text-transform: uppercase;
    font-size: 12px;
    letter-spacing: 0.5px;
    color: #6c757d;
}

/* Status Badges */
.status-badge {
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 11px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.status-badge.active {
    background: #d4edda;
    color: #155724;
}

.status-badge.inactive {
    background: #f8d7da;
    color: #721c24;
}

.status-badge.pending {
    background: #fff3cd;
    color: #856404;
}

.status-badge.completed {
    background: #d1ecf1;
    color: #0c5460;
}

/* Form Styles */
.pos-form-group {
    margin-bottom: 20px;
}

.pos-form-label {
    font-weight: 600;
    margin-bottom: 5px;
    display: block;
    color: #333;
}

.pos-form-control {
    width: 100%;
    padding: 10px 12px;
    border: 1px solid #dee2e6;
    border-radius: 4px;
    font-size: 14px;
    transition: border-color 0.3s ease;
}

.pos-form-control:focus {
    outline: none;
    border-color: #007bff;
    box-shadow: 0 0 0 0.2rem rgba(0,123,255,0.25);
}

/* Loading Spinner */
.pos-loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid #f3f3f3;
    border-top: 3px solid #007bff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Alerts */
.pos-alert {
    padding: 12px 15px;
    border-radius: 4px;
    margin-bottom: 20px;
    border: 1px solid transparent;
}

.pos-alert.success {
    color: #155724;
    background-color: #d4edda;
    border-color: #c3e6cb;
}

.pos-alert.error {
    color: #721c24;
    background-color: #f8d7da;
    border-color: #f5c6cb;
}

.pos-alert.warning {
    color: #856404;
    background-color: #fff3cd;
    border-color: #ffeaa7;
}

.pos-alert.info {
    color: #0c5460;
    background-color: #d1ecf1;
    border-color: #bee5eb;
}
