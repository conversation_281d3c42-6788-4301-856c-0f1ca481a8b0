<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $title; ?></title>
    <link href="<?php echo base_url('assets/css/bootstrap.min.css'); ?>" rel="stylesheet">
    <link href="<?php echo base_url('assets/css/font-awesome.min.css'); ?>" rel="stylesheet">
    <style>
        body { padding: 20px; }
        .contact-item {
            padding: 10px;
            border: 1px solid #ddd;
            margin-bottom: 10px;
            cursor: pointer;
            border-radius: 4px;
        }
        .contact-item:hover {
            background-color: #f5f5f5;
        }
        .contact-item.selected {
            background-color: #d4edda;
            border-color: #28a745;
        }
        .search-box {
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <div class="col-md-12">
                <h4><?php echo $title; ?></h4>
                <hr>
                
                <div class="row">
                    <div class="col-md-6">
                        <div class="panel panel-primary">
                            <div class="panel-heading">
                                <h5 class="panel-title"><?php echo _l('supplier_information'); ?></h5>
                            </div>
                            <div class="panel-body">
                                <table class="table table-striped">
                                    <tr>
                                        <td><strong><?php echo _l('name'); ?>:</strong></td>
                                        <td><?php echo $supplier['name']; ?></td>
                                    </tr>
                                    <tr>
                                        <td><strong><?php echo _l('company'); ?>:</strong></td>
                                        <td><?php echo $supplier['company']; ?></td>
                                    </tr>
                                    <tr>
                                        <td><strong><?php echo _l('email'); ?>:</strong></td>
                                        <td><?php echo $supplier['email']; ?></td>
                                    </tr>
                                    <tr>
                                        <td><strong><?php echo _l('phone'); ?>:</strong></td>
                                        <td><?php echo $supplier['phone']; ?></td>
                                    </tr>
                                    <tr>
                                        <td><strong><?php echo _l('address'); ?>:</strong></td>
                                        <td><?php echo $supplier['address']; ?></td>
                                    </tr>
                                </table>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-6">
                        <div class="panel panel-success">
                            <div class="panel-heading">
                                <h5 class="panel-title"><?php echo _l('select_perfex_contact'); ?></h5>
                            </div>
                            <div class="panel-body">
                                <div class="search-box">
                                    <input type="text" class="form-control" id="contact-search" 
                                           placeholder="<?php echo _l('search_contacts'); ?>">
                                </div>
                                
                                <div id="contacts-list" style="max-height: 400px; overflow-y: auto;">
                                    <?php foreach($perfex_contacts as $contact): ?>
                                        <div class="contact-item" data-contact-id="<?php echo $contact['userid']; ?>">
                                            <strong><?php echo $contact['company']; ?></strong><br>
                                            <small class="text-muted">
                                                <?php echo $contact['email']; ?>
                                                <?php if (!empty($contact['phonenumber'])): ?>
                                                    | <?php echo $contact['phonenumber']; ?>
                                                <?php endif; ?>
                                            </small>
                                            <?php if (!empty($contact['address'])): ?>
                                                <br><small class="text-muted"><?php echo $contact['address']; ?></small>
                                            <?php endif; ?>
                                        </div>
                                    <?php endforeach; ?>
                                </div>
                                
                                <div class="text-center" style="margin-top: 20px;">
                                    <button type="button" class="btn btn-success" id="link-contact" disabled>
                                        <i class="fa fa-link"></i> <?php echo _l('link_contact'); ?>
                                    </button>
                                    <button type="button" class="btn btn-warning" id="unlink-contact">
                                        <i class="fa fa-unlink"></i> <?php echo _l('remove_link'); ?>
                                    </button>
                                    <button type="button" class="btn btn-default" onclick="window.close()">
                                        <?php echo _l('cancel'); ?>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Auto-matching suggestions -->
                <div class="row">
                    <div class="col-md-12">
                        <div class="panel panel-info">
                            <div class="panel-heading">
                                <h5 class="panel-title">
                                    <i class="fa fa-magic"></i> <?php echo _l('suggested_matches'); ?>
                                </h5>
                            </div>
                            <div class="panel-body">
                                <div id="suggested-matches">
                                    <!-- Suggested matches will be loaded here -->
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="<?php echo base_url('assets/js/jquery.min.js'); ?>"></script>
    <script src="<?php echo base_url('assets/js/bootstrap.min.js'); ?>"></script>
    <script>
    $(document).ready(function() {
        var selectedContactId = null;
        var supplierId = <?php echo $supplier['id']; ?>;
        
        // Highlight current linked contact
        <?php if (!empty($supplier['perfex_contact_id'])): ?>
            $('.contact-item[data-contact-id="<?php echo $supplier['perfex_contact_id']; ?>"]').addClass('selected');
            selectedContactId = <?php echo $supplier['perfex_contact_id']; ?>;
            $('#link-contact').prop('disabled', false).text('<?php echo _l('update_link'); ?>');
        <?php endif; ?>
        
        // Contact selection
        $('.contact-item').click(function() {
            $('.contact-item').removeClass('selected');
            $(this).addClass('selected');
            selectedContactId = $(this).data('contact-id');
            $('#link-contact').prop('disabled', false);
        });
        
        // Search functionality
        $('#contact-search').on('input', function() {
            var searchTerm = $(this).val().toLowerCase();
            $('.contact-item').each(function() {
                var text = $(this).text().toLowerCase();
                if (text.indexOf(searchTerm) > -1) {
                    $(this).show();
                } else {
                    $(this).hide();
                }
            });
        });
        
        // Link contact
        $('#link-contact').click(function() {
            if (!selectedContactId) {
                alert('<?php echo _l('please_select_contact'); ?>');
                return;
            }
            
            $.post('<?php echo admin_url('pos_inventory/save_supplier_perfex_link'); ?>', {
                supplier_id: supplierId,
                contact_id: selectedContactId
            }, function(response) {
                if (response.success) {
                    alert('<?php echo _l('contact_linked_successfully'); ?>');
                    if (window.opener) {
                        window.opener.location.reload();
                    }
                    window.close();
                } else {
                    alert(response.message || '<?php echo _l('error_linking_contact'); ?>');
                }
            }, 'json').fail(function() {
                alert('<?php echo _l('error_linking_contact'); ?>');
            });
        });
        
        // Unlink contact
        $('#unlink-contact').click(function() {
            if (confirm('<?php echo _l('confirm_unlink_contact'); ?>')) {
                $.post('<?php echo admin_url('pos_inventory/save_supplier_perfex_link'); ?>', {
                    supplier_id: supplierId,
                    contact_id: null
                }, function(response) {
                    if (response.success) {
                        alert('<?php echo _l('contact_unlinked_successfully'); ?>');
                        if (window.opener) {
                            window.opener.location.reload();
                        }
                        window.close();
                    } else {
                        alert(response.message || '<?php echo _l('error_unlinking_contact'); ?>');
                    }
                }, 'json').fail(function() {
                    alert('<?php echo _l('error_unlinking_contact'); ?>');
                });
            }
        });
        
        // Load suggested matches
        loadSuggestedMatches();
    });
    
    function loadSuggestedMatches() {
        $.post('<?php echo admin_url('pos_inventory/get_suggested_perfex_matches'); ?>', {
            supplier_id: <?php echo $supplier['id']; ?>
        }, function(response) {
            if (response.success && response.matches.length > 0) {
                var html = '';
                response.matches.forEach(function(match) {
                    html += '<div class="alert alert-info">';
                    html += '<strong>' + match.company + '</strong> ';
                    html += '<span class="label label-info">' + match.score + '% match</span>';
                    html += '<br><small>' + match.reason + '</small>';
                    html += '<div class="pull-right">';
                    html += '<button class="btn btn-xs btn-success" onclick="selectSuggestedMatch(' + match.userid + ')">';
                    html += '<i class="fa fa-check"></i> <?php echo _l('select'); ?>';
                    html += '</button>';
                    html += '</div>';
                    html += '<div class="clearfix"></div>';
                    html += '</div>';
                });
                $('#suggested-matches').html(html);
            } else {
                $('#suggested-matches').html('<p class="text-muted"><?php echo _l('no_suggested_matches'); ?></p>');
            }
        }, 'json');
    }
    
    function selectSuggestedMatch(contactId) {
        $('.contact-item').removeClass('selected');
        $('.contact-item[data-contact-id="' + contactId + '"]').addClass('selected');
        selectedContactId = contactId;
        $('#link-contact').prop('disabled', false);
        
        // Scroll to the selected contact
        var selectedElement = $('.contact-item[data-contact-id="' + contactId + '"]');
        if (selectedElement.length) {
            $('#contacts-list').scrollTop(selectedElement.offset().top - $('#contacts-list').offset().top + $('#contacts-list').scrollTop());
        }
    }
    </script>
</body>
</html>
