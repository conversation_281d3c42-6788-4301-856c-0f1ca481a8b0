/**
 * Advanced POS & Central Inventory Module JavaScript
 */

// Global POS object
var POS = {
    cart: [],
    customer: null,
    paymentMethod: 'cash',
    discount: 0,
    tax: 0,
    
    // Initialize POS system
    init: function() {
        this.bindEvents();
        this.loadCart();
        this.updateCartDisplay();
    },
    
    // Bind event handlers
    bindEvents: function() {
        var self = this;
        
        // Product selection
        $(document).on('click', '.product-card', function() {
            var productId = $(this).data('product-id');
            var productName = $(this).data('product-name');
            var productPrice = parseFloat($(this).data('product-price'));
            var productStock = parseInt($(this).data('product-stock'));
            
            if (productStock > 0) {
                self.addToCart(productId, productName, productPrice, 1);
            } else {
                self.showAlert('error', 'Product is out of stock');
            }
        });
        
        // Cart quantity controls
        $(document).on('click', '.quantity-increase', function() {
            var index = $(this).data('index');
            self.updateQuantity(index, self.cart[index].quantity + 1);
        });
        
        $(document).on('click', '.quantity-decrease', function() {
            var index = $(this).data('index');
            if (self.cart[index].quantity > 1) {
                self.updateQuantity(index, self.cart[index].quantity - 1);
            }
        });
        
        $(document).on('change', '.quantity-input', function() {
            var index = $(this).data('index');
            var quantity = parseInt($(this).val()) || 1;
            self.updateQuantity(index, quantity);
        });
        
        // Remove from cart
        $(document).on('click', '.cart-remove', function() {
            var index = $(this).data('index');
            self.removeFromCart(index);
        });
        
        // Payment method selection
        $(document).on('click', '.payment-method', function() {
            $('.payment-method').removeClass('active');
            $(this).addClass('active');
            self.paymentMethod = $(this).data('method');
        });
        
        // Customer selection
        $(document).on('change', '#customer-select', function() {
            self.customer = $(this).val();
        });
        
        // Search products
        $(document).on('input', '#product-search', function() {
            var searchTerm = $(this).val().toLowerCase();
            self.filterProducts(searchTerm);
        });
        
        // Barcode scanner
        $(document).on('keypress', '#barcode-input', function(e) {
            if (e.which === 13) { // Enter key
                var barcode = $(this).val();
                self.searchByBarcode(barcode);
                $(this).val('');
            }
        });
        
        // Action buttons
        $(document).on('click', '#complete-sale', function() {
            self.completeSale();
        });
        
        $(document).on('click', '#hold-order', function() {
            self.holdOrder();
        });
        
        $(document).on('click', '#clear-cart', function() {
            self.clearCart();
        });
        
        $(document).on('click', '#print-receipt', function() {
            self.printReceipt();
        });
    },
    
    // Add product to cart
    addToCart: function(productId, productName, productPrice, quantity, productData) {
        var existingIndex = this.cart.findIndex(item => item.id === productId);

        if (existingIndex !== -1) {
            this.cart[existingIndex].quantity += quantity;
        } else {
            this.cart.push({
                id: productId,
                name: productName,
                price: productPrice,
                quantity: quantity,
                cost: productData ? productData.cost_price : 0,
                trackInventory: productData ? productData.track_inventory : false,
                allowBackorders: productData ? productData.allow_backorders : false
            });
        }

        this.saveCart();
        this.updateCartDisplay();
        this.showAlert('success', 'Product added to cart');
    },
    
    // Update cart item quantity
    updateQuantity: function(index, quantity) {
        if (quantity <= 0) {
            this.removeFromCart(index);
            return;
        }
        
        this.cart[index].quantity = quantity;
        this.saveCart();
        this.updateCartDisplay();
    },
    
    // Remove item from cart
    removeFromCart: function(index) {
        this.cart.splice(index, 1);
        this.saveCart();
        this.updateCartDisplay();
        this.showAlert('info', 'Product removed from cart');
    },
    
    // Clear entire cart
    clearCart: function() {
        if (confirm('Are you sure you want to clear the cart?')) {
            this.cart = [];
            this.saveCart();
            this.updateCartDisplay();
            this.showAlert('info', 'Cart cleared');
        }
    },
    
    // Update cart display
    updateCartDisplay: function() {
        var cartItemsHtml = '';
        var subtotal = 0;
        
        this.cart.forEach(function(item, index) {
            var itemTotal = item.price * item.quantity;
            subtotal += itemTotal;

            cartItemsHtml += `
                <div class="cart-item">
                    <div class="cart-item-info">
                        <div class="cart-item-name">${item.name}</div>
                        <div class="cart-item-price">$${item.price.toFixed(2)}</div>
                    </div>
                    <div class="cart-item-quantity">
                        <button class="quantity-btn quantity-decrease" data-index="${index}">-</button>
                        <input type="number" class="quantity-input" data-index="${index}" value="${item.quantity}" min="1">
                        <button class="quantity-btn quantity-increase" data-index="${index}">+</button>
                    </div>
                    <div class="cart-item-total">$${itemTotal.toFixed(2)}</div>
                    <div class="cart-remove" data-index="${index}">
                        <i class="fa fa-trash"></i>
                    </div>
                </div>
            `;
        });
        
        $('#cart-items').html(cartItemsHtml);
        
        // Update summary
        var tax = subtotal * (this.tax / 100);
        var discount = subtotal * (this.discount / 100);
        var total = subtotal + tax - discount;
        
        $('#cart-subtotal').text('$' + subtotal.toFixed(2));
        $('#cart-tax').text('$' + tax.toFixed(2));
        $('#cart-discount').text('$' + discount.toFixed(2));
        $('#cart-total').text('$' + total.toFixed(2));
        
        // Update cart count
        var totalItems = this.cart.reduce((sum, item) => sum + item.quantity, 0);
        $('#cart-count').text(totalItems);
        
        // Enable/disable checkout button
        $('#complete-sale').prop('disabled', this.cart.length === 0);
    },
    
    // Save cart to localStorage
    saveCart: function() {
        localStorage.setItem('pos_cart', JSON.stringify(this.cart));
    },
    
    // Load cart from localStorage
    loadCart: function() {
        var savedCart = localStorage.getItem('pos_cart');
        if (savedCart) {
            this.cart = JSON.parse(savedCart);
        }
    },
    
    // Filter products by search term
    filterProducts: function(searchTerm) {
        $('.product-card').each(function() {
            var productName = $(this).data('product-name').toLowerCase();
            var productSku = $(this).data('product-sku').toLowerCase();
            
            if (productName.includes(searchTerm) || productSku.includes(searchTerm)) {
                $(this).show();
            } else {
                $(this).hide();
            }
        });
    },
    
    // Search product by barcode
    searchByBarcode: function(barcode) {
        var self = this;
        
        $.ajax({
            url: admin_url + 'pos_inventory/search_by_barcode',
            type: 'POST',
            data: { barcode: barcode },
            dataType: 'json',
            success: function(response) {
                if (response.success && response.product) {
                    var product = response.product;
                    self.addToCart(product.id, product.name, product.price, 1);
                } else {
                    self.showAlert('error', 'Product not found');
                }
            },
            error: function() {
                self.showAlert('error', 'Error searching for product');
            }
        });
    },
    
    // Complete sale
    completeSale: function() {
        var self = this;

        if (this.cart.length === 0) {
            this.showAlert('warning', lang.cart_is_empty || 'Cart is empty');
            return;
        }

        // Validate payment amount for cash transactions
        if (this.paymentMethod === 'cash') {
            var total = this.getCartTotal();
            var received = parseFloat($('#amount-received').val()) || 0;

            if (received < total) {
                this.showAlert('warning', lang.insufficient_payment_amount || 'Insufficient payment amount');
                $('#amount-received').focus();
                return;
            }
        }

        var saleData = {
            cart: this.cart,
            customer: this.customer,
            payment_method: this.paymentMethod,
            discount_type: this.discountType || 'percentage',
            discount_value: this.discountValue || 0,
            discount_reason: this.discountReason || '',
            tax_rate: this.taxRate || 0,
            amount_received: parseFloat($('#amount-received').val()) || this.getCartTotal(),
            location_id: current_location_id || 1,
            notes: $('#transaction-notes').val() || ''
        };

        $.ajax({
            url: admin_url + 'pos_inventory/complete_sale',
            type: 'POST',
            data: saleData,
            dataType: 'json',
            beforeSend: function() {
                $('#complete-sale').prop('disabled', true).html('<span class="pos-loading"></span> Processing...');
            },
            success: function(response) {
                if (response.success) {
                    self.clearCart();
                    self.showAlert('success', response.message || 'Sale completed successfully');

                    // Show receipt options
                    self.showReceiptOptions(response);
                } else {
                    self.showAlert('error', response.message || 'Error completing sale');
                }
            },
            error: function() {
                self.showAlert('error', 'Network error occurred');
            },
            complete: function() {
                $('#complete-sale').prop('disabled', false).html('<i class="fa fa-check"></i> Complete Sale');
            }
        });
    },

    showReceiptOptions: function(response) {
        var self = this;

        // Create receipt options modal
        var modalHtml = `
            <div class="modal fade" id="receiptOptionsModal" tabindex="-1" role="dialog">
                <div class="modal-dialog" role="document">
                    <div class="modal-content">
                        <div class="modal-header">
                            <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                <span aria-hidden="true">&times;</span>
                            </button>
                            <h4 class="modal-title">Sale Completed Successfully!</h4>
                        </div>
                        <div class="modal-body text-center">
                            <div class="alert alert-success">
                                <i class="fa fa-check-circle fa-2x"></i><br>
                                <strong>Transaction #: ${response.transaction_number}</strong><br>
                                Total: ${currency_symbol}${this.getCartTotal().toFixed(2)}
                            </div>
                            <p>What would you like to do with the receipt?</p>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-default" data-dismiss="modal">
                                <i class="fa fa-times"></i> Close
                            </button>
                            <button type="button" class="btn btn-info" onclick="window.open('${response.receipt_url}', '_blank')">
                                <i class="fa fa-eye"></i> View Receipt
                            </button>
                            <button type="button" class="btn btn-primary" onclick="window.open('${response.print_url}', '_blank')">
                                <i class="fa fa-print"></i> Print Receipt
                            </button>
                            <button type="button" class="btn btn-success" onclick="POS.emailReceipt('${response.transaction_id}')">
                                <i class="fa fa-envelope"></i> Email Receipt
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        // Remove existing modal if any
        $('#receiptOptionsModal').remove();

        // Add modal to body and show
        $('body').append(modalHtml);
        $('#receiptOptionsModal').modal('show');

        // Auto-close after 10 seconds
        setTimeout(function() {
            $('#receiptOptionsModal').modal('hide');
        }, 10000);
    },

    emailReceipt: function(transactionId) {
        var email = prompt('Enter email address:');
        if (email) {
            $.post(admin_url + 'pos_inventory/email_receipt', {
                transaction_id: transactionId,
                email: email
            }, function(response) {
                if (response.success) {
                    POS.showAlert('success', response.message);
                } else {
                    POS.showAlert('error', response.message);
                }
            }, 'json');
        }
    },
    
    // Hold order
    holdOrder: function() {
        if (this.cart.length === 0) {
            this.showAlert('error', 'Cart is empty');
            return;
        }
        
        var self = this;
        var orderData = {
            cart: this.cart,
            customer: this.customer
        };
        
        $.ajax({
            url: admin_url + 'pos_inventory/hold_order',
            type: 'POST',
            data: orderData,
            dataType: 'json',
            success: function(response) {
                if (response.success) {
                    self.clearCart();
                    self.showAlert('success', 'Order held successfully');
                } else {
                    self.showAlert('error', response.message || 'Error holding order');
                }
            },
            error: function() {
                self.showAlert('error', 'Error holding order');
            }
        });
    },
    
    // Print receipt
    printReceipt: function() {
        window.print();
    },
    
    // Show alert message
    showAlert: function(type, message) {
        var alertHtml = `
            <div class="pos-alert ${type}" style="position: fixed; top: 20px; right: 20px; z-index: 9999; min-width: 300px;">
                ${message}
                <button type="button" class="close" style="float: right; background: none; border: none; font-size: 18px; cursor: pointer;">&times;</button>
            </div>
        `;
        
        $('body').append(alertHtml);
        
        // Auto remove after 3 seconds
        setTimeout(function() {
            $('.pos-alert').fadeOut(function() {
                $(this).remove();
            });
        }, 3000);
        
        // Manual close
        $(document).on('click', '.pos-alert .close', function() {
            $(this).parent().fadeOut(function() {
                $(this).remove();
            });
        });
    }
};

// Inventory Management
var Inventory = {
    inventoryTable: null,

    init: function() {
        this.bindEvents();
        this.initDataTables();
    },

    bindEvents: function() {
        var self = this;

        // Filter change handlers
        $('#filter_location, #filter_category, #filter_stock_status').change(function() {
            if (self.inventoryTable) {
                self.inventoryTable.ajax.reload();
            }
        });

        // Clear filters
        $('#clear_filters').click(function() {
            $('#filter_location, #filter_category, #filter_stock_status').val('').trigger('change');
            $('.selectpicker').selectpicker('refresh');
            if (self.inventoryTable) {
                self.inventoryTable.ajax.reload();
            }
        });

        // Select all checkbox
        $('#select-all').change(function() {
            $('.individual-checkbox').prop('checked', $(this).is(':checked'));
            self.updateBulkActionButton();
        });

        // Individual checkboxes
        $(document).on('change', '.individual-checkbox', function() {
            self.updateBulkActionButton();
        });

        // Quick adjustment
        $(document).on('click', '.quick-adjust', function() {
            var data = $(this).data();
            $('#quick_product_id').val(data.productId);
            $('#quick_location_id').val(data.locationId);
            $('#quick_product_name').text(data.productName);
            $('#quick_location_name').text(data.locationName);
            $('#quick_current_stock').text(data.currentStock);
            $('#quickAdjustmentModal').modal('show');
        });

        // Quick adjustment form submit
        $('#quick-adjustment-form').submit(function(e) {
            e.preventDefault();
            var formData = $(this).serialize();

            $.post(admin_url + 'pos_inventory/quick_stock_adjustment', formData, function(response) {
                if (response.success) {
                    alert_float('success', response.message);
                    $('#quickAdjustmentModal').modal('hide');
                    if (self.inventoryTable) {
                        self.inventoryTable.ajax.reload();
                    }
                } else {
                    alert_float('danger', response.message);
                }
            }, 'json');
        });

        // Bulk adjustment
        $('#bulk-adjustment-form').submit(function(e) {
            e.preventDefault();
            var selectedIds = self.getSelectedIds();

            if (selectedIds.length === 0) {
                alert('Please select products for bulk adjustment');
                return;
            }

            var formData = $(this).serialize() + '&product_ids=' + selectedIds.join(',');

            $.post(admin_url + 'pos_inventory/bulk_stock_adjustment', formData, function(response) {
                if (response.success) {
                    alert_float('success', response.message);
                    $('#bulkAdjustmentModal').modal('hide');
                    if (self.inventoryTable) {
                        self.inventoryTable.ajax.reload();
                    }
                } else {
                    alert_float('danger', response.message);
                }
            }, 'json');
        });
    },

    initDataTables: function() {
        var self = this;

        // Initialize inventory table with server-side processing
        if ($('#inventory-table').length) {
            this.inventoryTable = $('#inventory-table').DataTable({
                processing: true,
                serverSide: true,
                ajax: {
                    url: admin_url + 'pos_inventory/inventory_table',
                    type: 'POST',
                    data: function(d) {
                        d.location = $('#filter_location').val();
                        d.category = $('#filter_category').val();
                        d.stock_status = $('#filter_stock_status').val();
                    }
                },
                columns: [
                    { data: 'checkbox', orderable: false, searchable: false },
                    { data: 'product_name' },
                    { data: 'sku' },
                    { data: 'location_name' },
                    { data: 'quantity' },
                    { data: 'reserved' },
                    { data: 'available' },
                    { data: 'value' },
                    { data: 'status' },
                    { data: 'last_updated' },
                    { data: 'actions', orderable: false, searchable: false }
                ],
                order: [[1, 'asc']],
                responsive: true,
                drawCallback: function() {
                    // Update summary after table draw
                    var info = this.api().ajax.json();
                    if (info && info.summary) {
                        $('#total-products').text(info.summary.total_products);
                        $('#total-stock-value').text(info.summary.total_value);
                        $('#low-stock-count').text(info.summary.low_stock_count);
                        $('#out-of-stock-count').text(info.summary.out_of_stock_count);
                    }
                }
            });
        }

        // Initialize low stock table
        if ($('#low-stock-table').length) {
            $('#low-stock-table').DataTable({
                processing: true,
                serverSide: true,
                ajax: {
                    url: admin_url + 'pos_inventory/low_stock_table',
                    type: 'POST'
                },
                columns: [
                    { data: 'product_name' },
                    { data: 'location_name' },
                    { data: 'current_stock' },
                    { data: 'threshold' },
                    { data: 'actions', orderable: false, searchable: false }
                ],
                order: [[2, 'asc']],
                pageLength: 5,
                responsive: true
            });
        }
    },

    updateBulkActionButton: function() {
        var selectedCount = $('.individual-checkbox:checked').length;
        $('#selected-products-count').text(selectedCount);

        if (selectedCount > 0) {
            $('#bulk-actions-btn').prop('disabled', false);
        } else {
            $('#bulk-actions-btn').prop('disabled', true);
        }
    },

    getSelectedIds: function() {
        var ids = [];
        $('.individual-checkbox:checked').each(function() {
            ids.push($(this).val());
        });
        return ids;
    }
};

// Enhanced POS functionality
POS.applyDiscount = function() {
    var type = $('#discount-type').val();
    var value = parseFloat($('#discount-value').val()) || 0;
    var reason = $('#discount-reason').val();

    if (value <= 0) {
        this.showAlert('warning', lang.invalid_discount);
        return;
    }

    if (type === 'percentage' && value > 100) {
        this.showAlert('warning', lang.invalid_discount);
        return;
    }

    this.discountType = type;
    this.discountValue = value;
    this.discountReason = reason;

    this.updateCartDisplay();
    $('#discountModal').modal('hide');
    this.showAlert('success', lang.discount_applied);
};

POS.removeDiscount = function() {
    this.discountType = 'percentage';
    this.discountValue = 0;
    this.discountReason = '';
    this.updateCartDisplay();
};

POS.updateCustomerInfo = function() {
    var selectedOption = $('#customer-select option:selected');
    var customerId = selectedOption.val();

    if (customerId) {
        $('#customer-name').text(selectedOption.text());
        $('#customer-email').text(selectedOption.data('email') || 'N/A');
        $('#customer-phone').text(selectedOption.data('phone') || 'N/A');
        $('#customer-info').show();
    } else {
        $('#customer-info').hide();
    }
};

POS.addCustomer = function() {
    var formData = $('#add-customer-form').serialize();

    $.post(admin_url + 'pos_inventory/add_customer', formData, function(response) {
        if (response.success) {
            // Add new customer to select
            var newOption = '<option value="' + response.customer_id + '" selected>' +
                           $('#customer-company').val() + '</option>';
            $('#customer-select').append(newOption);

            $('#addCustomerModal').modal('hide');
            $('#add-customer-form')[0].reset();
            POS.updateCustomerInfo();
            POS.showAlert('success', lang.customer_added);
        } else {
            POS.showAlert('danger', response.message || lang.error_adding_customer);
        }
    }, 'json');
};

POS.loadHeldOrders = function() {
    $('#heldOrdersModal').modal('show');

    $.get(admin_url + 'pos_inventory/get_held_orders', function(response) {
        var html = '';

        if (response.success && response.orders.length > 0) {
            response.orders.forEach(function(order) {
                html += '<tr>';
                html += '<td>' + order.order_number + '</td>';
                html += '<td>' + (order.customer_name || 'Walk-in') + '</td>';
                html += '<td>' + order.items_count + '</td>';
                html += '<td>' + currency_symbol + parseFloat(order.total).toFixed(2) + '</td>';
                html += '<td>' + order.created_at + '</td>';
                html += '<td>';
                html += '<button class="btn btn-sm btn-success retrieve-order" data-id="' + order.id + '">Retrieve</button> ';
                html += '<button class="btn btn-sm btn-danger delete-held-order" data-id="' + order.id + '">Delete</button>';
                html += '</td>';
                html += '</tr>';
            });
        } else {
            html = '<tr><td colspan="6" class="text-center text-muted">' + lang.no_held_orders + '</td></tr>';
        }

        $('#held-orders-list').html(html);
    }, 'json');
};

POS.updateHeldOrdersCount = function() {
    $.get(admin_url + 'pos_inventory/get_held_orders_count', function(response) {
        if (response.count > 0) {
            $('#held-orders-count').text(response.count).show();
        } else {
            $('#held-orders-count').hide();
        }
    }, 'json');
};

// Retrieve held order
$(document).on('click', '.retrieve-order', function() {
    var orderId = $(this).data('id');

    $.post(admin_url + 'pos_inventory/retrieve_held_order', {order_id: orderId}, function(response) {
        if (response.success) {
            POS.cart = response.cart;
            POS.updateCartDisplay();
            $('#heldOrdersModal').modal('hide');
            POS.showAlert('success', lang.order_retrieved);
            POS.updateHeldOrdersCount();
        } else {
            POS.showAlert('danger', response.message || lang.error_retrieving_order);
        }
    }, 'json');
});

// Delete held order
$(document).on('click', '.delete-held-order', function() {
    var orderId = $(this).data('id');

    if (confirm('Are you sure you want to delete this held order?')) {
        $.post(admin_url + 'pos_inventory/delete_held_order', {order_id: orderId}, function(response) {
            if (response.success) {
                POS.loadHeldOrders();
                POS.updateHeldOrdersCount();
                POS.showAlert('success', 'Held order deleted');
            } else {
                POS.showAlert('danger', response.message);
            }
        }, 'json');
    }
});

// Integration Management
var Integration = {
    init: function() {
        this.bindEvents();
    },
    
    bindEvents: function() {
        var self = this;
        
        // Test connection
        $(document).on('click', '.test-connection', function() {
            var platform = $(this).data('platform');
            self.testConnection(platform);
        });
        
        // Manual sync
        $(document).on('click', '.manual-sync', function() {
            var platform = $(this).data('platform');
            var syncType = $(this).data('sync-type');
            self.manualSync(platform, syncType);
        });
    },
    
    testConnection: function(platform) {
        var self = this;
        
        $.ajax({
            url: admin_url + 'pos_inventory/test_connection',
            type: 'POST',
            data: { platform: platform },
            dataType: 'json',
            beforeSend: function() {
                $('.test-connection[data-platform="' + platform + '"]').prop('disabled', true).html('<span class="pos-loading"></span> Testing...');
            },
            success: function(response) {
                if (response.success) {
                    alert('Connection successful!');
                } else {
                    alert('Connection failed: ' + (response.message || 'Unknown error'));
                }
            },
            error: function() {
                alert('Error testing connection');
            },
            complete: function() {
                $('.test-connection[data-platform="' + platform + '"]').prop('disabled', false).html('Test Connection');
            }
        });
    },
    
    manualSync: function(platform, syncType) {
        var self = this;
        
        if (!confirm('Are you sure you want to start manual sync?')) {
            return;
        }
        
        $.ajax({
            url: admin_url + 'pos_inventory/manual_sync',
            type: 'POST',
            data: { 
                platform: platform,
                sync_type: syncType
            },
            dataType: 'json',
            beforeSend: function() {
                $('.manual-sync[data-platform="' + platform + '"][data-sync-type="' + syncType + '"]').prop('disabled', true).html('<span class="pos-loading"></span> Syncing...');
            },
            success: function(response) {
                if (response.success) {
                    alert('Sync started successfully!');
                    location.reload();
                } else {
                    alert('Sync failed: ' + (response.message || 'Unknown error'));
                }
            },
            error: function() {
                alert('Error starting sync');
            },
            complete: function() {
                $('.manual-sync[data-platform="' + platform + '"][data-sync-type="' + syncType + '"]').prop('disabled', false).html('Manual Sync');
            }
        });
    }
};

// Initialize modules when document is ready
$(document).ready(function() {
    // Initialize based on current page
    if ($('.pos-interface').length) {
        POS.init();
    }
    
    if ($('#inventory-table').length) {
        Inventory.init();
    }
    
    if ($('.integration-settings').length) {
        Integration.init();
    }
    
    // Initialize tooltips
    $('[data-toggle="tooltip"]').tooltip();
    
    // Initialize select2 for better dropdowns
    if (typeof $.fn.select2 !== 'undefined') {
        $('.select2').select2();
    }
});
