<?php

// Test script to verify that the logging and routing fixes work
define('BASEPATH', true);

// Include Perfex CRM bootstrap
require_once('../../index.php');

echo "<h2>POS Inventory Module - Fix Verification Test</h2>\n";

$CI = &get_instance();

// Test 1: Check if logging helper is loaded
echo "<h3>Test 1: Logging Helper Verification</h3>\n";
if (function_exists('pos_inventory_safe_log')) {
    echo "✅ pos_inventory_safe_log function available<br>\n";
} else {
    echo "❌ pos_inventory_safe_log function not available<br>\n";
}

if (function_exists('pos_inventory_log_info')) {
    echo "✅ pos_inventory_log_info function available<br>\n";
} else {
    echo "❌ pos_inventory_log_info function not available<br>\n";
}

if (function_exists('pos_inventory_log_error')) {
    echo "✅ pos_inventory_log_error function available<br>\n";
} else {
    echo "❌ pos_inventory_log_error function not available<br>\n";
}

// Test 2: Test safe logging functions
echo "<h3>Test 2: Safe Logging Function Test</h3>\n";
try {
    $test_result = pos_inventory_log_info('Test info message from fix verification');
    if ($test_result) {
        echo "✅ Safe info logging works<br>\n";
    } else {
        echo "⚠️ Safe info logging returned false (may be disabled)<br>\n";
    }
} catch (Exception $e) {
    echo "❌ Safe info logging failed: " . $e->getMessage() . "<br>\n";
}

try {
    $test_result = pos_inventory_log_warning('Test warning message from fix verification');
    if ($test_result) {
        echo "✅ Safe warning logging works<br>\n";
    } else {
        echo "⚠️ Safe warning logging returned false (may be disabled)<br>\n";
    }
} catch (Exception $e) {
    echo "❌ Safe warning logging failed: " . $e->getMessage() . "<br>\n";
}

// Test 3: Check log level constants
echo "<h3>Test 3: Log Level Constants Check</h3>\n";
$log_constants = ['LOG_LEVEL_ERROR', 'LOG_LEVEL_DEBUG', 'LOG_LEVEL_INFO', 'LOG_LEVEL_WARNING'];
foreach ($log_constants as $constant) {
    if (defined($constant)) {
        echo "✅ $constant = " . constant($constant) . "<br>\n";
    } else {
        echo "❌ $constant not defined<br>\n";
    }
}

// Test 4: Check global log levels array
echo "<h3>Test 4: Global Log Levels Array Check</h3>\n";
if (isset($GLOBALS['_log_levels'])) {
    echo "✅ Global _log_levels array exists<br>\n";
    foreach ($GLOBALS['_log_levels'] as $level => $value) {
        echo "  - $level = $value<br>\n";
    }
} else {
    echo "❌ Global _log_levels array not found<br>\n";
}

// Test 5: Check file existence
echo "<h3>Test 5: File Existence Check</h3>\n";
$files_to_check = [
    'index.php' => 'Module index file',
    '.htaccess' => 'Security configuration file',
    'helpers/pos_inventory_logging_helper.php' => 'Logging helper file'
];

foreach ($files_to_check as $file => $description) {
    if (file_exists($file)) {
        echo "✅ $description exists: $file<br>\n";
    } else {
        echo "❌ $description missing: $file<br>\n";
    }
}

// Test 6: Test module loading without errors
echo "<h3>Test 6: Module Loading Test</h3>\n";
try {
    // Try to load the main module file
    require_once('pos_inventory.php');
    echo "✅ Main module file loaded without errors<br>\n";
} catch (Exception $e) {
    echo "❌ Error loading main module file: " . $e->getMessage() . "<br>\n";
} catch (Error $e) {
    echo "❌ Fatal error loading main module file: " . $e->getMessage() . "<br>\n";
}

// Test 7: Check if module is registered
echo "<h3>Test 7: Module Registration Check</h3>\n";
$module = $CI->db->get_where(db_prefix() . 'modules', ['module_name' => 'pos_inventory'])->row();
if (!$module) {
    $module = $CI->db->get_where(db_prefix() . 'modules', ['system_name' => 'pos_inventory'])->row();
}

if ($module) {
    echo "✅ Module found in database<br>\n";
    echo "  - Status: " . ($module->active ? 'Active' : 'Inactive') . "<br>\n";
    if (isset($module->installed_version)) {
        echo "  - Version: " . $module->installed_version . "<br>\n";
    }
} else {
    echo "❌ Module not found in database<br>\n";
}

// Test 8: Check CodeIgniter logging configuration
echo "<h3>Test 8: CodeIgniter Logging Configuration</h3>\n";
if (isset($CI->config)) {
    $log_threshold = $CI->config->item('log_threshold');
    echo "✅ Log threshold: " . $log_threshold . "<br>\n";
    
    $log_path = $CI->config->item('log_path');
    if ($log_path) {
        echo "✅ Log path: " . $log_path . "<br>\n";
        if (is_writable($log_path)) {
            echo "✅ Log path is writable<br>\n";
        } else {
            echo "⚠️ Log path is not writable<br>\n";
        }
    } else {
        echo "⚠️ Log path not configured<br>\n";
    }
} else {
    echo "❌ CodeIgniter config not available<br>\n";
}

echo "<h3>Summary</h3>\n";
echo "✅ = Working correctly<br>\n";
echo "⚠️ = Working but with warnings<br>\n";
echo "❌ = Not working or missing<br>\n";
echo "<br>\n";
echo "If you see mostly ✅ symbols, the fixes are working correctly.<br>\n";
echo "If you see ❌ symbols, there may be additional issues to resolve.<br>\n";

?>
