<?php defined('BASEPATH') or exit('No direct script access allowed'); ?>
<?php init_head(); ?>
<div id="wrapper">
    <div class="content">
        <div class="row">
            <div class="col-md-12">
                <?php echo form_open($this->uri->uri_string(), ['id' => 'stock-transfer-form']); ?>
                <div class="panel_s">
                    <div class="panel-body">
                        <div class="row">
                            <div class="col-md-8">
                                <h4 class="no-margin">
                                    <i class="fa fa-exchange"></i> <?php echo _l('stock_transfer'); ?>
                                </h4>
                            </div>
                            <div class="col-md-4 text-right">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fa fa-exchange"></i> <?php echo _l('transfer_stock'); ?>
                                </button>
                                <a href="<?php echo admin_url('pos_inventory/inventory'); ?>" class="btn btn-default">
                                    <?php echo _l('cancel'); ?>
                                </a>
                            </div>
                        </div>
                        <hr class="hr-panel-heading" />
                        
                        <div class="row">
                            <!-- Product Selection -->
                            <div class="col-md-12">
                                <div class="form-group">
                                    <label for="product_id" class="control-label">
                                        <?php echo _l('product'); ?> <span class="text-danger">*</span>
                                    </label>
                                    <select class="form-control selectpicker" id="product_id" name="product_id" 
                                            data-live-search="true" data-size="10" required>
                                        <option value=""><?php echo _l('select_product'); ?></option>
                                        <?php foreach ($products as $product) { ?>
                                            <option value="<?php echo $product['id']; ?>" 
                                                    data-sku="<?php echo $product['sku']; ?>"
                                                    data-name="<?php echo htmlspecialchars($product['name']); ?>">
                                                <?php echo $product['name'] . ' (' . $product['sku'] . ')'; ?>
                                            </option>
                                        <?php } ?>
                                    </select>
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <!-- From Location -->
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="from_location" class="control-label">
                                        <?php echo _l('transfer_from'); ?> <span class="text-danger">*</span>
                                    </label>
                                    <select class="form-control selectpicker" id="from_location" name="from_location" required>
                                        <option value=""><?php echo _l('select_location'); ?></option>
                                        <?php foreach ($locations as $location) { ?>
                                            <option value="<?php echo $location['id']; ?>">
                                                <?php echo $location['name']; ?>
                                            </option>
                                        <?php } ?>
                                    </select>
                                </div>
                            </div>
                            
                            <!-- To Location -->
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="to_location" class="control-label">
                                        <?php echo _l('transfer_to'); ?> <span class="text-danger">*</span>
                                    </label>
                                    <select class="form-control selectpicker" id="to_location" name="to_location" required>
                                        <option value=""><?php echo _l('select_location'); ?></option>
                                        <?php foreach ($locations as $location) { ?>
                                            <option value="<?php echo $location['id']; ?>">
                                                <?php echo $location['name']; ?>
                                            </option>
                                        <?php } ?>
                                    </select>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Stock Information Display -->
                        <div class="row" id="stock-info" style="display: none;">
                            <div class="col-md-6">
                                <div class="panel panel-info">
                                    <div class="panel-heading">
                                        <h5 class="panel-title">
                                            <i class="fa fa-cube"></i> <?php echo _l('source_location_stock'); ?>
                                        </h5>
                                    </div>
                                    <div class="panel-body">
                                        <div class="row">
                                            <div class="col-md-6">
                                                <strong><?php echo _l('total_stock'); ?>:</strong>
                                                <span id="source-total-stock">0</span>
                                            </div>
                                            <div class="col-md-6">
                                                <strong><?php echo _l('available'); ?>:</strong>
                                                <span id="source-available-stock">0</span>
                                            </div>
                                        </div>
                                        <div class="row" style="margin-top: 10px;">
                                            <div class="col-md-6">
                                                <strong><?php echo _l('reserved'); ?>:</strong>
                                                <span id="source-reserved-stock">0</span>
                                            </div>
                                            <div class="col-md-6">
                                                <strong><?php echo _l('value'); ?>:</strong>
                                                <span id="source-stock-value">$0.00</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="col-md-6">
                                <div class="panel panel-success">
                                    <div class="panel-heading">
                                        <h5 class="panel-title">
                                            <i class="fa fa-cube"></i> <?php echo _l('destination_location_stock'); ?>
                                        </h5>
                                    </div>
                                    <div class="panel-body">
                                        <div class="row">
                                            <div class="col-md-6">
                                                <strong><?php echo _l('total_stock'); ?>:</strong>
                                                <span id="dest-total-stock">0</span>
                                            </div>
                                            <div class="col-md-6">
                                                <strong><?php echo _l('available'); ?>:</strong>
                                                <span id="dest-available-stock">0</span>
                                            </div>
                                        </div>
                                        <div class="row" style="margin-top: 10px;">
                                            <div class="col-md-6">
                                                <strong><?php echo _l('reserved'); ?>:</strong>
                                                <span id="dest-reserved-stock">0</span>
                                            </div>
                                            <div class="col-md-6">
                                                <strong><?php echo _l('value'); ?>:</strong>
                                                <span id="dest-stock-value">$0.00</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <!-- Transfer Quantity -->
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="transfer_quantity" class="control-label">
                                        <?php echo _l('transfer_quantity'); ?> <span class="text-danger">*</span>
                                    </label>
                                    <input type="number" class="form-control" id="transfer_quantity" name="quantity" 
                                           required min="1" step="1">
                                    <small class="text-muted" id="max-transfer-help"></small>
                                </div>
                            </div>
                            
                            <!-- Batch Number -->
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="batch_number" class="control-label"><?php echo _l('batch_number'); ?></label>
                                    <input type="text" class="form-control" id="batch_number" name="batch_number">
                                    <small class="text-muted"><?php echo _l('batch_number_transfer_help'); ?></small>
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <!-- Transfer Notes -->
                            <div class="col-md-12">
                                <div class="form-group">
                                    <label for="notes" class="control-label"><?php echo _l('transfer_notes'); ?></label>
                                    <textarea class="form-control" id="notes" name="notes" rows="3" 
                                              placeholder="<?php echo _l('transfer_notes_placeholder'); ?>"></textarea>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Transfer Preview -->
                        <div class="row" id="transfer-preview" style="display: none;">
                            <div class="col-md-12">
                                <div class="alert alert-info">
                                    <h5><i class="fa fa-info-circle"></i> <?php echo _l('transfer_preview'); ?></h5>
                                    <div class="row">
                                        <div class="col-md-6">
                                            <strong><?php echo _l('from'); ?>:</strong> <span id="preview-from-location"></span><br>
                                            <strong><?php echo _l('current_stock'); ?>:</strong> <span id="preview-from-current"></span><br>
                                            <strong><?php echo _l('after_transfer'); ?>:</strong> <span id="preview-from-after" class="text-warning"></span>
                                        </div>
                                        <div class="col-md-6">
                                            <strong><?php echo _l('to'); ?>:</strong> <span id="preview-to-location"></span><br>
                                            <strong><?php echo _l('current_stock'); ?>:</strong> <span id="preview-to-current"></span><br>
                                            <strong><?php echo _l('after_transfer'); ?>:</strong> <span id="preview-to-after" class="text-success"></span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Confirmation -->
                        <div class="row">
                            <div class="col-md-12">
                                <div class="alert alert-warning">
                                    <div class="checkbox">
                                        <label>
                                            <input type="checkbox" id="confirm_transfer" name="confirm_transfer" required>
                                            <?php echo _l('confirm_stock_transfer'); ?>
                                        </label>
                                    </div>
                                    <small><?php echo _l('stock_transfer_warning'); ?></small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <?php echo form_close(); ?>
            </div>
        </div>
        
        <!-- Recent Transfers -->
        <div class="row">
            <div class="col-md-12">
                <div class="panel_s">
                    <div class="panel-body">
                        <h5><?php echo _l('recent_stock_transfers'); ?></h5>
                        <div class="table-responsive">
                            <table class="table table-striped" id="recent-transfers-table">
                                <thead>
                                    <tr>
                                        <th><?php echo _l('date'); ?></th>
                                        <th><?php echo _l('product'); ?></th>
                                        <th><?php echo _l('from'); ?></th>
                                        <th><?php echo _l('to'); ?></th>
                                        <th><?php echo _l('quantity'); ?></th>
                                        <th><?php echo _l('notes'); ?></th>
                                        <th><?php echo _l('user'); ?></th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <!-- Recent transfers will be loaded via AJAX -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
$(document).ready(function() {
    // Load stock information when selections change
    $('#product_id, #from_location, #to_location').change(function() {
        loadStockInformation();
        updateTransferPreview();
    });
    
    // Update preview when quantity changes
    $('#transfer_quantity').on('input', function() {
        updateTransferPreview();
        validateTransferQuantity();
    });
    
    // Prevent selecting same location for from and to
    $('#from_location').change(function() {
        var fromLocation = $(this).val();
        $('#to_location option').prop('disabled', false);
        if (fromLocation) {
            $('#to_location option[value="' + fromLocation + '"]').prop('disabled', true);
        }
        $('#to_location').selectpicker('refresh');
    });
    
    $('#to_location').change(function() {
        var toLocation = $(this).val();
        $('#from_location option').prop('disabled', false);
        if (toLocation) {
            $('#from_location option[value="' + toLocation + '"]').prop('disabled', true);
        }
        $('#from_location').selectpicker('refresh');
    });
    
    // Load recent transfers
    loadRecentTransfers();
});

function loadStockInformation() {
    var productId = $('#product_id').val();
    var fromLocation = $('#from_location').val();
    var toLocation = $('#to_location').val();
    
    if (productId && fromLocation && toLocation) {
        // Load source location stock
        $.post(admin_url + 'pos_inventory/get_product_stock', {
            product_id: productId,
            location_id: fromLocation
        }, function(data) {
            if (data.success) {
                $('#source-total-stock').text(data.stock.quantity);
                $('#source-available-stock').text(data.stock.available);
                $('#source-reserved-stock').text(data.stock.reserved);
                $('#source-stock-value').text(data.stock.value);
                
                // Set max transfer quantity
                $('#transfer_quantity').attr('max', data.stock.available);
                $('#max-transfer-help').text('<?php echo _l('max_available'); ?>: ' + data.stock.available);
            }
        }, 'json');
        
        // Load destination location stock
        $.post(admin_url + 'pos_inventory/get_product_stock', {
            product_id: productId,
            location_id: toLocation
        }, function(data) {
            if (data.success) {
                $('#dest-total-stock').text(data.stock.quantity);
                $('#dest-available-stock').text(data.stock.available);
                $('#dest-reserved-stock').text(data.stock.reserved);
                $('#dest-stock-value').text(data.stock.value);
            }
        }, 'json');
        
        $('#stock-info').show();
    } else {
        $('#stock-info').hide();
    }
}

function updateTransferPreview() {
    var productId = $('#product_id').val();
    var fromLocation = $('#from_location').val();
    var toLocation = $('#to_location').val();
    var quantity = parseInt($('#transfer_quantity').val()) || 0;
    
    if (productId && fromLocation && toLocation && quantity > 0) {
        var fromLocationName = $('#from_location option:selected').text();
        var toLocationName = $('#to_location option:selected').text();
        var fromCurrent = parseInt($('#source-total-stock').text()) || 0;
        var toCurrent = parseInt($('#dest-total-stock').text()) || 0;
        
        $('#preview-from-location').text(fromLocationName);
        $('#preview-to-location').text(toLocationName);
        $('#preview-from-current').text(fromCurrent);
        $('#preview-to-current').text(toCurrent);
        $('#preview-from-after').text(fromCurrent - quantity);
        $('#preview-to-after').text(toCurrent + quantity);
        
        $('#transfer-preview').show();
    } else {
        $('#transfer-preview').hide();
    }
}

function validateTransferQuantity() {
    var quantity = parseInt($('#transfer_quantity').val()) || 0;
    var available = parseInt($('#source-available-stock').text()) || 0;
    var $input = $('#transfer_quantity');
    
    if (quantity > available) {
        $input.addClass('has-error');
        $input.next('.text-muted').addClass('text-danger').text('<?php echo _l('insufficient_stock'); ?>');
        return false;
    } else {
        $input.removeClass('has-error');
        $input.next('.text-muted').removeClass('text-danger').text('<?php echo _l('max_available'); ?>: ' + available);
        return true;
    }
}

function loadRecentTransfers() {
    $('#recent-transfers-table').DataTable({
        processing: true,
        serverSide: true,
        ajax: {
            url: admin_url + 'pos_inventory/recent_transfers_table',
            type: 'POST'
        },
        columns: [
            { data: 'created_at' },
            { data: 'product_name' },
            { data: 'from_location' },
            { data: 'to_location' },
            { data: 'quantity' },
            { data: 'notes' },
            { data: 'user_name' }
        ],
        order: [[0, 'desc']],
        pageLength: 10,
        responsive: true
    });
}

// Form validation
$('#stock-transfer-form').submit(function(e) {
    if (!validateTransferQuantity()) {
        e.preventDefault();
        alert('<?php echo _l('please_check_transfer_quantity'); ?>');
        return false;
    }
});
</script>

<?php init_tail(); ?>
