<?php defined('BASEPATH') or exit('No direct script access allowed'); ?>
<?php init_head(); ?>
<div id="wrapper">
    <div class="content">
        <div class="row">
            <div class="col-md-12">
                <div class="panel_s">
                    <div class="panel-body">
                        <div class="row">
                            <div class="col-md-8">
                                <h4 class="no-margin">
                                    <?php echo _l('inventory_management'); ?>
                                </h4>
                            </div>
                            <div class="col-md-4 text-right">
                                <?php if (has_permission('pos_inventory', '', 'edit')) { ?>
                                    <a href="<?php echo admin_url('pos_inventory/stock_adjustment'); ?>" class="btn btn-primary">
                                        <i class="fa fa-plus-minus"></i> <?php echo _l('stock_adjustment'); ?>
                                    </a>
                                    <a href="<?php echo admin_url('pos_inventory/stock_transfer'); ?>" class="btn btn-info">
                                        <i class="fa fa-exchange"></i> <?php echo _l('stock_transfer'); ?>
                                    </a>
                                    <div class="btn-group">
                                        <button type="button" class="btn btn-success dropdown-toggle" data-toggle="dropdown">
                                            <i class="fa fa-cog"></i> <?php echo _l('actions'); ?> <span class="caret"></span>
                                        </button>
                                        <ul class="dropdown-menu dropdown-menu-right">
                                            <li><a href="<?php echo admin_url('pos_inventory/locations'); ?>"><i class="fa fa-map-marker"></i> <?php echo _l('manage_locations'); ?></a></li>
                                            <li><a href="<?php echo admin_url('pos_inventory/stock_movements'); ?>"><i class="fa fa-history"></i> <?php echo _l('stock_movements'); ?></a></li>
                                            <li><a href="#" data-toggle="modal" data-target="#bulkAdjustmentModal"><i class="fa fa-cubes"></i> <?php echo _l('bulk_adjustment'); ?></a></li>
                                        </ul>
                                    </div>
                                <?php } ?>
                            </div>
                        </div>
                        <hr class="hr-panel-heading" />
                        
                        <!-- Inventory Summary Cards -->
                        <div class="row">
                            <div class="col-md-3">
                                <div class="stat-card info">
                                    <div class="stat-value" id="total-products">0</div>
                                    <div class="stat-label"><?php echo _l('total_products'); ?></div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="stat-card success">
                                    <div class="stat-value" id="total-stock-value">$0.00</div>
                                    <div class="stat-label"><?php echo _l('total_stock_value'); ?></div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="stat-card warning">
                                    <div class="stat-value" id="low-stock-count">0</div>
                                    <div class="stat-label"><?php echo _l('low_stock_products'); ?></div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="stat-card danger">
                                    <div class="stat-value" id="out-of-stock-count">0</div>
                                    <div class="stat-label"><?php echo _l('out_of_stock'); ?></div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Filters -->
                        <div class="row" style="margin-top: 20px;">
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label for="filter_location"><?php echo _l('location'); ?></label>
                                    <select class="form-control selectpicker" id="filter_location" data-live-search="true">
                                        <option value=""><?php echo _l('all_locations'); ?></option>
                                        <?php foreach ($locations as $location) { ?>
                                            <option value="<?php echo $location['id']; ?>"><?php echo $location['name']; ?></option>
                                        <?php } ?>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label for="filter_category"><?php echo _l('category'); ?></label>
                                    <select class="form-control selectpicker" id="filter_category" data-live-search="true">
                                        <option value=""><?php echo _l('all_categories'); ?></option>
                                        <!-- Categories will be loaded via AJAX -->
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label for="filter_stock_status"><?php echo _l('stock_status'); ?></label>
                                    <select class="form-control selectpicker" id="filter_stock_status">
                                        <option value=""><?php echo _l('all'); ?></option>
                                        <option value="in_stock"><?php echo _l('in_stock'); ?></option>
                                        <option value="low_stock"><?php echo _l('low_stock'); ?></option>
                                        <option value="out_of_stock"><?php echo _l('out_of_stock'); ?></option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label>&nbsp;</label>
                                    <div>
                                        <button type="button" class="btn btn-default btn-block" id="clear_filters">
                                            <i class="fa fa-refresh"></i> <?php echo _l('clear_filters'); ?>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Inventory Table -->
                        <div class="table-responsive">
                            <table class="table table-striped pos-table" id="inventory-table">
                                <thead>
                                    <tr>
                                        <th><input type="checkbox" id="select-all"></th>
                                        <th><?php echo _l('product'); ?></th>
                                        <th><?php echo _l('sku'); ?></th>
                                        <th><?php echo _l('location'); ?></th>
                                        <th><?php echo _l('quantity'); ?></th>
                                        <th><?php echo _l('reserved'); ?></th>
                                        <th><?php echo _l('available'); ?></th>
                                        <th><?php echo _l('value'); ?></th>
                                        <th><?php echo _l('status'); ?></th>
                                        <th><?php echo _l('last_updated'); ?></th>
                                        <th><?php echo _l('actions'); ?></th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <!-- Data will be loaded via DataTables AJAX -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Low Stock Alerts -->
        <div class="row">
            <div class="col-md-12">
                <div class="panel_s">
                    <div class="panel-body">
                        <h5 class="text-warning">
                            <i class="fa fa-exclamation-triangle"></i> <?php echo _l('low_stock_alerts'); ?>
                        </h5>
                        <div class="table-responsive">
                            <table class="table table-striped" id="low-stock-table">
                                <thead>
                                    <tr>
                                        <th><?php echo _l('product'); ?></th>
                                        <th><?php echo _l('location'); ?></th>
                                        <th><?php echo _l('current_stock'); ?></th>
                                        <th><?php echo _l('threshold'); ?></th>
                                        <th><?php echo _l('actions'); ?></th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <!-- Low stock data will be loaded via AJAX -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Bulk Adjustment Modal -->
<div class="modal fade" id="bulkAdjustmentModal" tabindex="-1" role="dialog">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
                <h4 class="modal-title"><?php echo _l('bulk_stock_adjustment'); ?></h4>
            </div>
            <form id="bulk-adjustment-form">
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="bulk_location"><?php echo _l('location'); ?></label>
                                <select class="form-control selectpicker" id="bulk_location" name="location_id" required>
                                    <?php foreach ($locations as $location) { ?>
                                        <option value="<?php echo $location['id']; ?>"><?php echo $location['name']; ?></option>
                                    <?php } ?>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="bulk_adjustment_type"><?php echo _l('adjustment_type'); ?></label>
                                <select class="form-control" id="bulk_adjustment_type" name="adjustment_type" required>
                                    <option value="increase"><?php echo _l('increase'); ?></option>
                                    <option value="decrease"><?php echo _l('decrease'); ?></option>
                                    <option value="set"><?php echo _l('set_quantity'); ?></option>
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="bulk_quantity"><?php echo _l('quantity'); ?></label>
                                <input type="number" class="form-control" id="bulk_quantity" name="quantity" required min="0">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="bulk_reason"><?php echo _l('reason'); ?></label>
                                <input type="text" class="form-control" id="bulk_reason" name="reason" required>
                            </div>
                        </div>
                    </div>
                    <div class="form-group">
                        <label for="bulk_notes"><?php echo _l('notes'); ?></label>
                        <textarea class="form-control" id="bulk_notes" name="notes" rows="3"></textarea>
                    </div>
                    <div class="alert alert-info">
                        <span id="selected-products-count">0</span> <?php echo _l('products_selected_for_adjustment'); ?>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal"><?php echo _l('close'); ?></button>
                    <button type="submit" class="btn btn-primary"><?php echo _l('apply_adjustment'); ?></button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Quick Adjustment Modal -->
<div class="modal fade" id="quickAdjustmentModal" tabindex="-1" role="dialog">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
                <h4 class="modal-title"><?php echo _l('quick_stock_adjustment'); ?></h4>
            </div>
            <form id="quick-adjustment-form">
                <input type="hidden" id="quick_product_id" name="product_id">
                <input type="hidden" id="quick_location_id" name="location_id">
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-12">
                            <div class="alert alert-info">
                                <strong id="quick_product_name"></strong><br>
                                <small>Location: <span id="quick_location_name"></span></small><br>
                                <small>Current Stock: <span id="quick_current_stock"></span></small>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="quick_adjustment_type"><?php echo _l('adjustment_type'); ?></label>
                                <select class="form-control" id="quick_adjustment_type" name="adjustment_type" required>
                                    <option value="increase"><?php echo _l('increase'); ?></option>
                                    <option value="decrease"><?php echo _l('decrease'); ?></option>
                                    <option value="set"><?php echo _l('set_quantity'); ?></option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="quick_quantity"><?php echo _l('quantity'); ?></label>
                                <input type="number" class="form-control" id="quick_quantity" name="quantity" required min="0">
                            </div>
                        </div>
                    </div>
                    <div class="form-group">
                        <label for="quick_reason"><?php echo _l('reason'); ?></label>
                        <input type="text" class="form-control" id="quick_reason" name="reason" required>
                    </div>
                    <div class="form-group">
                        <label for="quick_notes"><?php echo _l('notes'); ?></label>
                        <textarea class="form-control" id="quick_notes" name="notes" rows="2"></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal"><?php echo _l('close'); ?></button>
                    <button type="submit" class="btn btn-primary"><?php echo _l('adjust_stock'); ?></button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
$(document).ready(function() {
    // Initialize inventory management
    Inventory.init();
    
    // Load summary statistics
    loadInventorySummary();
    
    // Load categories for filter
    loadCategories();
});

function loadInventorySummary() {
    $.get(admin_url + 'pos_inventory/inventory_summary', function(data) {
        $('#total-products').text(data.total_products);
        $('#total-stock-value').text(data.total_stock_value);
        $('#low-stock-count').text(data.low_stock_count);
        $('#out-of-stock-count').text(data.out_of_stock_count);
    });
}

function loadCategories() {
    $.get(admin_url + 'pos_inventory/get_categories', function(data) {
        var options = '<option value="">' + lang.all_categories + '</option>';
        $.each(data, function(i, category) {
            options += '<option value="' + category.id + '">' + category.name + '</option>';
        });
        $('#filter_category').html(options);
        $('.selectpicker').selectpicker('refresh');
    });
}
</script>

<?php init_tail(); ?>
