<?php defined('BASEPATH') or exit('No direct script access allowed'); ?>
<?php init_head(); ?>
<div id="wrapper">
    <div class="content">
        <div class="row">
            <div class="col-md-12">
                <div class="panel_s">
                    <div class="panel-body">
                        <div class="row">
                            <div class="col-md-8">
                                <h4 class="no-margin">
                                    <?php echo _l('products_management'); ?>
                                </h4>
                            </div>
                            <div class="col-md-4 text-right">
                                <?php if (has_permission('pos_products', '', 'create')) { ?>
                                    <a href="<?php echo admin_url('pos_inventory/product'); ?>" class="btn btn-primary">
                                        <i class="fa fa-plus"></i> <?php echo _l('add_new_product'); ?>
                                    </a>
                                    <a href="#" class="btn btn-info" data-toggle="modal" data-target="#importProductsModal">
                                        <i class="fa fa-upload"></i> <?php echo _l('import_products'); ?>
                                    </a>
                                    <a href="#" class="btn btn-success" data-toggle="modal" data-target="#categoryModal">
                                        <i class="fa fa-tags"></i> <?php echo _l('manage_categories'); ?>
                                    </a>
                                <?php } ?>
                            </div>
                        </div>
                        <hr class="hr-panel-heading" />
                        
                        <!-- Filters -->
                        <div class="row">
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label for="filter_category"><?php echo _l('category'); ?></label>
                                    <select class="form-control selectpicker" id="filter_category" data-live-search="true">
                                        <option value=""><?php echo _l('all_categories'); ?></option>
                                        <!-- Categories will be loaded via AJAX -->
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label for="filter_status"><?php echo _l('status'); ?></label>
                                    <select class="form-control selectpicker" id="filter_status">
                                        <option value=""><?php echo _l('all_statuses'); ?></option>
                                        <option value="1"><?php echo _l('active'); ?></option>
                                        <option value="0"><?php echo _l('inactive'); ?></option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label for="filter_stock"><?php echo _l('stock_status'); ?></label>
                                    <select class="form-control selectpicker" id="filter_stock">
                                        <option value=""><?php echo _l('all'); ?></option>
                                        <option value="in_stock"><?php echo _l('in_stock'); ?></option>
                                        <option value="low_stock"><?php echo _l('low_stock'); ?></option>
                                        <option value="out_of_stock"><?php echo _l('out_of_stock'); ?></option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label>&nbsp;</label>
                                    <div>
                                        <button type="button" class="btn btn-default btn-block" id="clear_filters">
                                            <i class="fa fa-refresh"></i> <?php echo _l('clear_filters'); ?>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Products Table -->
                        <div class="table-responsive">
                            <table class="table table-striped pos-table" id="products-table">
                                <thead>
                                    <tr>
                                        <th><?php echo _l('product_image'); ?></th>
                                        <th><?php echo _l('product_name'); ?></th>
                                        <th><?php echo _l('sku'); ?></th>
                                        <th><?php echo _l('category'); ?></th>
                                        <th><?php echo _l('sale_price'); ?></th>
                                        <th><?php echo _l('cost_price'); ?></th>
                                        <th><?php echo _l('stock_quantity'); ?></th>
                                        <th><?php echo _l('status'); ?></th>
                                        <th><?php echo _l('actions'); ?></th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <!-- Data will be loaded via DataTables AJAX -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Import Products Modal -->
<div class="modal fade" id="importProductsModal" tabindex="-1" role="dialog">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
                <h4 class="modal-title"><?php echo _l('import_products'); ?></h4>
            </div>
            <form id="import-products-form" enctype="multipart/form-data">
                <div class="modal-body">
                    <div class="form-group">
                        <label for="import_file"><?php echo _l('select_csv_file'); ?></label>
                        <input type="file" class="form-control" id="import_file" name="import_file" accept=".csv" required>
                        <small class="text-muted"><?php echo _l('csv_import_help'); ?></small>
                    </div>
                    <div class="form-group">
                        <div class="checkbox">
                            <label>
                                <input type="checkbox" name="update_existing" value="1">
                                <?php echo _l('update_existing_products'); ?>
                            </label>
                        </div>
                    </div>
                    <div class="form-group">
                        <a href="<?php echo admin_url('pos_inventory/download_sample_csv'); ?>" class="btn btn-info btn-sm">
                            <i class="fa fa-download"></i> <?php echo _l('download_sample_csv'); ?>
                        </a>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal"><?php echo _l('close'); ?></button>
                    <button type="submit" class="btn btn-primary"><?php echo _l('import'); ?></button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Category Management Modal -->
<div class="modal fade" id="categoryModal" tabindex="-1" role="dialog">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
                <h4 class="modal-title"><?php echo _l('manage_categories'); ?></h4>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-md-5">
                        <h5><?php echo _l('add_category'); ?></h5>
                        <form id="category-form">
                            <input type="hidden" id="category_id" name="category_id">
                            <div class="form-group">
                                <label for="category_name"><?php echo _l('category_name'); ?></label>
                                <input type="text" class="form-control" id="category_name" name="name" required>
                            </div>
                            <div class="form-group">
                                <label for="category_description"><?php echo _l('category_description'); ?></label>
                                <textarea class="form-control" id="category_description" name="description" rows="3"></textarea>
                            </div>
                            <div class="form-group">
                                <label for="parent_category"><?php echo _l('parent_category'); ?></label>
                                <select class="form-control selectpicker" id="parent_category" name="parent_id" data-live-search="true">
                                    <option value=""><?php echo _l('none'); ?></option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label for="category_sort_order"><?php echo _l('sort_order'); ?></label>
                                <input type="number" class="form-control" id="category_sort_order" name="sort_order" value="0">
                            </div>
                            <button type="submit" class="btn btn-primary" id="save-category-btn">
                                <?php echo _l('save'); ?>
                            </button>
                            <button type="button" class="btn btn-default" id="cancel-category-btn" style="display: none;">
                                <?php echo _l('cancel'); ?>
                            </button>
                        </form>
                    </div>
                    <div class="col-md-7">
                        <h5><?php echo _l('categories'); ?></h5>
                        <div class="table-responsive">
                            <table class="table table-striped" id="categories-table">
                                <thead>
                                    <tr>
                                        <th><?php echo _l('name'); ?></th>
                                        <th><?php echo _l('parent'); ?></th>
                                        <th><?php echo _l('products_count'); ?></th>
                                        <th><?php echo _l('actions'); ?></th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <!-- Categories will be loaded via AJAX -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-default" data-dismiss="modal"><?php echo _l('close'); ?></button>
            </div>
        </div>
    </div>
</div>

<!-- Bulk Actions Modal -->
<div class="modal fade" id="bulkActionsModal" tabindex="-1" role="dialog">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
                <h4 class="modal-title"><?php echo _l('bulk_actions'); ?></h4>
            </div>
            <form id="bulk-actions-form">
                <div class="modal-body">
                    <div class="form-group">
                        <label for="bulk_action"><?php echo _l('select_action'); ?></label>
                        <select class="form-control" id="bulk_action" name="action" required>
                            <option value=""><?php echo _l('select_action'); ?></option>
                            <option value="activate"><?php echo _l('activate'); ?></option>
                            <option value="deactivate"><?php echo _l('deactivate'); ?></option>
                            <option value="delete"><?php echo _l('delete'); ?></option>
                            <option value="update_category"><?php echo _l('update_category'); ?></option>
                            <option value="update_price"><?php echo _l('update_price'); ?></option>
                        </select>
                    </div>
                    <div class="form-group" id="bulk_category_group" style="display: none;">
                        <label for="bulk_category"><?php echo _l('category'); ?></label>
                        <select class="form-control selectpicker" id="bulk_category" name="category_id" data-live-search="true">
                            <option value=""><?php echo _l('select_category'); ?></option>
                        </select>
                    </div>
                    <div class="form-group" id="bulk_price_group" style="display: none;">
                        <label for="bulk_price_type"><?php echo _l('price_update_type'); ?></label>
                        <select class="form-control" id="bulk_price_type" name="price_type">
                            <option value="percentage"><?php echo _l('percentage'); ?></option>
                            <option value="fixed"><?php echo _l('fixed_amount'); ?></option>
                        </select>
                        <div class="row" style="margin-top: 10px;">
                            <div class="col-md-6">
                                <input type="number" class="form-control" id="bulk_price_value" name="price_value" placeholder="<?php echo _l('value'); ?>" step="0.01">
                            </div>
                            <div class="col-md-6">
                                <select class="form-control" id="bulk_price_operation" name="price_operation">
                                    <option value="increase"><?php echo _l('increase'); ?></option>
                                    <option value="decrease"><?php echo _l('decrease'); ?></option>
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="alert alert-info">
                        <span id="selected-count">0</span> <?php echo _l('products_selected'); ?>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal"><?php echo _l('close'); ?></button>
                    <button type="submit" class="btn btn-primary"><?php echo _l('apply'); ?></button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
// Ensure jQuery is available
if (typeof $ === 'undefined' && typeof jQuery !== 'undefined') {
    var $ = jQuery;
}

// Check if jQuery is loaded
if (typeof $ === 'undefined') {
    console.error('jQuery is not loaded on products manage page');
    // Try to wait for jQuery to load
    var checkJQuery = setInterval(function() {
        if (typeof $ !== 'undefined' || typeof jQuery !== 'undefined') {
            if (typeof $ === 'undefined' && typeof jQuery !== 'undefined') {
                var $ = jQuery;
            }
            clearInterval(checkJQuery);
            initProductsPage();
        }
    }, 100);
} else {
    $(document).ready(function() {
        initProductsPage();
    });
}

function initProductsPage() {
    // Check if DataTable is available
    if (typeof $.fn.DataTable === 'undefined') {
        console.error('DataTables is not loaded');
        return;
    }

    // Check if admin_url is defined
    var baseUrl = (typeof admin_url !== 'undefined') ? admin_url : '/admin/';

    // Initialize products DataTable
    var productsTable = $('#products-table').DataTable({
        processing: true,
        serverSide: true,
        ajax: {
            url: baseUrl + 'pos_inventory/products_table',
            type: 'POST',
            data: function(d) {
                d.category = $('#filter_category').val();
                d.status = $('#filter_status').val();
                d.stock = $('#filter_stock').val();
            },
            error: function(xhr, error, thrown) {
                console.error('DataTables AJAX error:', error, thrown);
            }
        },
        columns: [
            { data: 'image', orderable: false, searchable: false },
            { data: 'name' },
            { data: 'sku' },
            { data: 'category_name' },
            { data: 'sale_price' },
            { data: 'cost_price' },
            { data: 'stock_quantity' },
            { data: 'status', orderable: false },
            { data: 'actions', orderable: false, searchable: false }
        ],
        order: [[1, 'asc']],
        responsive: true,
        select: {
            style: 'multi',
            selector: 'td:first-child'
        }
    });

    // Filter change handlers
    $('#filter_category, #filter_status, #filter_stock').change(function() {
        productsTable.ajax.reload();
    });

    // Clear filters
    $('#clear_filters').click(function() {
        $('#filter_category, #filter_status, #filter_stock').val('').trigger('change');
        $('.selectpicker').selectpicker('refresh');
        productsTable.ajax.reload();
    });

    // Load categories for filters
    loadCategories();
}

function loadCategories() {
    // Ensure jQuery is available
    if (typeof $ === 'undefined') {
        console.error('jQuery not available for loadCategories');
        return;
    }

    // Check if admin_url is defined
    var baseUrl = (typeof admin_url !== 'undefined') ? admin_url : '/admin/';

    $.get(baseUrl + 'pos_inventory/get_categories', function(data) {
        var options = '<option value="">' + (typeof lang !== 'undefined' && lang.all_categories ? lang.all_categories : 'All Categories') + '</option>';
        $.each(data, function(i, category) {
            options += '<option value="' + category.id + '">' + category.name + '</option>';
        });
        $('#filter_category, #bulk_category, #parent_category').html(options);

        // Refresh selectpicker if available
        if (typeof $.fn.selectpicker === 'function') {
            $('.selectpicker').selectpicker('refresh');
        }
    }).fail(function() {
        console.error('Failed to load categories');
    });
}
</script>

<?php init_tail(); ?>
